const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { authenticateJWT } = require('../middleware/authMiddleware');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY); // Uncommented for AM.4

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// GET /me/subscriptions - Fetches all subscriptions for the authenticated user
router.get('/subscriptions', async (req, res) => {
    const userId = req.user.id;

    if (!userId) {
        return sendError(res, 401, 'User not authenticated.');
    }

    try {
        const { data: subscriptions, error } = await supabase
            .from('subscriptions') // Assuming your table is named 'subscriptions'
            .select('*') // Select all columns for now, adjust as needed
            .eq('user_id', userId);

        if (error) {
            console.error('Error fetching subscriptions for user:', error);
            return sendError(res, 500, 'Failed to fetch subscriptions.');
        }

        if (!subscriptions || subscriptions.length === 0) {
            return sendSuccess(res, [], 'No subscriptions found for this user.');
        }

        sendSuccess(res, subscriptions);
    } catch (err) {
        console.error('Unexpected error fetching subscriptions:', err);
        sendError(res, 500, 'An unexpected error occurred while fetching subscriptions.');
    }
});

// POST /me/billing/create-customer-portal-session - Creates a Stripe billing portal session
router.post('/billing/create-customer-portal-session', async (req, res) => {
    const userId = req.user.id;
    const { return_url } = req.body;

    if (!userId) {
        return sendError(res, 401, 'User not authenticated.');
    }

    if (!return_url) {
        return sendError(res, 400, 'return_url is required.');
    }

    try {
        // Fetch the user's profile to get their Stripe customer ID
        const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('stripe_customer_id')
            .eq('id', userId)
            .single();

        if (profileError || !profile || !profile.stripe_customer_id) {
            console.error('Error fetching profile or Stripe customer ID not found:', profileError);
            return sendError(res, 404, 'Stripe customer ID not found for this user. Please ensure you have an active subscription.');
        }

        const stripeCustomerId = profile.stripe_customer_id;

        // Create a Stripe billing portal session
        const session = await stripe.billingPortal.sessions.create({
            customer: stripeCustomerId,
            return_url: return_url,
        });

        sendSuccess(res, { url: session.url }, 'Stripe billing portal session created.');

    } catch (err) {
        console.error('Error creating Stripe billing portal session:', err);
        // Differentiate Stripe errors from other errors if possible
        if (err.type && err.type.startsWith('Stripe')) {
            return sendError(res, err.statusCode || 500, err.message || 'A Stripe error occurred.');
        }
        sendError(res, 500, 'An unexpected error occurred while creating the billing portal session.');
    }
});

module.exports = router;
