# plan.md - Detailed Project Execution Plan (Multi-Project Workspace)

**Purpose:** Contains the detailed, actionable breakdown of tasks and subtasks, clearly indicating which subproject each task belongs to.

---

## 1. Planning Summary & Context

<!-- #PlanningContext -->
*   **Related PRD/Specification:** [Link/Reference]
*   **Primary Goal(s) Addressed:** [Reference `projectbrief.md`]
*   **Planning Date:** [Date]
*   **Overall Status:** [e.g., Planning Complete, Executing Frontend Tasks]

<!-- #PlanningAssumptions -->
**Key Assumptions Made During Planning:**
*   [Assumption 1]
*   [Assumption 2]

<!-- #OpenPlanningQuestions -->
**Open Questions/Risks Identified:**
*   [Question 1]
*   [Risk 1]

---

## 2. Task Breakdown & Status

<!--
Instructions for Dane:
- Use standard Markdown task list format: `- [ ] Task Name` / `- [x] Task Name`.
- Use indentation for subtasks.
- **CRITICAL:** Prefix **every** task and subtask name with the relevant subproject identifier in brackets, e.g., `[FE]`, `[BE]`, `[VSTO]`, `[Shared]`, `[Cross-Project]`.
- Mark the **single, specific task/subtask currently being worked on** with `**>>> CURRENT FOCUS >>>**`.
- Optionally add details like (Est: 2h), (Depends: TaskID/SubtaskID), (Issue: Issue-XXX), (File: subproject/path/file.ext).
- Update checkboxes and the `>>> CURRENT FOCUS >>>` marker IMMEDIATELY after completing a task/subtask. Update the `last_updated` YAML field.
-->

<!-- #Tasks: Phase/Feature 1 Name -->
### Phase/Feature: [Name, e.g., User Authentication Implementation]

*   [ ] **[BE] Task 1.1:** Define User Schema in DB (Est: 1h)
    *   [ ] [BE] Subtask 1.1.1: Define `users` table.
    *   [ ] [BE] Subtask 1.1.2: Define indexes.
*   [ ] **[BE] Task 1.2:** Implement Backend Registration API (Est: 3h) (File: `backend/src/routes/auth.ts`)
    *   [ ] [BE] Subtask 1.2.1: Create `POST /api/register` route.
    *   [ ] [BE] Subtask 1.2.2: Add input validation.
    *   [ ] [BE] Subtask 1.2.3: Implement password hashing. (Depends: 1.1)
    *   [ ] [BE] Subtask 1.2.4: Save user to DB. (Depends: 1.2.3)
*   [ ] **[FE] Task 1.3:** Implement Frontend Registration Form (Est: 4h) (File: `frontend/src/pages/Register.tsx`)
    *   [ ] [FE] Subtask 1.3.1: Create UI form components.
    *   [ ] [FE] Subtask 1.3.2: Add client-side validation.
    *   [ ] [FE] Subtask 1.3.3: Implement API call to `POST /api/register`. (Depends: 1.2)
    *   [ ] [FE] Subtask 1.3.4: Handle success/error responses.
*   [ ] **[BE] Task 1.4:** Implement Backend Login API (Est: 2h)
    *   [ ] **>>> CURRENT FOCUS >>>** [BE] Subtask 1.4.1: Create `POST /api/login` route.
    *   [ ] [BE] Subtask 1.4.2: Validate input.
    *   [ ] [BE] Subtask 1.4.3: Verify credentials against DB.
    *   [ ] [BE] Subtask 1.4.4: Generate/return JWT token.
*   [ ] **[FE] Task 1.5:** Implement Frontend Login Form (Est: 3h) (Depends: 1.4)

<!-- #Tasks: Phase/Feature 2 Name -->
### Phase/Feature: [Name, e.g., VSTO Data Display]

*   [ ] **[VSTO] Task 2.1:** Create Task Pane UI (Est: 2h)
*   [ ] **[VSTO] Task 2.2:** Implement API call to fetch data from `[Backend]` (Est: 3h) (Depends: [Relevant Backend Task ID])

---

## 3. Future Considerations / Parking Lot

<!-- #ParkingLot -->
*   `[Scope]` [Idea/Task deferred]

---