# QuantBoost Infrastructure Deployment Script
# Run this script from the QuantBoost_API/infrastructure directory

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("staging", "prod")]
    [string]$Environment = "staging",
    
    [Parameter(Mandatory=$false)]
    [switch]$PlanOnly,
    
    [Parameter(Mandatory=$false)]
    [switch]$DestroyInfrastructure
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 QuantBoost Infrastructure Deployment" -ForegroundColor Green
Write-Host "Environment: $Environment" -ForegroundColor Yellow

# Check if terraform.tfvars exists
if (-not (Test-Path "terraform.tfvars")) {
    Write-Host "❌ terraform.tfvars not found!" -ForegroundColor Red
    Write-Host "Please copy terraform.tfvars.example to terraform.tfvars and fill in your values" -ForegroundColor Yellow
    exit 1
}

# Check if Azure CLI is logged in
try {
    $azAccount = az account show --query "name" -o tsv 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Not logged into Azure CLI" -ForegroundColor Red
        Write-Host "Please run: az login" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "✅ Azure CLI logged in as: $azAccount" -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI not found or not logged in" -ForegroundColor Red
    exit 1
}

# Initialize Terraform
Write-Host "📦 Initializing Terraform..." -ForegroundColor Blue
terraform init
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Terraform init failed" -ForegroundColor Red
    exit 1
}

# Create Terraform plan
Write-Host "📋 Creating Terraform plan..." -ForegroundColor Blue
$planArgs = @(
    "plan"
    "-var=environment=$Environment"
    "-out=tfplan"
)

terraform @planArgs
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Terraform plan failed" -ForegroundColor Red
    exit 1
}

if ($PlanOnly) {
    Write-Host "✅ Plan completed. Review the plan above." -ForegroundColor Green
    Write-Host "To apply: terraform apply tfplan" -ForegroundColor Yellow
    exit 0
}

if ($DestroyInfrastructure) {
    Write-Host "⚠️  DESTROYING INFRASTRUCTURE" -ForegroundColor Red
    $confirmation = Read-Host "Are you sure you want to destroy all infrastructure? Type 'yes' to confirm"
    if ($confirmation -eq "yes") {
        terraform destroy -var="environment=$Environment" -auto-approve
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Infrastructure destroyed successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Infrastructure destruction failed" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "❌ Destruction cancelled" -ForegroundColor Yellow
    }
    exit 0
}

# Apply Terraform plan
Write-Host "🚀 Applying Terraform plan..." -ForegroundColor Blue
terraform apply -auto-approve tfplan
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Terraform apply failed" -ForegroundColor Red
    exit 1
}

# Get outputs
Write-Host "📊 Getting deployment outputs..." -ForegroundColor Blue
$staticWebAppName = terraform output -raw static_web_app_default_hostname
$containerAppFqdn = terraform output -raw container_app_fqdn
$resourceGroupName = terraform output -raw resource_group_name

Write-Host "✅ Infrastructure deployed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Deployment Summary:" -ForegroundColor Cyan
Write-Host "  Environment: $Environment" -ForegroundColor White
Write-Host "  Resource Group: $resourceGroupName" -ForegroundColor White
Write-Host "  Frontend URL: https://$staticWebAppName" -ForegroundColor White
Write-Host "  Backend API URL: https://$containerAppFqdn" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Deploy your frontend code to the Static Web App" -ForegroundColor White
Write-Host "  2. Build and push your API container to Azure Container Registry" -ForegroundColor White
Write-Host "  3. Configure your custom domain (quantboost.ai) to point to the Static Web App" -ForegroundColor White
Write-Host "  4. Update Stripe webhook URL to: https://$staticWebAppName/api/webhooks/stripe" -ForegroundColor White
