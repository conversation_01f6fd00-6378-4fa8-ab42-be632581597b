---
title: Known Issues and TODOs for PowerPoint Add-in
purpose: Lists potential bugs, areas needing fixes, and explicit TODOs from code comments for the QuantBoost PowerPoint Add-in.
projects: ["powerpoint-add-in"]
source_analysis: "Codebase analysis of QuantBoost_PPTX (powerpoint-add-in), including UI components, Utilities, Properties, and ThisAddIn.cs." # Updated source_analysis
status: bootstrapped-incomplete
last_updated: 2025-05-28T00:00:00Z # Updated timestamp
tags: ["powerpoint-add-in", "issues", "bugs", "todos", "technical_debt", "ui", "utilities", "core"] # Added tags
---

## Recent Issues

### Issue-EXL003: Excel Application Closure in Excel Link
**Status:** Resolved  
**Subproject:** PowerPoint Add-in  
**Date Found:** 2025-05-28  
**Date Resolved:** 2025-05-28  

**Description:**  
When using Excel Link feature, the user's Excel application would close unexpectedly after pressing the Excel Link button. This was causing disruption to the user's workflow as they would lose their Excel data.

**Root Cause:**  
The `ExcelComWrapper.Dispose()` method was calling `_workbook.Close()` even when the wrapper had attached to an existing Excel instance (`_ownsExcelApp = false`). This would close the user's open workbook, and if it was the only workbook open, Excel would close entirely.

**Fix Applied:**  
Modified the disposal logic in `ExcelComWrapper.cs` to only close the workbook when the wrapper owns the Excel instance:
```csharp
// Only close the workbook if we own the Excel Application
// If we attached to an existing instance, don't close the user's workbook
if (_ownsExcelApp)
{
    try { _workbook.Close(SaveChanges: false); } // Close without saving
    catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Ignoring error during Workbook.Close: {ex.Message}"); }
}
```

### Issue-EXL004: FileNotFoundException for Unsaved Presentations
**Status:** Resolved  
**Subproject:** PowerPoint Add-in  
**Date Found:** 2025-05-28  
**Date Resolved:** 2025-05-28  

**Description:**  
Excel Link metadata operations were failing with `FileNotFoundException` when trying to work with unsaved PowerPoint presentations ("Presentation1"). The error occurred because unsaved presentations don't have a `FullName` property.

**Root Cause:**  
Three methods in `ExcelLinkService.cs` were accessing `Globals.ThisAddIn.Application.ActivePresentation.FullName` without checking if the presentation was saved:
- `EmbedExcelContentAsync()` (line 131)
- `RemoveExcelLinkAsync()` (line 411) 
- `UpdateLinkMetadataAsync()` (line 482)

**Fix Applied:**  
Added validation to check if presentation is saved before attempting metadata operations:
```csharp
var activePresentation = Globals.ThisAddIn.Application.ActivePresentation;
string presentationPath = activePresentation.FullName;

// Check if presentation is saved (has a file path)
if (string.IsNullOrEmpty(presentationPath))
{
    throw new InvalidOperationException("Please save the PowerPoint presentation before creating Excel links. Unsaved presentations cannot store link metadata.");
}
```

### Issue-EXL001: SharePoint/OneDrive URL Handling in Excel Link
**Status:** Resolved  
**Subproject:** PowerPoint Add-in  
**Date Found:** 2025-05-28  
**Date Resolved:** 2025-05-28  

**Description:**  
Excel Link feature was failing when trying to import content from SharePoint or OneDrive URLs. The `GetFileHashAsync()` and `File.GetLastWriteTimeUtc()` methods were attempting to treat remote URLs as local file paths, causing `System.NotSupportedException: The given path's format is not supported.`

**Error Details:**
```
Exception: System.NotSupportedException: The given path's format is not supported.
   at System.Security.Permissions.FileIOPermission.EmulateFileIOPermissionChecks(String fullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, ...)
   at System.IO.File.OpenRead(String path)
   at QuantBoost_Powerpoint_Addin.ExcelLink.ExcelLinkService.<GetFileHashAsync>d__6.MoveNext()
```

**Root Cause:**  
Code assumed all Excel file paths would be local file system paths, but modern Office environments often use SharePoint/OneDrive URLs.

**Resolution:**  
- Modified `GetFileHashAsync()` to detect URLs vs local paths using `Uri.TryCreate()`
- For remote URLs: Generate hash from URL + current date instead of file content
- Added `GetFileLastModifiedUtc()` helper method with same URL detection logic
- For remote URLs: Use current timestamp since actual file modification time isn't easily accessible

**Files Modified:**
- `ExcelLink/ExcelLinkService.cs`: Added URL detection and alternative handling for remote files

**Testing:**  
Ready for re-testing with SharePoint/OneDrive Excel files.

### Issue-EXL005: PowerPoint File Locking in Excel Link Metadata Storage
**Status:** Resolved  
**Subproject:** PowerPoint Add-in  
**Date Found:** 2025-05-28  
**Date Resolved:** 2025-05-28  

**Description:**  
Excel Link metadata storage was failing with `System.IO.IOException: The process cannot access the file 'Excel Link Test.pptx' because it is being used by another process` when trying to store metadata in saved PowerPoint presentations.

**Root Cause:**  
OpenXML SDK's `PresentationDocument.Open()` method cannot access files that are currently open in PowerPoint because the application holds an exclusive file lock. This created a fundamental conflict between the metadata storage system and live PowerPoint sessions.

**Fix Applied:**  
Completely replaced OpenXML-based metadata storage with PowerPoint COM interface approach:

1. **New Methods Added:**
   ```csharp
   private async Task StoreMetadataViaCOMAsync(ExcelLinkMetadata linkMetadata, PowerPoint.Presentation presentation)
   private async Task RemoveMetadataViaCOMAsync(string linkId, PowerPoint.Presentation presentation)  
   private async Task UpdateMetadataViaCOMAsync(ChartLink link, PowerPoint.Presentation presentation)
   private string SerializeToSimpleJson(ExcelLinkMetadata metadata)
   ```

2. **Storage Mechanism:**
   - Uses PowerPoint's `CustomDocumentProperties` collection
   - Stores metadata as JSON strings in custom properties
   - Property names: `QuantBoostLink_{LinkId}`
   - No file access conflicts with live PowerPoint sessions

3. **Updated Methods:**
   - `EmbedExcelContentAsync()` - Now uses COM interface
   - `RemoveExcelLinkAsync()` - Now uses COM interface
   - `UpdateLinkMetadataAsync()` - Now uses COM interface

**Benefits:**
- Eliminates file locking conflicts completely
- Uses PowerPoint's native metadata storage system
- Maintains data integrity with live presentations
- No dependency on external JSON libraries
- Seamless integration with existing PowerPoint workflow

**Technical Impact:**
- Metadata now persists in PowerPoint's document properties
- Compatible with presentation save/load operations
- Survives PowerPoint restarts and file sharing
- Accessible through PowerPoint's standard metadata APIs

---

## Potential Issues and Explicit TODOs

This document lists potential issues inferred from the codebase structure and areas explicitly marked for attention in code comments.

### Potential Issues (Inferred)
*   **COM Interop Robustness:** Applications relying heavily on COM interop (like the `ExcelLink/` feature and UI validation in `EditLinkDialog.cs`) are susceptible to issues.
*   **Performance with Large Files/Complex UI:**
    *   Parsing large presentations (`PptxFileParser.cs`) or linking to large/complex Excel files.
    *   Rendering and updating complex UIs like `LinkManagerPane` with many links, or `AnalyzePane` with many results, could be slow if not optimized. DataGridView performance with large datasets can be a concern.
*   **Error Handling Completeness:** Ensure all critical paths, especially within COM interop, file parsing, and UI event handlers, are covered.
*   **UI Responsiveness:** Long-running operations triggered from UI (e.g., refresh all links, full analysis) should use background threads consistently to keep the UI responsive, with clear progress indication and cancellation capabilities. `AnalyzePane` shows progress, but ensure this is comprehensive.
*   **UI Layout and Resizing:** As noted in `AnalyzePane.cs` (`// For future: test resizing...`), thorough testing of UI elements in different screen resolutions, DPI settings, and window sizes is needed to ensure usability.

### Explicit TODOs, FIXMEs, BUGs from Code Comments
*   **[Dane to Scan Code]:** Systematically scan the codebase for comments like `//TODO:`, `//FIXME:`, `//BUG:`, etc. List them here with their file and a brief description.
    *   `ExcelLinkService.cs` (EmbedExcelContentAsync): `// TODO: Add cases for other LinkTypes (Text)` - Functionality for embedding text-based links from Excel is pending.
    *   `ExcelLinkService.cs` (Various methods): Several `// Stub:` comments indicate placeholder implementations that need to be fully developed:
        *   `GetAllLinksAsync()`
        *   `RefreshChartAsync(ChartLink link)` (currently a `Task.CompletedTask`)
        *   `RefreshAllChartsAsync(IProgress<QuantBoost_Powerpoint_Addin.Analysis.ProgressState> progress)` (relies on the stubbed `GetAllLinksAsync` and `RefreshChartAsync`)
    *   `EditLinkDialog.cs` (InitializeComponent related): `// TODO: Add controls to select worksheet/chart from file if needed` - The dialog may lack an intuitive way to select worksheet/chart from the chosen Excel file, relying on manual text entry.
    *   `EditLinkDialog.cs` (BtnBrowseSourceFile_Click): `// TODO: Optionally clear/update worksheet/chart fields or trigger reload` - Logic after selecting a new source file in the edit dialog might be incomplete.
    *   `AnalyzePane.cs` (Constructor): `// For future: test resizing in various PowerPoint window sizes and refine as needed` - Acknowledges potential UI layout issues with resizing.
    *   `ErrorHandlingService.cs` (`LogException`): `// Basic logging to Debug output. TODO: Implement more robust logging (e.g., to a file).` - The current logging mechanism is not suitable for production.
    *   `ThisAddIn.cs` (`MapToClientDetails`): `// GracePeriodEndsUtc = null; // This would typically be calculated based on rules if needed` - Calculation for grace period end date is not implemented.
    *   `ThisAddIn.cs` (`MapToClientStatus`): `// Consider how to map NotAssigned - maybe InvalidKey or a dedicated UI status?` - Ambiguity in mapping `LicenseStatus.NotAssigned`.

### Potential Issues (Inferred from Batch 3 Analysis)
*   **Early Access to `GlobalCancellationToken`:** The getter for `GlobalCancellationToken` in `ThisAddIn.cs` has a safeguard against being accessed before `ThisAddIn_Startup` is complete. If other parts of the add-in attempt to access it prematurely, it could lead to unexpected behavior (though it returns a cancelled token as a fallback).
*   **Robustness of `ErrorHandlingService.LogException`:** The current implementation writes to `Debug.WriteLine`. This is insufficient for production error tracking. Errors might be missed if a debugger is not attached.
*   **Hardcoded API URL in `ThisAddIn.cs`:** `API_BASE_URL = "http://localhost:3000"` is hardcoded. This will need to be configurable for different environments (development, staging, production).
*   **Thread Safety of `ToastNotifier`:** While `ToastNotifier` uses `AsyncHelper` to show forms on the UI thread, the creation and management of multiple `ToastForm` instances itself (if `ShowToast` is called rapidly or from multiple background threads before a previous toast is closed) should be reviewed for any potential race conditions, though the typical use case might be sequential.

*(This list should be actively maintained and updated as issues are formally logged or TODOs are addressed.)*
