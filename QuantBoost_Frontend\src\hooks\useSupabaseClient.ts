"use client";

import { createBrowserClient } from '@supabase/ssr';
import { useMemo } from 'react';

/**
 * Custom hook to safely create Supabase client for client components
 * This avoids build-time issues by creating the client only when the hook is called
 * and environment variables are available
 */
export function useSupabaseClient() {
  const supabase = useMemo(() => {
    // Check if we're in a browser environment and have the required env vars
    if (typeof window === 'undefined') {
      // During build time or SSR, return a mock client that won't be used
      return createBrowserClient('https://placeholder.supabase.co', 'placeholder-key');
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase environment variables');
      // Return a placeholder client that won't work but won't crash the build
      return createBrowserClient('https://placeholder.supabase.co', 'placeholder-key');
    }

    return createBrowserClient(supabaseUrl, supabaseAnonKey);
  }, []);

  return supabase;
}
