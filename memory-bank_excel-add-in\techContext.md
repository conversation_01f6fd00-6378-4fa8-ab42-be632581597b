---
title: Technical Context for QuantBoost Excel Add-in
purpose: Details the planned technology stack, dependencies, and environment for the QuantBoost Excel Add-in.
projects: ["excel-add-in"]
source_analysis: "Documentation review and project guidelines."
status: bootstrapping
last_updated: 2025-05-13T17:05:00Z # Will be updated by agent
tags: ["excel-add-in", "technical", "dependencies", "environment", "planned"]
---

## Planned Technology Stack and Dependencies for QuantBoost Excel Add-in

Based on the development guides, project guidelines, and technical specifications, the Excel Add-in will utilize the following technology stack:

### Core Technologies
*   **Primary Language:** C# 7.3
*   **Framework:** .NET Framework 4.8.1 (VSTO Add-in)
*   **Project Type:** VSTO (Visual Studio Tools for Office) Excel Add-in

### UI Technologies
*   **Windows Forms:** For custom Task Panes and Dialogs.
*   **Ribbon (Office Fluent UI):** Custom ribbon for Excel, defined via XML and controlled by C#.

### Key Libraries and Dependencies
*   **DocumentFormat.OpenXml SDK v2.18.0:** Will be used by the `Sheet Size Analyzer` for direct analysis of OpenXML Excel files (e.g., `.xlsx`). Usage must adhere strictly to v2.18.0 APIs.
*   **Newtonsoft.Json / System.Text.Json:** Likely for any configuration or internal data serialization.
*   **Office Interop Assemblies:**
    *   `Microsoft.Office.Interop.Excel` (Primary)
    *   `Microsoft.Office.Core`
    (Essential for interacting with the Excel application via COM.)
*   **`QuantBoost.Licensing.dll`:** Crucial shared project reference. Will handle all licensing, activation, and feature gating for the add-in.
*   **`System.Threading.Tasks`:** Will be extensively used for asynchronous operations (C# 7.3 `async`/`await` patterns).

### Build and Environment
*   **Build Tool:** MSBuild (via `.csproj` and `.sln` files).
*   **Target Environment:** Desktop environment with Microsoft Excel (Microsoft 365 Desktop, Windows 10/11) and .NET Framework 4.8.1 installed.
*   **Development Environment:** Visual Studio with VSTO development tools, configured for C# 7.3 and .NET Framework 4.8.1.
*   **Installer:** WiX Toolset for creating a unified MSI installer for the QuantBoost Suite.

### External API Calls
*   The add-in will interact with the QuantBoost Licensing API (e.g., `https://www.quantboost.ai/api/v1/`) via the shared `QuantBoost.Licensing.dll`.

### Database Interactions
*   No direct database interactions are planned for the Excel add-in features themselves.

### Key Architectural Principles
*   **Async-first:** All long-running operations must be asynchronous.
*   **Centralized Licensing:** The `QuantBoost.Licensing.SDK` is central to all feature access.
*   **Shared Installer:** A unified MSI for both PowerPoint and Excel add-ins.
*   **OpenXML and COM Interop:** Dual approach for file analysis in `Sheet Size Analyzer`.
*   **Strict Adherence to C# 7.3 and .NET 4.8.1:** No features from later language or framework versions.
*   **Forbidden OpenXML v3.x:** Use of OpenXML SDK v3.x or `System.IO.Packaging` is prohibited.
