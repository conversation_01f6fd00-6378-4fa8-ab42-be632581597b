# Project Requirements Document (PRD) - [Project Name]

**Purpose:** This document provides the foundational requirements and context for the "[Project Name]" project. It serves as the primary input for the AI Agent "Dane" to initialize its Memory Bank. Please provide clear and concise information in each section.

**Date:** YYYY-MM-DD
**Version:** 1.0

---

## 1. Introduction & Goals

### 1.1. Project Name:
[Enter the official name of the project.]

### 1.2. Project Purpose & Elevator Pitch:
[Briefly describe (1-3 sentences) what this project is, the core problem it solves, and its main value proposition. This seeds `projectbrief.md`'s Pitch and `productContext.md`'s Problem/Solution.]

### 1.3. Core Business/Technical Goals:
[List the top 2-5 measurable goals this project aims to achieve. What does success look like at a high level? This seeds `projectbrief.md`'s Goals.]
*   Goal 1: [e.g., Reduce manual data entry time by X%]
*   Goal 2: [e.g., Provide users with real-time analytics dashboard]
*   Goal 3: [e.g., Integrate with existing System Y]

---

## 2. Target Users & Problem Space

### 2.1. Target Audience:
[Describe the primary users of this project/feature. Who are they? What are their roles or key characteristics? This seeds `productContext.md`'s Target Users.]

### 2.2. Problem Statement:
[Elaborate slightly on the core problem being solved (mentioned in 1.2). What specific pain points are being addressed? This seeds `productContext.md`'s Problem Statement.]

---

## 3. Scope & Key Features

### 3.1. Project Composition & Identified Subprojects  <!-- Choose a suitable sub-number like 3.4 or create a new top-level section if preferred -->

**Overall Composition:**
<!-- Briefly describe if this is a monolithic application, a microservices architecture, or a collection of distinct but related components. This helps contextualize the subproject list. -->
[Specify overall project composition, e.g., "This project will be developed as a single, monolithic application." or "This project comprises a backend API and a frontend web application."]

**Identified Subprojects:**
<!--
Instructions for populating this list:
- List each distinct logical subproject, major independent component, or primary application name below.
- Use one subproject name per line, starting with a hyphen '-'.
- These names will be used for scoping tasks in plan.md and other Memory Bank documents.
- If this is a single application, list its main conceptual name (e.g., "RedditSummarizerService", "MainApplication", "CoreAPI").
- If unsure or if details are still being defined, use a placeholder like "- [Specify Subproject Name: Describe the component, e.g., User Interface]".
Examples:
- WebFrontend
- BackendAPI
- DataProcessingPipeline
- SharedUtilitiesLibrary
-->
- [Specify Subproject Name 1: e.g., Main Application or primary service name]
- [Optional: Specify Subproject Name 2, if applicable]
- [Optional: Specify Subproject Name 3, if applicable]

### 3.2. Key Features / User Stories (In Scope):
[List the essential features or user stories required for the initial version (MVP). Focus on *what* the system should do. This is crucial for seeding `plan.md` and `projectbrief.md`'s Scope.]
*   **Feature/Story 1:** As a [User Type], I want to [Action] so that [Benefit]. (Subproject(s): `[Specify if multi-project, e.g., Frontend, Backend]`)
*   **Feature/Story 2:** ... (Subproject(s): `[...]`)
*   **Feature/Story 3:** ... (Subproject(s): `[...]`)
*   *(Add more as needed)*

### 3.3. Explicitly Out of Scope:
[List any significant features or functionalities that are *intentionally* excluded from this project phase to prevent scope creep. This seeds `projectbrief.md`'s Scope.]
*   [e.g., Mobile App interface]
*   [e.g., Advanced reporting features]
*   [e.g., Integration with System Z]

---

## 4. Non-Functional Requirements (Optional but helpful)

### 4.1. Performance:
[Any specific performance goals? e.g., Page load time < 2s, API response < 200ms. This informs `constraints.md`.]

### 4.2. Security:
[Any specific security requirements? e.g., Must use specific auth protocol, Compliance needs (HIPAA, GDPR). This informs `constraints.md`.]

### 4.3. Usability / UX:
[Briefly describe the desired user experience feel. e.g., Intuitive for non-technical users, Professional and data-dense, Mobile-responsive. This seeds `productContext.md`'s UX Goals.]

---

## 5. Technical Considerations

### 5.1. Preferred Technology Stack (if any):
[List any mandatory or strongly preferred languages, frameworks, databases, or cloud platforms. If none, state "Flexible" or "To be determined by Agent". This seeds `techContext.md`, `environmentContext.md`, `constraints.md`.]
*   Frontend: [e.g., React, Vue, Flexible]
*   Backend: [e.g., Node.js/Express, Python/Flask, .NET Core]
*   Database: [e.g., PostgreSQL, MongoDB, Flexible]
*   Cloud: [e.g., AWS, Azure, None]

### 5.2. Architectural Preferences/Constraints (if any):
[Any high-level architectural direction? e.g., Must be serverless, Prefer microservices, Must integrate with existing monolith via API X. This seeds `systemPatterns.md`, `constraints.md`.]

### 5.3. Known Constraints/Rules:
[List any other hard rules or constraints not covered above. e.g., Must use specific internal library, Cannot use GPL-licensed dependencies. This seeds `constraints.md`.]

---

## 6. Success Metrics (Optional)

### 6.1. Key Metrics:
[How will the success defined in Goal 1.3 be measured more specifically? e.g., User satisfaction score > 4.0, Task completion rate > 90%. This seeds `projectbrief.md`'s Success Criteria.]

---

## 7. Additional Context / Links

[Provide links to any relevant documents, mockups, existing code repositories, API specifications, etc., that Dane should reference.]
*   [Link 1 Description]: [URL]
*   [Link 2 Description]: [URL]

---