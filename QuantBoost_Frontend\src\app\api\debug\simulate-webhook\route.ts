import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

function toIsoDate(secOrMs: number | null | undefined): string | null {
  if (!secOrMs) return null;
  return new Date(secOrMs * 1000).toISOString();
}

function generateLicenseKey(): string {
  return crypto.randomUUID();
}

export async function POST(req: Request) {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );

  try {
    // Simulate the webhook processing for the actual customer
    const customerId = 'cus_SkPkZlHKKdP0Vl'; // The actual customer ID from the test payment
    
    console.log(`🔍 Processing subscription for customer: ${customerId}`);

    // Step 1: Look up profile by stripe_customer_id (this should work now)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('stripe_customer_id', customerId)
      .maybeSingle();

    if (profileError) {
      console.error('Profile lookup error:', profileError);
      return NextResponse.json({ error: 'Profile lookup failed', details: profileError }, { status: 500 });
    }

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found for customer: ' + customerId }, { status: 404 });
    }

    console.log('✅ Found profile:', profile);

    // Step 2: Create subscription record (simulating the webhook data)
    const subscriptionData = {
      user_id: profile.id,
      stripe_subscription_id: 'sub_simulated_' + Date.now(),
      status: 'active',
      quantity: 1,
      current_period_start: toIsoDate(Math.floor(Date.now() / 1000)),
      current_period_end: toIsoDate(Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60)), // 1 year
      trial_start: null,
      trial_end: null,
      cancel_at_period_end: false,
    };

    console.log('🔄 Creating subscription with data:', subscriptionData);

    const { data: subData, error: subError } = await supabase
      .from('subscriptions')
      .upsert(subscriptionData)
      .select('id')
      .single();

    if (subError || !subData?.id) {
      console.error('❌ Error upserting subscription:', subError);
      return NextResponse.json({ 
        error: 'Subscription creation failed', 
        details: subError,
        testData: subscriptionData 
      }, { status: 500 });
    }

    console.log('✅ Successfully created/updated subscription:', {
      subscriptionId: subData.id,
      stripeSubscriptionId: subscriptionData.stripe_subscription_id,
      userId: profile.id
    });

    // Step 3: Create license record
    const licenseData = {
      user_id: profile.id,
      subscription_id: subData.id,
      license_key: generateLicenseKey(),
      product_id: 'prod_1RC3HTE6FvhUKV1bE9D6zf6e', // Annual plan product ID
      status: 'active',
      email: null,
    };

    console.log('🔄 Creating license with data:', licenseData);

    const { data: licenseResult, error: licenseError } = await supabase
      .from('licenses')
      .insert(licenseData)
      .select('id')
      .single();

    if (licenseError) {
      console.error('❌ Error adding license:', licenseError);
      return NextResponse.json({ 
        error: 'License creation failed', 
        details: licenseError,
        testData: licenseData,
        subscriptionCreated: subData
      }, { status: 500 });
    }

    console.log('✅ Successfully created license:', licenseResult);

    return NextResponse.json({ 
      success: true, 
      subscription: subData, 
      license: licenseResult,
      profile: profile,
      message: 'Simulated webhook processing completed successfully'
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Unexpected error', details: error }, { status: 500 });
  }
}
