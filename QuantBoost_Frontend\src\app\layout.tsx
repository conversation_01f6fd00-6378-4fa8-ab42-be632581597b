import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { SupabaseProvider } from '../components/SupabaseProvider';
import { ToastProvider } from '../components/ui/toast';
import { getNonce } from '../lib/nonce';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Quant Boost",
  description: "Quant Boost SaaS Platform",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const nonce = await getNonce();

  return (
    <html lang="en">
      <head>
        {/* Preconnect to Stripe for faster checkout loading */}
        <link rel="preconnect" href="https://js.stripe.com" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SupabaseProvider>
          <ToastProvider>
            {children}
          </ToastProvider>
        </SupabaseProvider>
        {/* Include nonce in a script for client-side access if needed */}
        <script nonce={nonce} dangerouslySetInnerHTML={{
          __html: `window.__CSP_NONCE__ = '${nonce}';`
        }} />
      </body>
    </html>
  );
}
