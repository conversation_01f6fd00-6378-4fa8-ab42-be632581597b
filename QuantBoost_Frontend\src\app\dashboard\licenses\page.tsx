"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';


import { useEffect, useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import {
  <PERSON>ton,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui";
import { apiClient, Subscription, License, isApiSuccess, handleApiError } from '@/lib/api';

interface SubscriptionWithLicenses extends Subscription {
  licenses: License[];
}

export default function LicensesPage() {
  const [loading, setLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithLicenses[]>([]);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabaseClient();

  useEffect(() => {
    fetchLicenseData();
  }, []);

  const fetchLicenseData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('User not authenticated');
        return;
      }

      // Fetch subscriptions from Azure API
      const subscriptionsResponse = await apiClient.getUserSubscriptions();
      if (!isApiSuccess(subscriptionsResponse)) {
        const errorMsg = handleApiError(subscriptionsResponse, 'Failed to fetch subscriptions');
        setError(errorMsg);
        return;
      }

      const userSubscriptions = subscriptionsResponse.data;
      
      // Fetch licenses for each subscription
      const subscriptionsWithLicenses: SubscriptionWithLicenses[] = [];
      for (const subscription of userSubscriptions) {
        const licensesResponse = await apiClient.getTeamLicenses(subscription.id);
        const licenses = isApiSuccess(licensesResponse) ? licensesResponse.data : [];
        subscriptionsWithLicenses.push({
          ...subscription,
          licenses
        });
      }

      setSubscriptions(subscriptionsWithLicenses);

    } catch (error) {
      console.error('Error fetching license data:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'assigned':
        return 'bg-yellow-100 text-yellow-800';
      case 'unassigned':
        return 'bg-gray-100 text-gray-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'revoked':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getAllLicenses = () => {
    return subscriptions.flatMap(sub => 
      sub.licenses.map(license => ({
        ...license,
        subscriptionPlan: sub.plan_id,
        subscriptionStatus: sub.status
      }))
    );
  };

  const getLicenseStats = () => {
    const allLicenses = getAllLicenses();
    return {
      total: allLicenses.length,
      active: allLicenses.filter(l => l.status === 'active').length,
      assigned: allLicenses.filter(l => l.status === 'assigned').length,
      unassigned: allLicenses.filter(l => l.status === 'unassigned').length,
      expired: allLicenses.filter(l => l.status === 'expired').length,
    };
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">License Management</h3>
          <p className="text-sm text-muted-foreground">Loading license information...</p>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-6 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">License Management</h3>
          <p className="text-sm text-muted-foreground">
            View and manage all your licenses.
          </p>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Licenses</CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={fetchLicenseData} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const stats = getLicenseStats();
  const allLicenses = getAllLicenses();

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">License Management</h3>
        <p className="text-sm text-muted-foreground">
          View and manage all your licenses across subscriptions.
        </p>
      </div>

      {/* License Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Licenses</CardTitle>
            <span className="text-2xl">🔑</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Across {subscriptions.length} subscription{subscriptions.length !== 1 ? 's' : ''}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <span className="text-2xl">✅</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">
              Currently in use
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned</CardTitle>
            <span className="text-2xl">📧</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.assigned}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting activation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <span className="text-2xl">🆓</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.unassigned}</div>
            <p className="text-xs text-muted-foreground">
              Ready to assign
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
            <span className="text-2xl">❌</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.expired}</div>
            <p className="text-xs text-muted-foreground">
              Need renewal
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Licenses by Subscription */}
      {subscriptions.map((subscription) => (
        <Card key={subscription.id}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">{subscription.plan_id}</CardTitle>
                <CardDescription>
                  {subscription.licenses.length} license{subscription.licenses.length !== 1 ? 's' : ''} • 
                  {subscription.quantity} seat{subscription.quantity !== 1 ? 's' : ''} • 
                  Status: {subscription.status}
                </CardDescription>
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${
                subscription.status === 'active' 
                  ? 'bg-green-100 text-green-800'
                  : subscription.status === 'trialing'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {subscription.status}
              </span>
            </div>
          </CardHeader>
          <CardContent>
            {subscription.licenses.length === 0 ? (
              <p className="text-sm text-muted-foreground">No licenses found for this subscription.</p>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>License Key</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned To</TableHead>
                    <TableHead>Tier</TableHead>
                    <TableHead>Assigned Date</TableHead>
                    <TableHead>Expiry Date</TableHead>
                    <TableHead>Activations</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {subscription.licenses.map((license) => (
                    <TableRow key={license.id}>
                      <TableCell className="font-mono text-sm">
                        {license.license_key.slice(0, 8)}...{license.license_key.slice(-8)}
                      </TableCell>
                      <TableCell>
                        <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(license.status)}`}>
                          {license.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        {license.email || (license.user_id ? `User: ${license.user_id.slice(-8)}` : 'Unassigned')}
                      </TableCell>
                      <TableCell>{license.license_tier}</TableCell>
                      <TableCell>
                        {license.assigned_at 
                          ? new Date(license.assigned_at).toLocaleDateString()
                          : 'N/A'
                        }
                      </TableCell>
                      <TableCell>
                        {license.expiry_date 
                          ? new Date(license.expiry_date).toLocaleDateString()
                          : 'N/A'
                        }
                      </TableCell>
                      <TableCell>
                        {license.activation_count} / {license.max_activations}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      ))}

      {/* All Licenses Summary */}
      {allLicenses.length === 0 && (
        <Card>
          <CardHeader>
            <CardTitle>No Licenses Found</CardTitle>
            <CardDescription>
              You don't have any licenses yet. Purchase a subscription to get started.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.href = '/pricing'}>
              View Pricing
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
