import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  // 1. Generate a random nonce
  const nonce = Buffer.from(crypto.randomUUID()).toString('base64');

  // 2. Define the Content Security Policy
  // This policy allows scripts from 'self' and <PERSON><PERSON>, and only allows inline scripts that have the correct nonce.
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'nonce-${nonce}' 'strict-dynamic' https://js.stripe.com;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data:;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    frame-src 'self' https://js.stripe.com;
    block-all-mixed-content;
    upgrade-insecure-requests;
  `.replace(/\s{2,}/g, ' ').trim(); // Remove newlines and extra spaces

  // 3. Clone the request headers and set the nonce
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-nonce', nonce);

  // 4. Start building the response
  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });

  // 5. Set the CSP header on the response
  response.headers.set('Content-Security-Policy', cspHeader);

  // 6. Handle authentication redirect (existing logic)
  const token = request.cookies.get('sb-access-token')?.value;
  if (!token && request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
