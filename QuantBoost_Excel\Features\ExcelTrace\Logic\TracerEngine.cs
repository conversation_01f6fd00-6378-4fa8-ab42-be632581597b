
// --- START OF FILE TracerEngine.cs ---

using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using Excel = Microsoft.Office.Interop.Excel;
using QuantBoost_Excel.Features.ExcelTrace.Model;
using QuantBoost_Shared.Utilities;

namespace QuantBoost_Excel.Features.ExcelTrace.Logic
{
    /// <summary>
    /// EPIC 1 TASK 1.1: Helper class to represent a formula reference match with position information.
    /// </summary>
    internal class FormulaMatch
    {
        public string FullMatch { get; set; }
        public string WorkbookName { get; set; }
        public string WorksheetName { get; set; }
        public string CellAddress { get; set; }
        public int StartIndex { get; set; }
        public int Length { get; set; }
        public int GroupIndex { get; set; } = -1; // For formula highlighting color coordination
    }

    /// <summary>
    /// Core engine for tracing Excel formula precedents and building hierarchical trace trees.
    /// Handles complex scenarios including cross-sheet references, external workbooks, named ranges,
    /// circular references, and error conditions.
    /// </summary>
    public class TracerEngine
    {
        #region Constants

        /// <summary>
        /// Default maximum depth for precedent tracing to prevent infinite recursion.
        /// </summary>
        public const int DEFAULT_MAX_DEPTH = 10;

        /// <summary>
        /// Maximum number of precedents to process at any single level to prevent performance issues.
        /// ENHANCED: Reduced for better performance in production.
        /// </summary>
        public const int MAX_PRECEDENTS_PER_LEVEL = 500;

        /// <summary>
        /// Maximum time in milliseconds to spend on a single trace operation.
        /// </summary>
        public const int MAX_TRACE_TIME_MS = 30000; // 30 seconds

        #endregion

        #region Private Fields

        private readonly HashSet<string> _visitedNodes;
        private readonly Dictionary<string, TraceNode> _nodeCache;
        private int _totalNodesProcessed;
        private readonly System.Diagnostics.Stopwatch _traceTimer;

        #endregion

        #region Constructors

        /// <summary>
        /// Initializes a new instance of the <see cref="TracerEngine"/> class.
        /// </summary>
        public TracerEngine()
        {
            _visitedNodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            _nodeCache = new Dictionary<string, TraceNode>(StringComparer.OrdinalIgnoreCase);
            _totalNodesProcessed = 0;
            _traceTimer = new System.Diagnostics.Stopwatch();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Builds a complete precedent tree starting from the specified Excel range.
        /// This is the main entry point for precedent tracing.
        /// </summary>
        /// <param name="startCell">The Excel range to start tracing from. Must be a single cell with a formula.</param>
        /// <param name="maxDepth">Maximum depth to trace (default: 10). Prevents infinite recursion.</param>
        /// <returns>A TraceNode representing the root of the precedent tree.</returns>
        /// <exception cref="ArgumentNullException">Thrown when startCell is null.</exception>
        /// <exception cref="ArgumentException">Thrown when startCell is not a single cell or doesn't contain a formula.</exception>
        /// <exception cref="COMException">Thrown when Excel COM operations fail.</exception>
        /// REASON: The method will now use the new configurable setting by default, instead of the hard-coded constant.
        public TraceNode BuildPrecedentTree(Excel.Range startCell, int maxDepth = -1)
        {
            if (startCell == null)
                throw new ArgumentNullException(nameof(startCell), "Start cell cannot be null");

            // REASON: This logic allows the method to either use a specific depth passed as an argument
            // or fall back to using the new global setting from TraceSettings.
            if (maxDepth == -1)
            {
                maxDepth = TraceSettings.MaxTraceDepth;
            }

            if (maxDepth < 1)
                throw new ArgumentException("Maximum depth must be at least 1", nameof(maxDepth));

            try
            {
                // Validate that we have a single cell
                if (startCell.Cells.Count != 1)
                    throw new ArgumentException("Start cell must be a single cell, not a range", nameof(startCell));

                // Reset state for new trace operation
                _visitedNodes.Clear();
                _nodeCache.Clear();
                _totalNodesProcessed = 0;
                _traceTimer.Restart();

                ErrorHandlingService.LogException(null, $"Starting precedent trace for cell: {GetCellFullAddress(startCell)} with max depth: {maxDepth}");

                // Create the root node
                var rootNode = CreateTraceNode(startCell, NodeType.Root, 0);

                // Validate that the cell has a formula
                if (string.IsNullOrEmpty(rootNode.Formula))
                {
                    // REASON: Instead of treating this as a hard error, we create a special "leaf" node.
                    // This provides a much better user experience than a blank error screen.
                    rootNode.HasError = true; // Keep the error flag for internal logic
                    rootNode.ErrorDetails = "Selected cell does not contain a formula";

                    // Use the cell's actual value for display.
                    rootNode.DisplayValue = rootNode.DisplayValue; // The value is already set by CreateTraceNode

                    // Set a user-friendly message for the list view.
                    rootNode.DisplayText = "Cell contains a value, not a formula.";

                    ErrorHandlingService.LogException(null, "Trace aborted: Selected cell does not contain a formula. Displaying value instead.");
                    return rootNode;
                }

                // Start the recursive tracing process
                PopulatePrecedents(rootNode, 0, maxDepth, _visitedNodes);

                // PRIORITY 1.1: Mark the entire tree as loaded to prevent "Loading..." placeholders
                MarkAllNodesAsLoaded(rootNode);

                _traceTimer.Stop();
                ErrorHandlingService.LogException(null, $"Precedent trace completed. Total nodes processed: {_totalNodesProcessed}, Time elapsed: {_traceTimer.ElapsedMilliseconds}ms");

                return rootNode;
            }
            catch (COMException comEx)
            {
                ErrorHandlingService.LogException(comEx, "COM exception during precedent tracing");
                return CreateErrorNode($"Excel COM Error: {comEx.Message}", comEx);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Unexpected error during precedent tracing");
                return CreateErrorNode($"Unexpected Error: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Builds precedents for a specific node without rebuilding the entire tree.
        /// Used for lazy loading of child nodes in the UI.
        /// </summary>
        /// <param name="parentNode">The node to populate children for.</param>
        /// <param name="maxDepth">Maximum additional depth to trace from this node.</param>
        /// <returns>True if children were successfully loaded, false if an error occurred.</returns>
        public bool PopulateNodeChildren(TraceNode parentNode, int maxDepth = 5)
        {
            if (parentNode == null)
                throw new ArgumentNullException(nameof(parentNode));

            ErrorHandlingService.LogException(null, $"=== POPULATE NODE CHILDREN DEBUG ===");
            ErrorHandlingService.LogException(null, $"Node: {parentNode.DisplayText}");
            ErrorHandlingService.LogException(null, $"ChildrenLoaded: {parentNode.ChildrenLoaded}");
            ErrorHandlingService.LogException(null, $"HasError: {parentNode.HasError}");

            if (parentNode.ChildrenLoaded || parentNode.HasError)
            {
                ErrorHandlingService.LogException(null, $"EARLY EXIT: Node already loaded or has error");
                return false;
            }

            try
            {
                ErrorHandlingService.LogException(null, $"Lazy loading children for node: {parentNode.DisplayText}");

                // Check if this is an array node that needs array expansion
                bool isArray = IsArrayNode(parentNode);
                ErrorHandlingService.LogException(null, $"IsArrayNode result: {isArray}");

                if (isArray)
                {
                    ErrorHandlingService.LogException(null, $"CALLING PopulateArrayChildren for {parentNode.DisplayText}");
                    bool result = PopulateArrayChildren(parentNode);
                    ErrorHandlingService.LogException(null, $"PopulateArrayChildren returned: {result}");
                    return result;
                }

                ErrorHandlingService.LogException(null, $"Not an array node, proceeding with normal precedent population");

                // Create a local visited set to prevent infinite recursion within this subtree
                var localVisited = new HashSet<string>(_visitedNodes, StringComparer.OrdinalIgnoreCase);

                PopulatePrecedents(parentNode, parentNode.Depth, parentNode.Depth + maxDepth, localVisited);

                parentNode.ChildrenLoaded = true;
                ErrorHandlingService.LogException(null, $"=== END POPULATE NODE CHILDREN DEBUG ===");
                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error lazy loading children for node: {parentNode.DisplayText}");
                parentNode.HasError = true;
                parentNode.ErrorDetails = $"Failed to load children: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// Determines if a node represents an array that can be expanded.
        /// </summary>
        /// <param name="node">The node to check.</param>
        /// <returns>True if the node contains an array that can be expanded.</returns>
        private bool IsArrayNode(TraceNode node)
        {
            bool isArray = node.DisplayValue == "<array>" &&
                          node.RawValue != null &&
                          node.RawValue.GetType().IsArray &&
                          !node.ChildrenLoaded;

            // Debug logging
            ErrorHandlingService.LogException(null, $"IsArrayNode check for {node.DisplayText}: DisplayValue='{node.DisplayValue}', RawValue={node.RawValue?.GetType()?.Name ?? "null"}, IsArray={node.RawValue?.GetType()?.IsArray ?? false}, ChildrenLoaded={node.ChildrenLoaded}, Result={isArray}");

            return isArray;
        }

        /// <summary>
        /// Populates the children of an array node with individual array elements.
        /// CRITICAL FIX: Enhanced to handle 2D arrays properly.
        /// </summary>
        /// <param name="arrayNode">The array node to populate.</param>
        /// <returns>True if array children were successfully created.</returns>
        private bool PopulateArrayChildren(TraceNode arrayNode)
        {
            try
            {
                ErrorHandlingService.LogException(null, $"=== POPULATE ARRAY CHILDREN DEBUG ===");
                ErrorHandlingService.LogException(null, $"Array Node: {arrayNode.DisplayText}");
                ErrorHandlingService.LogException(null, $"RawValue: {arrayNode.RawValue?.GetType()?.Name ?? "null"}");
                ErrorHandlingService.LogException(null, $"RawValue IsArray: {arrayNode.RawValue?.GetType()?.IsArray ?? false}");

                if (!(arrayNode.RawValue is Array array))
                {
                    ErrorHandlingService.LogException(null, $"ERROR: Array node {arrayNode.DisplayText} does not contain a valid array");
                    ErrorHandlingService.LogException(null, $"RawValue actual type: {arrayNode.RawValue?.GetType()?.FullName ?? "null"}");
                    return false;
                }

                ErrorHandlingService.LogException(null, $"SUCCESS: Found valid array with Length={array.Length}, Rank={array.Rank}");

                // Clear any existing children
                arrayNode.Children.Clear();
                ErrorHandlingService.LogException(null, $"Cleared existing children");

                // Handle 2D arrays (most common in Excel)
                if (array.Rank == 2)
                {
                    int rows = array.GetLength(0);
                    int cols = array.GetLength(1);

                    // CRITICAL FIX: Excel COM arrays are 1-based, so we need to use proper bounds
                    int lowerBound0 = array.GetLowerBound(0);
                    int upperBound0 = array.GetUpperBound(0);
                    int lowerBound1 = array.GetLowerBound(1);
                    int upperBound1 = array.GetUpperBound(1);

                    ErrorHandlingService.LogException(null, $"2D Array: Rows={rows}, Cols={cols}");
                    ErrorHandlingService.LogException(null, $"Bounds: [{lowerBound0}..{upperBound0}, {lowerBound1}..{upperBound1}]");

                    int childCount = 0;
                    for (int row = lowerBound0; row <= upperBound0; row++)
                    {
                        for (int col = lowerBound1; col <= upperBound1; col++)
                        {
                            var element = array.GetValue(row, col);
                            // Display 1-based indexing for user-friendly display
                            var displayRow = row - lowerBound0 + 1;
                            var displayCol = col - lowerBound1 + 1;
                            var childNode = CreateArrayElementNode(element, displayRow, displayCol, arrayNode);
                            arrayNode.Children.Add(childNode);
                            childCount++;

                            if (childCount <= 3) // Log first few children
                            {
                                ErrorHandlingService.LogException(null, $"Created child {childCount}: [{displayRow},{displayCol}] = '{childNode.DisplayValue}'");
                            }
                        }
                    }
                    ErrorHandlingService.LogException(null, $"Created {childCount} children for 2D array");
                }
                else
                {
                    // Handle 1D arrays
                    int lowerBound = array.GetLowerBound(0);
                    int upperBound = array.GetUpperBound(0);

                    ErrorHandlingService.LogException(null, $"1D Array: Bounds=[{lowerBound}..{upperBound}]");

                    int childCount = 0;
                    for (int i = lowerBound; i <= upperBound; i++)
                    {
                        var element = array.GetValue(i);
                        // Display 1-based indexing for user-friendly display
                        var displayIndex = i - lowerBound + 1;
                        var childNode = CreateArrayElementNode(element, displayIndex, 1, arrayNode);
                        arrayNode.Children.Add(childNode);
                        childCount++;

                        if (childCount <= 3) // Log first few children
                        {
                            ErrorHandlingService.LogException(null, $"Created child {childCount}: [{displayIndex}] = '{childNode.DisplayValue}'");
                        }
                    }
                    ErrorHandlingService.LogException(null, $"Created {childCount} children for 1D array");
                }

                arrayNode.ChildrenLoaded = true;
                ErrorHandlingService.LogException(null, $"SUCCESS: Expanded array node with {arrayNode.Children.Count} elements");
                ErrorHandlingService.LogException(null, $"ChildrenLoaded set to: {arrayNode.ChildrenLoaded}");
                ErrorHandlingService.LogException(null, $"=== END POPULATE ARRAY CHILDREN DEBUG ===");
                return true;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"EXCEPTION in PopulateArrayChildren for {arrayNode.DisplayText}: {ex.Message}");
                ErrorHandlingService.LogException(null, $"Exception StackTrace: {ex.StackTrace}");
                arrayNode.HasError = true;
                arrayNode.ErrorDetails = $"Failed to expand array: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// Creates a TraceNode for an individual array element.
        /// CRITICAL FIX: Updated to show actual cell addresses instead of array indices.
        /// REFACTOR: Enhanced to properly detect formulas in array elements by accessing actual Excel cells.
        /// </summary>
        /// <param name="element">The array element value.</param>
        /// <param name="row">The row index of the element in the array.</param>
        /// <param name="col">The column index of the element in the array.</param>
        /// <param name="parentNode">The parent array node.</param>
        /// <returns>A TraceNode representing the array element.</returns>
        private TraceNode CreateArrayElementNode(object element, int row, int col, TraceNode parentNode)
        {
            // CRITICAL FIX: Calculate actual cell address from parent range and array position
            string actualCellAddress = CalculateArrayElementAddress(parentNode.CellAddress, row, col);

            var elementNode = new TraceNode
            {
                DisplayText = actualCellAddress, // Show actual cell address like "C12", "C13", etc.
                FullAddress = $"[{parentNode.WorkbookName}]'{parentNode.WorksheetName}'!{actualCellAddress}",
                RawValue = element,
                NodeType = NodeType.ArrayElement,
                Depth = parentNode.Depth + 1,
                WorkbookName = parentNode.WorkbookName,
                WorksheetName = parentNode.WorksheetName,
                CellAddress = actualCellAddress,
                ChildrenLoaded = true // Default: assume no children unless we find a formula
            };

            // REFACTOR: Access the actual Excel cell to properly detect formulas
            // The array element value is the calculated result, not the formula itself
            Excel.Range actualCell = null;
            try
            {
                // Get the workbook and worksheet objects to access the actual cell
                Excel.Workbook wb = Globals.ThisAddIn.Application.Workbooks[parentNode.WorkbookName];
                Excel.Worksheet ws = (Excel.Worksheet)wb.Worksheets[parentNode.WorksheetName];
                actualCell = ws.Range[actualCellAddress];

                // CRITICAL: Check the actual cell's Formula property, not the array element value
                string cellFormula = actualCell.Formula?.ToString();

                if (!string.IsNullOrEmpty(cellFormula) && cellFormula.StartsWith("="))
                {
                    // This cell contains a formula - make it expandable
                    elementNode.Formula = cellFormula;
                    elementNode.ChildrenLoaded = false; // CRITICAL: This makes the node expandable for tracing precedents

                    // Use the same formatting logic as regular cells for display
                    var cellValue2 = actualCell.Value2;
                    var cellText = actualCell.Text?.ToString();
                    elementNode.DisplayValue = GetFormattedValue(cellValue2, cellText);

                    ErrorHandlingService.LogException(null, $"Array element {actualCellAddress} contains formula: {cellFormula} - marked as expandable");
                }
                else
                {
                    // No formula - this is a leaf node
                    elementNode.ChildrenLoaded = true;

                    // Format the display value based on the element type
                    if (element == null)
                    {
                        elementNode.DisplayValue = "-"; // Show dash for null/empty array elements
                    }
                    else if (element.GetType().IsArray)
                    {
                        // Nested array
                        elementNode.DisplayValue = "<array>";
                        elementNode.ChildrenLoaded = false; // Allow further expansion
                    }
                    else
                    {
                        // TASK 2.2: Use the same formatting logic as regular cells
                        var cellValue2 = actualCell.Value2;
                        var cellText = actualCell.Text?.ToString();
                        elementNode.DisplayValue = GetFormattedValue(cellValue2, cellText);
                    }

                    ErrorHandlingService.LogException(null, $"Array element {actualCellAddress} contains value (no formula): {elementNode.DisplayValue}");
                }
            }
            catch (Exception ex)
            {
                // Fallback to the original method if Excel access fails
                ErrorHandlingService.LogException(ex, $"Failed to access Excel cell {actualCellAddress} for array element, using fallback logic");

                // FALLBACK: Use the original logic as a safety net
                if (element is string formula && formula.StartsWith("="))
                {
                    elementNode.Formula = formula;
                    elementNode.ChildrenLoaded = false; // Make it expandable
                    elementNode.DisplayValue = GetFormattedValue(element, formula);
                }
                else
                {
                    elementNode.ChildrenLoaded = true;

                    if (element == null)
                    {
                        elementNode.DisplayValue = "-"; // Show dash for null/empty array elements
                    }
                    else if (element.GetType().IsArray)
                    {
                        elementNode.DisplayValue = "<array>";
                        elementNode.ChildrenLoaded = false; // Allow further expansion
                    }
                    else
                    {
                        elementNode.DisplayValue = GetFormattedValue(element, element?.ToString());
                    }
                }
            }
            finally
            {
                // CRITICAL: Always release COM objects to prevent memory leaks
                if (actualCell != null && Marshal.IsComObject(actualCell))
                {
                    Marshal.ReleaseComObject(actualCell);
                }
            }

            return elementNode;
        }

        /// <summary>
        /// Creates a TraceNode for an individual array element (1D array overload for backward compatibility).
        /// </summary>
        /// <param name="element">The array element value.</param>
        /// <param name="index">The index of the element in the array.</param>
        /// <param name="parentNode">The parent array node.</param>
        /// <returns>A TraceNode representing the array element.</returns>
        private TraceNode CreateArrayElementNode(object element, int index, TraceNode parentNode)
        {
            return CreateArrayElementNode(element, index, 1, parentNode);
        }

        /// <summary>
        /// CRITICAL FIX: Calculates the actual Excel cell address for an array element.
        /// Converts array indices to real cell addresses like C12, C13, etc.
        /// </summary>
        /// <param name="parentRangeAddress">The parent range address (e.g., "C12:C2547")</param>
        /// <param name="row">The 1-based row index within the array</param>
        /// <param name="col">The 1-based column index within the array</param>
        /// <returns>The actual Excel cell address</returns>
        private string CalculateArrayElementAddress(string parentRangeAddress, int row, int col)
        {
            try
            {
                // Parse the parent range address (e.g., "C12:C2547" or "$C$12:$C$2547")
                var cleanAddress = parentRangeAddress.Replace("$", ""); // Remove $ signs

                if (cleanAddress.Contains(":"))
                {
                    // Range address like "C12:C2547"
                    var parts = cleanAddress.Split(':');
                    var startCell = parts[0]; // e.g., "C12"

                    // Parse the start cell to get column and row
                    var startColumn = ExtractColumnFromAddress(startCell);
                    var startRow = ExtractRowFromAddress(startCell);

                    // Calculate the actual cell position
                    var actualColumn = CalculateColumnFromIndex(startColumn, col - 1); // col is 1-based, make it 0-based
                    var actualRow = startRow + (row - 1); // row is 1-based, make it 0-based for addition

                    return $"{actualColumn}{actualRow}";
                }
                else
                {
                    // Single cell address - shouldn't happen for arrays, but handle gracefully
                    return $"{parentRangeAddress}[{row},{col}]";
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error calculating array element address for {parentRangeAddress}[{row},{col}]");
                return $"{parentRangeAddress}[{row},{col}]"; // Fallback to index notation
            }
        }

        /// <summary>
        /// Extracts the column letters from a cell address like "C12" -> "C"
        /// </summary>
        private string ExtractColumnFromAddress(string cellAddress)
        {
            var result = "";
            foreach (char c in cellAddress)
            {
                if (char.IsLetter(c))
                    result += c;
                else
                    break;
            }
            return result;
        }

        /// <summary>
        /// Extracts the row number from a cell address like "C12" -> 12
        /// </summary>
        private int ExtractRowFromAddress(string cellAddress)
        {
            var numberPart = "";
            foreach (char c in cellAddress)
            {
                if (char.IsDigit(c))
                    numberPart += c;
            }
            return int.TryParse(numberPart, out int row) ? row : 1;
        }

        /// <summary>
        /// Calculates the column letters for a given column index offset from a base column
        /// </summary>
        private string CalculateColumnFromIndex(string baseColumn, int offset)
        {
            // Convert column letters to number (A=1, B=2, ..., Z=26, AA=27, etc.)
            int columnNumber = 0;
            for (int i = 0; i < baseColumn.Length; i++)
            {
                columnNumber = columnNumber * 26 + (baseColumn[i] - 'A' + 1);
            }

            // Add the offset
            columnNumber += offset;

            // Convert back to column letters
            string result = "";
            while (columnNumber > 0)
            {
                columnNumber--; // Make it 0-based
                result = (char)('A' + (columnNumber % 26)) + result;
                columnNumber /= 26;
            }

            return result;
        }

        #endregion

        #region Private Helper Methods - Core Tracing Logic

        /// <summary>
        /// PRIORITY 1.1: Marks all nodes in the tree as loaded to prevent "Loading..." placeholders.
        /// BUGFIX: Enhanced to preserve expandability for both array nodes AND array elements with formulas.
        /// </summary>
        /// <param name="node">The root node to start marking from.</param>
        private void MarkAllNodesAsLoaded(TraceNode node)
        {
            if (node == null) return;

            // CRITICAL FIX: Do not mark these node types as loaded - they should remain expandable:
            // 1. Array nodes (can expand to show array elements)
            // 2. Array element nodes with formulas (can expand to show precedents)
            bool isArrayNode = node.DisplayValue == "<array>" &&
                              node.RawValue != null &&
                              node.RawValue.GetType().IsArray;

            bool isArrayElementWithFormula = node.NodeType == NodeType.ArrayElement &&
                                           !string.IsNullOrEmpty(node.Formula) &&
                                           node.Formula.StartsWith("=");

            bool shouldRemainExpandable = isArrayNode || isArrayElementWithFormula;

            if (!shouldRemainExpandable)
            {
                node.ChildrenLoaded = true;
                ErrorHandlingService.LogException(null, $"MarkAllNodesAsLoaded: Marked {node.DisplayText} as loaded");
            }
            else
            {
                if (isArrayNode)
                {
                    ErrorHandlingService.LogException(null, $"MarkAllNodesAsLoaded: Skipped array node {node.DisplayText} to keep it expandable");
                }
                else if (isArrayElementWithFormula)
                {
                    ErrorHandlingService.LogException(null, $"MarkAllNodesAsLoaded: Skipped array element with formula {node.DisplayText} to keep it expandable");
                }
            }

            foreach (var child in node.Children)
            {
                MarkAllNodesAsLoaded(child);
            }
        }

        /// <summary>
        /// Recursively populates the precedents for a given node.
        /// This is the core recursive method that builds the precedent tree.
        /// </summary>
        /// <param name="parentNode">The node to populate precedents for.</param>
        /// <param name="currentDepth">Current depth in the recursion.</param>
        /// <param name="maxDepth">Maximum allowed depth.</param>
        /// <param name="visited">Set of already visited node addresses to prevent cycles.</param>
        private void PopulatePrecedents(TraceNode parentNode, int currentDepth, int maxDepth, HashSet<string> visited)
        {
            // PRIORITY 1.1: Prevent re-tracing of already loaded nodes
            if (parentNode.Children.Any())
            {
                // This node has already been processed. Do not process it again.
                ErrorHandlingService.LogException(null, $"Node {parentNode.DisplayText} already has children, skipping re-trace");
                return;
            }

            // REASON: This is the critical fix. If a node does not contain a formula, it cannot have precedents.
            // By returning early, we avoid the unnecessary and problematic calls to DirectPrecedents and ShowPrecedents.
            if (string.IsNullOrEmpty(parentNode.Formula))
            {
                return;
            }

            // Check depth limit
            if (currentDepth >= maxDepth)
            {
                ErrorHandlingService.LogException(null, $"Maximum trace depth ({maxDepth}) reached for node: {parentNode.DisplayText}");
                return;
            }

            // ENHANCED: Check timeout to prevent long-running operations
            if (_traceTimer.ElapsedMilliseconds > MAX_TRACE_TIME_MS)
            {
                ErrorHandlingService.LogException(null, $"Trace timeout ({MAX_TRACE_TIME_MS}ms) reached for node: {parentNode.DisplayText}");
                return;
            }

            // Check if we've already visited this node (cycle detection)
            if (visited.Contains(parentNode.FullAddress))
            {
                parentNode.IsCircularReference = true;
                ErrorHandlingService.LogException(null, $"Circular reference detected: {parentNode.FullAddress}");
                return;
            }

            // Add this node to visited set
            visited.Add(parentNode.FullAddress);

            // Declare COM objects at the proper scope for cleanup
            Excel.Range parentRange = null;
            Excel.Range precedents = null;

            try
            {
                // EPIC 1 TASK 1.3: Use the new formula parser as the primary method
                var formulaMatches = ParsePrecedentsFromFormulaString(parentNode.Formula);

                if (formulaMatches.Any())
                {
                    // EPIC 1 TASK 1.3.3: Process each match from the parser
                    ProcessFormulaMatches(formulaMatches, parentNode, currentDepth);
                }
                else
                {
                    // Fallback to DirectPrecedents for complex formulas that the parser might miss
                    try
                    {
                        // Get the workbook and worksheet objects
                        Excel.Workbook wb = Globals.ThisAddIn.Application.Workbooks[parentNode.WorkbookName];
                        Excel.Worksheet ws = (Excel.Worksheet)wb.Worksheets[parentNode.WorksheetName];
                        parentRange = ws.Range[parentNode.CellAddress];

                        const int NO_CELLS_FOUND_HRESULT = -2146827284; // HRESULT for 0x800A03EC

                        try
                        {
                            // Try DirectPrecedents as fallback
                            precedents = parentRange.DirectPrecedents;
                            ErrorHandlingService.LogException(null, $"SUCCESS: DirectPrecedents fallback found precedents for {parentNode.DisplayText}.");

                            // CRITICAL FIX: Track processed addresses to prevent duplicates
                            var processedAddresses = new HashSet<string>(
                                formulaMatches.Select(m => m.CellAddress),
                                StringComparer.OrdinalIgnoreCase
                            );

                            ProcessPrecedentRangeWithDuplicateCheck(precedents, parentNode, currentDepth, processedAddresses);
                        }
                        catch (COMException ex) when (ex.ErrorCode == NO_CELLS_FOUND_HRESULT)
                        {
                            // Both parser and DirectPrecedents failed - this is a leaf node
                            ErrorHandlingService.LogException(null, $"INFO: Both parser and DirectPrecedents failed for {parentNode.DisplayText}. Treating as leaf node.");
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex, $"Error in DirectPrecedents fallback for {parentNode.DisplayText}");
                    }
                }

                // Recurse into all children
                foreach (var childNode in parentNode.Children)
                {
                    if (!string.IsNullOrEmpty(childNode.Formula) && !childNode.IsCircularReference)
                    {
                        PopulatePrecedents(childNode, currentDepth + 1, maxDepth, visited);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"FATAL: Could not resolve range for {parentNode.DisplayText}. The workbook or sheet may be protected or closed.");
            }
            finally
            {
                // Release COM objects
                if (parentRange != null && Marshal.IsComObject(parentRange))
                    Marshal.ReleaseComObject(parentRange);
                if (precedents != null && Marshal.IsComObject(precedents))
                    Marshal.ReleaseComObject(precedents);

                visited.Remove(parentNode.FullAddress);
            }
        }

        /// <summary>
        /// CRITICAL FIX: Enhanced version that prevents duplicate processing.
        /// </summary>
        /// <param name="precedents">The range containing all precedent cells.</param>
        /// <param name="parentNode">The parent node to add children to.</param>
        /// <param name="currentDepth">Current recursion depth.</param>
        /// <param name="processedAddresses">Set of already processed cell addresses.</param>
        private void ProcessPrecedentRangeWithDuplicateCheck(Excel.Range precedents, TraceNode parentNode, int currentDepth, HashSet<string> processedAddresses)
        {
            try
            {
                int precedentCount = 0;

                // DirectPrecedents can return multiple Areas (e.g., when formula references non-contiguous ranges)
                foreach (Excel.Range area in precedents.Areas)
                {
                    // CRITICAL FIX: Always create range nodes for multi-cell ranges to show proper hierarchy
                    if (area.Cells.Count > 1)
                    {
                        // Create a single "range" parent node for the entire range
                        var rangeNode = CreateRangeNode(area, currentDepth + 1, parentNode);
                        parentNode.Children.Add(rangeNode);
                        _totalNodesProcessed++;

                        // CRITICAL FIX: Always add individual cells as children, but limit to reasonable number
                        int cellCount = 0;
                        const int MAX_CELLS_TO_SHOW = 50; // Limit for performance

                        foreach (Excel.Range cell in area.Cells)
                        {
                            string cellAddress = cell.Address[false, false];
                            if (processedAddresses.Contains(cellAddress))
                            {
                                continue; // Skip already processed
                            }

                            if (cellCount >= MAX_CELLS_TO_SHOW)
                            {
                                // Add a summary node for remaining cells
                                var remainingCount = area.Cells.Count - cellCount;
                                var summaryNode = new TraceNode
                                {
                                    DisplayText = $"... and {remainingCount} more cells",
                                    DisplayValue = "[Truncated for performance]",
                                    NodeType = NodeType.Summary,
                                    Depth = currentDepth + 2,
                                    ChildrenLoaded = true // No children for summary nodes
                                };
                                rangeNode.Children.Add(summaryNode);
                                break;
                            }

                            try
                            {
                                var cellNode = CreateTraceNode(cell, DetermineNodeType(cell, parentNode), currentDepth + 2, rangeNode);
                                rangeNode.Children.Add(cellNode);
                                _totalNodesProcessed++;
                                cellCount++;
                            }
                            catch (Exception cellEx)
                            {
                                ErrorHandlingService.LogException(cellEx, $"Error processing cell in range");
                                var errorChild = CreateErrorNode($"Cell processing error: {cellEx.Message}", cellEx);
                                rangeNode.Children.Add(errorChild);
                            }
                            finally
                            {
                                if (cell != null && Marshal.IsComObject(cell))
                                    Marshal.ReleaseComObject(cell);
                            }
                        }

                        ErrorHandlingService.LogException(null, $"CRITICAL FIX: Created range node {rangeNode.DisplayText} with {rangeNode.Children.Count} child cells");
                    }
                    else
                    {
                        // Single cell - process normally with duplicate check
                        foreach (Excel.Range cell in area.Cells)
                        {
                            string cellAddress = cell.Address[false, false];
                            if (processedAddresses.Contains(cellAddress))
                            {
                                continue; // Skip already processed
                            }

                            if (precedentCount >= MAX_PRECEDENTS_PER_LEVEL)
                            {
                                ErrorHandlingService.LogException(null, $"Maximum precedents per level ({MAX_PRECEDENTS_PER_LEVEL}) reached for node: {parentNode.DisplayText}");
                                break;
                            }

                            try
                            {
                                var childNode = CreateTraceNode(cell, DetermineNodeType(cell, parentNode), currentDepth + 1, parentNode);
                                parentNode.Children.Add(childNode);
                                _totalNodesProcessed++;
                            }
                            catch (Exception cellEx)
                            {
                                ErrorHandlingService.LogException(cellEx, $"Error processing individual precedent cell");
                                var errorChild = CreateErrorNode($"Cell processing error: {cellEx.Message}", cellEx);
                                parentNode.Children.Add(errorChild);
                            }
                            finally
                            {
                                if (cell != null && Marshal.IsComObject(cell))
                                    Marshal.ReleaseComObject(cell);
                            }

                            precedentCount++;
                        }
                    }

                    // Release the area COM object
                    if (area != null && Marshal.IsComObject(area))
                        Marshal.ReleaseComObject(area);
                }
            }
            finally
            {
                // Release the precedents range COM object
                if (precedents != null && Marshal.IsComObject(precedents))
                    Marshal.ReleaseComObject(precedents);
            }
        }

        /// <summary>
        /// Processes a range of precedent cells returned by Excel's DirectPrecedents.
        /// Handles multiple areas and individual cells within each area.
        /// EPIC 1 TASK 1.2: Implements hierarchical range node creation for multi-cell ranges.
        /// CRITICAL FIX: Enhanced to create proper hierarchical structure with individual cell children.
        /// </summary>
        /// <param name="precedents">The range containing all precedent cells.</param>
        /// <param name="parentNode">The parent node to add children to.</param>
        /// <param name="currentDepth">Current recursion depth.</param>
        private void ProcessPrecedentRange(Excel.Range precedents, TraceNode parentNode, int currentDepth)
        {
            try
            {
                int precedentCount = 0;

                // DirectPrecedents can return multiple Areas (e.g., when formula references non-contiguous ranges)
                foreach (Excel.Range area in precedents.Areas)
                {
                    // CRITICAL FIX: Always create range nodes for multi-cell ranges to show proper hierarchy
                    if (area.Cells.Count > 1)
                    {
                        // Create a single "range" parent node for the entire range
                        var rangeNode = CreateRangeNode(area, currentDepth + 1, parentNode);
                        parentNode.Children.Add(rangeNode);
                        _totalNodesProcessed++;

                        // CRITICAL FIX: Always add individual cells as children, but limit to reasonable number
                        int cellCount = 0;
                        const int MAX_CELLS_TO_SHOW = 50; // Limit for performance

                        foreach (Excel.Range cell in area.Cells)
                        {
                            if (cellCount >= MAX_CELLS_TO_SHOW)
                            {
                                // Add a summary node for remaining cells
                                var remainingCount = area.Cells.Count - cellCount;
                                var summaryNode = new TraceNode
                                {
                                    DisplayText = $"... and {remainingCount} more cells",
                                    DisplayValue = "[Truncated for performance]",
                                    NodeType = NodeType.Summary,
                                    Depth = currentDepth + 2,
                                    ChildrenLoaded = true // No children for summary nodes
                                };
                                rangeNode.Children.Add(summaryNode);
                                break;
                            }

                            try
                            {
                                var cellNode = CreateTraceNode(cell, DetermineNodeType(cell, parentNode), currentDepth + 2, rangeNode);
                                rangeNode.Children.Add(cellNode);
                                _totalNodesProcessed++;
                                cellCount++;
                            }
                            catch (Exception cellEx)
                            {
                                ErrorHandlingService.LogException(cellEx, $"Error processing cell in range");
                                var errorChild = CreateErrorNode($"Cell processing error: {cellEx.Message}", cellEx);
                                rangeNode.Children.Add(errorChild);
                            }
                            finally
                            {
                                if (cell != null && Marshal.IsComObject(cell))
                                    Marshal.ReleaseComObject(cell);
                            }
                        }

                        ErrorHandlingService.LogException(null, $"CRITICAL FIX: Created range node {rangeNode.DisplayText} with {rangeNode.Children.Count} child cells");
                    }
                    else
                    {
                        // Single cell - process normally
                        foreach (Excel.Range cell in area.Cells)
                        {
                            if (precedentCount >= MAX_PRECEDENTS_PER_LEVEL)
                            {
                                ErrorHandlingService.LogException(null, $"Maximum precedents per level ({MAX_PRECEDENTS_PER_LEVEL}) reached for node: {parentNode.DisplayText}");
                                break;
                            }

                            try
                            {
                                var childNode = CreateTraceNode(cell, DetermineNodeType(cell, parentNode), currentDepth + 1, parentNode);
                                parentNode.Children.Add(childNode);
                                _totalNodesProcessed++;
                            }
                            catch (Exception cellEx)
                            {
                                ErrorHandlingService.LogException(cellEx, $"Error processing individual precedent cell");
                                var errorChild = CreateErrorNode($"Cell processing error: {cellEx.Message}", cellEx);
                                parentNode.Children.Add(errorChild);
                            }
                            finally
                            {
                                if (cell != null && Marshal.IsComObject(cell))
                                    Marshal.ReleaseComObject(cell);
                            }

                            precedentCount++;
                        }
                    }

                    // Release the area COM object
                    if (area != null && Marshal.IsComObject(area))
                        Marshal.ReleaseComObject(area);
                }
            }
            finally
            {
                // Release the precedents range COM object
                if (precedents != null && Marshal.IsComObject(precedents))
                    Marshal.ReleaseComObject(precedents);
            }
        }

        #endregion

        #region Private Helper Methods - Formula Parsing

        /// <summary>
        /// EPIC 1 TASK 1.1: Core formula parser that tokenizes Excel formulas to find all cell/range references.
        /// This is the heart of the new engine, replacing reliance on DirectPrecedents.
        /// Enhanced with GroupIndex assignment for formula highlighting color coordination.
        /// CRITICAL FIX: Improved regex pattern and SUMIF-aware parsing logic.
        /// </summary>
        /// <param name="formula">The Excel formula string to parse (including the leading '=').</param>
        /// <returns>A list of FormulaMatch objects containing address, start index, length, and group index.</returns>
        private List<FormulaMatch> ParsePrecedentsFromFormulaString(string formula)
        {
            var matches = new List<FormulaMatch>();

            if (string.IsNullOrEmpty(formula) || !formula.StartsWith("="))
                return matches;

            try
            {
                // CRITICAL FIX: Enhanced regex pattern that better handles complex formulas
                // This pattern now correctly captures:
                // 1. External workbook references: [Book.xlsx]'Sheet'!A1
                // 2. Sheet references with spaces: 'Sheet Name'!A1
                // 3. Range references: A1:B10, C:C, 1:1
                // 4. Mixed absolute/relative: $A$1, A$1, $A1
                // CRITICAL FIX: Single comprehensive regex pattern to avoid overlapping matches
                // This pattern handles all Excel reference types in one pass
                var pattern = @"(?:'([^']+)'!|([A-Za-z_][A-Za-z0-9_]*)!)?(\$?[A-Z]{1,3}\$?\d{1,7}(?::\$?[A-Z]{1,3}\$?\d{1,7})?)";

                // DEBUG: Let's see what the actual formula looks like
                ErrorHandlingService.LogException(null, $"DEBUG: Parsing formula: '{formula}'");

                var regex = new System.Text.RegularExpressions.Regex(pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                var regexMatches = regex.Matches(formula);

                // CRITICAL FIX: Improved grouping logic for SUMIF and other functions
                int groupIndex = 0;
                string upperFormula = formula.ToUpper();

                // Special handling for SUMIF, SUMIFS, COUNTIF, etc.
                bool isSumifFunction = upperFormula.Contains("SUMIF") || upperFormula.Contains("COUNTIF") ||
                                      upperFormula.Contains("AVERAGEIF") || upperFormula.Contains("XLOOKUP") ||
                                      upperFormula.Contains("VLOOKUP") || upperFormula.Contains("HLOOKUP");

                foreach (System.Text.RegularExpressions.Match match in regexMatches)
                {
                    // Skip empty matches or matches that are just operators/functions
                    if (string.IsNullOrEmpty(match.Groups[3].Value))
                        continue;

                    // CRITICAL FIX: For SUMIF-type functions, assign different groups to different arguments
                    if (isSumifFunction)
                    {
                        // Find which argument position this match is in
                        string beforeMatch = formula.Substring(0, match.Index);
                        int commaCount = beforeMatch.Count(c => c == ',');
                        int openParenCount = beforeMatch.Count(c => c == '(');
                        int closeParenCount = beforeMatch.Count(c => c == ')');

                        // Only count commas that are at the same parentheses level as the function
                        if (openParenCount > closeParenCount)
                        {
                            groupIndex = commaCount; // Each argument gets its own group
                        }
                    }
                    else
                    {
                        // Standard grouping logic for other formulas
                        string beforeMatch = formula.Substring(0, match.Index);
                        int parenDepth = beforeMatch.Count(c => c == '(') - beforeMatch.Count(c => c == ')');
                        char[] operators = { '+', '-', '*', '/' };

                        if (parenDepth <= 1 && match.Index > 0 && operators.Contains(formula.ElementAtOrDefault(match.Index - 1)))
                        {
                            groupIndex++;
                        }
                    }

                    // CRITICAL FIX: Proper group extraction for the corrected regex
                    // Group 1: Quoted worksheet name ('Platform monthly GP Hours')
                    // Group 2: Unquoted worksheet name (GP_BC_Users)
                    // Group 3: Cell address ($C$12:$C$2547)

                    string worksheetName = null;
                    if (match.Groups[1].Success)
                    {
                        worksheetName = match.Groups[1].Value; // Quoted worksheet
                    }
                    else if (match.Groups[2].Success)
                    {
                        worksheetName = match.Groups[2].Value; // Unquoted worksheet
                    }

                    string cellAddress = match.Groups[3].Value;

                    // EPIC 1 TASK 1.1.4: Capture value, index, length, and group for each match
                    var formulaMatch = new FormulaMatch
                    {
                        FullMatch = match.Value,
                        WorkbookName = null, // No workbook references in this formula
                        WorksheetName = worksheetName,
                        CellAddress = cellAddress,
                        StartIndex = match.Index,
                        Length = match.Length,
                        GroupIndex = groupIndex
                    };

                    matches.Add(formulaMatch);

                    // DEBUG: Log each match details
                    ErrorHandlingService.LogException(null, $"DEBUG: Found match '{match.Value}' -> Worksheet: '{worksheetName}', Address: '{cellAddress}'");
                }

                ErrorHandlingService.LogException(null, $"CRITICAL FIX: Enhanced formula parser found {matches.Count} references in: {formula}");

                // DEBUG: Log each match for troubleshooting
                foreach (var match in matches)
                {
                    ErrorHandlingService.LogException(null, $"  Match: '{match.FullMatch}' at position {match.StartIndex}, length {match.Length}, worksheet: '{match.WorksheetName}', address: '{match.CellAddress}'");
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error parsing formula: {formula}");
            }

            return matches;
        }

        /// <summary>
        /// EPIC 1 TASK 1.3: Processes formula matches from the parser and creates child TraceNodes.
        /// This method populates the FormulaStartIndex and FormulaLength properties for highlighting.
        /// </summary>
        /// <param name="formulaMatches">The list of formula matches from the parser.</param>
        /// <param name="parentNode">The parent node to add children to.</param>
        /// <param name="currentDepth">Current recursion depth.</param>
        private void ProcessFormulaMatches(List<FormulaMatch> formulaMatches, TraceNode parentNode, int currentDepth)
        {
            try
            {
                foreach (var match in formulaMatches)
                {
                    if (_totalNodesProcessed >= MAX_PRECEDENTS_PER_LEVEL)
                    {
                        ErrorHandlingService.LogException(null, $"Maximum precedents per level ({MAX_PRECEDENTS_PER_LEVEL}) reached for node: {parentNode.DisplayText}");
                        break;
                    }

                    try
                    {
                        // EPIC 1 TASK 1.3.4: Create TraceNode with formula position information
                        var childNode = CreateTraceNodeFromMatch(match, parentNode, currentDepth + 1);

                        if (childNode != null)
                        {
                            parentNode.Children.Add(childNode);
                            _totalNodesProcessed++;
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex, $"Error processing formula match: {match.FullMatch}");
                        var errorChild = CreateErrorNode($"Match processing error: {ex.Message}", ex);
                        parentNode.Children.Add(errorChild);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error processing formula matches");
            }
        }

        /// <summary>
        /// EPIC 1 TASK 1.3.4: Creates a TraceNode from a formula match with position information.
        /// EPIC 2 TASK 2.1.1 & 2.1.2: Enhanced with proper formula position tracking for highlighting.
        /// </summary>
        /// <param name="match">The formula match containing address and position data.</param>
        /// <param name="parentNode">The parent node for context.</param>
        /// <param name="depth">The depth of the new node.</param>
        /// <returns>A new TraceNode with formula position data, or null if creation fails.</returns>
        private TraceNode CreateTraceNodeFromMatch(FormulaMatch match, TraceNode parentNode, int depth)
        {
            try
            {
                // Determine the target workbook and worksheet
                string targetWorkbook = match.WorkbookName ?? parentNode.WorkbookName;
                string targetWorksheet = match.WorksheetName ?? parentNode.WorksheetName;

                // Get the Excel range for this match
                Excel.Workbook wb = Globals.ThisAddIn.Application.Workbooks[targetWorkbook];
                Excel.Worksheet ws = (Excel.Worksheet)wb.Worksheets[targetWorksheet];
                Excel.Range targetRange = ws.Range[match.CellAddress];

                // Create the TraceNode using existing logic
                var nodeType = DetermineNodeTypeFromMatch(match, parentNode);
                var childNode = CreateTraceNode(targetRange, nodeType, depth, parentNode);

                // EPIC 2 TASK 2.1.1 & 2.1.2: Set the critical formula position properties for highlighting
                childNode.FormulaStartIndex = match.StartIndex;
                childNode.FormulaLength = match.Length;
                childNode.GroupIndex = match.GroupIndex; // Set group index for color coordination

                ErrorHandlingService.LogException(null, $"EPIC 2 TASK 2.1.2: Created node with formula position: {match.FullMatch} at index {match.StartIndex}, length {match.Length}");

                // Release the COM object
                if (targetRange != null && Marshal.IsComObject(targetRange))
                    Marshal.ReleaseComObject(targetRange);

                return childNode;
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error creating TraceNode from match: {match.FullMatch}");
                return null;
            }
        }

        /// <summary>
        /// EPIC 1 TASK 1.3.4: Determines the NodeType for a formula match.
        /// </summary>
        /// <param name="match">The formula match.</param>
        /// <param name="parentNode">The parent node for comparison.</param>
        /// <returns>The appropriate NodeType.</returns>
        private NodeType DetermineNodeTypeFromMatch(FormulaMatch match, TraceNode parentNode)
        {
            try
            {
                string targetWorkbook = match.WorkbookName ?? parentNode.WorkbookName;
                string targetWorksheet = match.WorksheetName ?? parentNode.WorksheetName;

                // Check if it's an external workbook
                if (!string.Equals(targetWorkbook, parentNode.WorkbookName, StringComparison.OrdinalIgnoreCase))
                {
                    return NodeType.ExternalWorkbook;
                }

                // Check if it's a different sheet
                if (!string.Equals(targetWorksheet, parentNode.WorksheetName, StringComparison.OrdinalIgnoreCase))
                {
                    return NodeType.DifferentSheet;
                }

                // Same sheet
                return NodeType.SameSheet;
            }
            catch
            {
                return NodeType.Error;
            }
        }

        /// <summary>
        /// REASON: This new helper method is the core of the fix. It directly parses simple link formulas
        /// (e.g., ='Sheet2'!A1) to find the precedent, bypassing the unreliable DirectPrecedents and ShowPrecedents
        /// methods which cause the unwanted arrows and fail to find the link.
        /// </summary>
        /// <param name="parentNode">The node containing the formula to parse.</param>
        /// <param name="parentWorksheet">The worksheet context for resolving addresses.</param>
        private void ParseSimpleLinkPrecedent(TraceNode parentNode, Excel.Worksheet parentWorksheet)
        {
            try
            {
                // REASON: This improved regex is much stricter. It now only matches formulas that start with '='
                // and are immediately followed by something that looks like a sheet/cell address (e.g., 'Sheet'!A1, A1:B2, $C$3).
                // It will correctly IGNORE simple calculations like '=1/3' or '=A1+B1', preventing the error.
                var match = System.Text.RegularExpressions.Regex.Match(parentNode.Formula, @"^='?([^']*)'?!\$?[A-Z]{1,3}\$?\d{1,7}|^\$?[A-Z]{1,3}\$?\d{1,7}");
                if (!match.Success) return;

                string address = match.Value.Substring(1); // Remove the '=' prefix
                Excel.Range precedentRange = null;
                try
                {
                    // Use the parent's context to resolve the address, which could be on another sheet.
                    precedentRange = parentWorksheet.Application.Range[address];

                    // We have the precedent cell(s), now process them just like the main method does.
                    ProcessPrecedentRange(precedentRange, parentNode, parentNode.Depth);
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex, $"Could not resolve parsed formula address: {address}");
                }
                finally
                {
                    if (precedentRange != null && Marshal.IsComObject(precedentRange))
                        Marshal.ReleaseComObject(precedentRange);
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error during simple link parsing for formula: {parentNode.Formula}");
            }
        }

        #endregion

        #region Private Helper Methods - Node Creation and Utilities

        /// <summary>
        /// Creates a TraceNode representing a multi-cell range with hierarchical structure.
        /// EPIC 1 TASK 1.2: Creates range nodes for multi-cell precedents like AM28:AM33.
        /// EPIC 1 TASK 1.2.1 & 1.2.2: Implements proper <array> display and formula inheritance.
        /// CRITICAL FIX: Actually get the array values from the range.
        /// </summary>
        /// <param name="range">The Excel range containing multiple cells.</param>
        /// <param name="depth">The depth of this node in the tree.</param>
        /// <param name="parentNode">The parent node to inherit formula context from.</param>
        /// <returns>A TraceNode representing the range with appropriate display properties.</returns>
        private TraceNode CreateRangeNode(Excel.Range range, int depth, TraceNode parentNode = null)
        {
            var node = new TraceNode
            {
                NodeType = NodeType.Range,
                Depth = depth
            };

            try
            {
                // Get range address information
                node.CellAddress = range.Address[false, false]; // Local address without $ signs
                node.WorksheetName = ((Excel.Worksheet)range.Worksheet)?.Name ?? "Unknown";
                node.WorkbookName = ((Excel.Workbook)((Excel.Worksheet)range.Worksheet)?.Parent)?.Name ?? "Unknown";

                // Build full address for cycle detection
                node.FullAddress = GetRangeFullAddress(range);

                // EPIC 1 TASK 1.2.1: Set display properties for range nodes
                node.DisplayText = node.CellAddress; // e.g., "AM28:AM33"

                // CRITICAL FIX: Actually get the array values from the range
                try
                {
                    // Get the array of values from the range
                    var values = range.Value2;
                    if (values != null && values.GetType().IsArray)
                    {
                        node.RawValue = values;
                        node.DisplayValue = "<array>";
                        node.ChildrenLoaded = false; // BUGFIX: Ensure array nodes remain expandable
                        ErrorHandlingService.LogException(null, $"CreateRangeNode: Created array node {node.CellAddress} with ChildrenLoaded=false");
                    }
                    else if (range.Cells.Count > 1)
                    {
                        // Multi-cell range but Value2 didn't return array - try to create one
                        var cellValues = new object[range.Cells.Count];
                        int index = 0;
                        foreach (Excel.Range cell in range.Cells)
                        {
                            try
                            {
                                cellValues[index] = cell.Value2;
                            }
                            catch
                            {
                                cellValues[index] = null;
                            }
                            finally
                            {
                                if (cell != null && Marshal.IsComObject(cell))
                                    Marshal.ReleaseComObject(cell);
                            }
                            index++;
                        }
                        node.RawValue = cellValues;
                        node.DisplayValue = "<array>";
                        node.ChildrenLoaded = false; // BUGFIX: Ensure array nodes remain expandable
                        ErrorHandlingService.LogException(null, $"CreateRangeNode: Created multi-cell array node {node.CellAddress} with ChildrenLoaded=false");
                    }
                    else
                    {
                        // Single cell range - get the single value
                        node.RawValue = values;
                        node.DisplayValue = values?.ToString() ?? "";
                        node.ChildrenLoaded = true; // Single cell ranges are leaf nodes
                    }
                }
                catch (Exception ex)
                {
                    ErrorHandlingService.LogException(ex, $"Error getting range values for {node.CellAddress}");
                    node.DisplayValue = $"[{range.Cells.Count} cells]";
                    node.ChildrenLoaded = true; // Error nodes are leaf nodes
                }

                // EPIC 1 TASK 1.2.2: Set Formula = parentNode.Formula to inherit formula context
                if (parentNode != null)
                {
                    node.Formula = parentNode.Formula; // Inherit formula from parent for formula bar context
                }

                ErrorHandlingService.LogException(null, $"EPIC 1 TASK 1.2: Created range node: {node.DisplayText} with {range.Cells.Count} cells, inherited formula: {node.Formula}");
            }
            catch (Exception ex)
            {
                node.HasError = true;
                node.ErrorDetails = $"Error creating range node: {ex.Message}";
                node.DisplayValue = "[Range Error]";
                ErrorHandlingService.LogException(ex, $"Error creating range TraceNode");
            }

            return node;
        }

        /// <summary>
        /// Creates a TraceNode from an Excel Range with all properties populated.
        /// EPIC 1 TASK 1.3: Enhanced with parentNode parameter for context-aware display text.
        /// </summary>
        /// <param name="cell">The Excel range (should be a single cell).</param>
        /// <param name="nodeType">The type of node to create.</param>
        /// <param name="depth">The depth of this node in the tree.</param>
        /// <param name="parentNode">The parent node for context-aware formatting (optional).</param>
        /// <returns>A fully populated TraceNode.</returns>
        private TraceNode CreateTraceNode(Excel.Range cell, NodeType nodeType, int depth, TraceNode parentNode = null)
        {
            var node = new TraceNode
            {
                NodeType = nodeType,
                Depth = depth
                // FIX: Removed SourceRange assignment to avoid COM object lifetime issues
            };

            try
            {
                // Get basic address information
                node.CellAddress = cell.Address[false, false]; // Local address without $ signs
                node.WorksheetName = ((Excel.Worksheet)cell.Worksheet)?.Name ?? "Unknown";
                node.WorkbookName = ((Excel.Workbook)((Excel.Worksheet)cell.Worksheet)?.Parent)?.Name ?? "Unknown";

                // Build full address for cycle detection
                node.FullAddress = GetCellFullAddress(cell);

                // Build display text based on node type and depth
                node.DisplayText = BuildDisplayText(cell, nodeType, depth, parentNode);

                // PRIORITY 2.1: Improved value retrieval for better formatting consistency
                // By accessing Value2 first, we ensure Excel has fully calculated the cell.
                // Then, accessing .Text is more likely to return the fully formatted string.
                var cellValue2 = cell.Value2;
                var cellText = cell.Text?.ToString();

                // Store the raw value for potential future use
                node.RawValue = cellValue2;

                // Check if this is an array value and handle appropriately
                if (cellValue2 != null && cellValue2.GetType().IsArray)
                {
                    node.DisplayValue = "<array>";
                    node.ChildrenLoaded = false; // BUGFIX: Ensure array nodes remain expandable
                    ErrorHandlingService.LogException(null, $"CreateTraceNode: Created array node {node.CellAddress} with ChildrenLoaded=false");
                }
                else if (string.IsNullOrWhiteSpace(cellText))
                {
                    // Handle blank/empty cells
                    if (cellValue2 == null)
                    {
                        node.DisplayValue = "";
                        node.ChildrenLoaded = true; // Empty cells are leaf nodes
                    }
                    else
                    {
                        // Value exists but text is blank - might be an array or special value
                        var valueType = cellValue2.GetType();
                        if (valueType.IsArray)
                        {
                            node.DisplayValue = "<array>";
                            node.ChildrenLoaded = false; // BUGFIX: Ensure array nodes remain expandable
                            ErrorHandlingService.LogException(null, $"CreateTraceNode: Created blank-text array node {node.CellAddress} with ChildrenLoaded=false");
                        }
                        else
                        {
                            node.DisplayValue = cellValue2.ToString();
                            node.ChildrenLoaded = true; // Regular values are leaf nodes
                        }
                    }
                }
                else
                {
                    node.DisplayValue = cellText; // Use Excel's formatted display value
                    node.ChildrenLoaded = true; // Regular cells with text are leaf nodes (unless they have formulas, handled below)
                }

                // Get formula if it exists
                try
                {
                    var formula = cell.Formula?.ToString();
                    node.Formula = !string.IsNullOrEmpty(formula) && formula.StartsWith("=") ? formula : null;

                    // BUGFIX: If node has a formula, it should be expandable (override previous ChildrenLoaded setting)
                    if (!string.IsNullOrEmpty(node.Formula))
                    {
                        node.ChildrenLoaded = false; // Nodes with formulas can have precedents
                        ErrorHandlingService.LogException(null, $"CreateTraceNode: Node {node.CellAddress} has formula, set ChildrenLoaded=false");
                    }
                }
                catch
                {
                    node.Formula = null;
                }
            }
            catch (Exception ex)
            {
                node.HasError = true;
                node.ErrorDetails = $"Error creating node: {ex.Message}";
                node.DisplayValue = "[Error]";
                ErrorHandlingService.LogException(ex, $"Error creating TraceNode for cell");
            }

            return node;
        }

        /// <summary>
        /// Determines the appropriate NodeType for a cell relative to its parent.
        /// </summary>
        /// <param name="cell">The cell to analyze.</param>
        /// <param name="parentNode">The parent node for comparison.</param>
        /// <returns>The appropriate NodeType.</returns>
        private NodeType DetermineNodeType(Excel.Range cell, TraceNode parentNode)
        {
            try
            {
                var cellWorkbook = ((Excel.Workbook)cell.Worksheet?.Parent)?.Name;
                var cellWorksheet = cell.Worksheet?.Name;

                // Check if it's an external workbook
                if (!string.Equals(cellWorkbook, parentNode.WorkbookName, StringComparison.OrdinalIgnoreCase))
                {
                    return NodeType.ExternalWorkbook;
                }

                // Check if it's a different sheet
                if (!string.Equals(cellWorksheet, parentNode.WorksheetName, StringComparison.OrdinalIgnoreCase))
                {
                    return NodeType.DifferentSheet;
                }

                // Same sheet
                return NodeType.SameSheet;
            }
            catch
            {
                return NodeType.Error;
            }
        }

        /// <summary>
        /// Builds appropriate display text for a node based on its type and location.
        /// EPIC 1 TASK 1.3: Enhanced with parentNode parameter for context-aware formatting.
        /// </summary>
        /// <param name="cell">The Excel cell.</param>
        /// <param name="nodeType">The type of node (currently unused but kept for future extensibility).</param>
        /// <param name="depth">The depth of this node in the tree.</param>
        /// <param name="parentNode">The parent node for worksheet comparison context.</param>
        /// <returns>Formatted display text.</returns>
        private string BuildDisplayText(Excel.Range cell, NodeType nodeType, int depth, TraceNode parentNode)
        {
            try
            {
                var cellWorksheet = cell.Worksheet?.Name;
                var address = cell.Address[false, false]; // EPIC 1 TASK 1.3: Strip $ characters

                // Root node shows simple address (e.g., AP50)
                if (depth == 0)
                {
                    return address;
                }

                // EPIC 1 TASK 1.3: Compare worksheets with parent for context-aware display
                if (parentNode != null && string.Equals(cellWorksheet, parentNode.WorksheetName, StringComparison.OrdinalIgnoreCase))
                {
                    // Same worksheet as parent - show only cell address
                    return address;
                }
                else
                {
                    // Different worksheet - show qualified name
                    return $"'{cellWorksheet}'!{address}";
                }
            }
            catch
            {
                return "[Error: Cannot determine address]";
            }
        }

        /// <summary>
        /// Gets the fully qualified address of a cell for cycle detection.
        /// </summary>
        /// <param name="cell">The Excel cell.</param>
        /// <returns>Fully qualified address string.</returns>
        private string GetCellFullAddress(Excel.Range cell)
        {
            try
            {
                var address = cell.Address[false, false];
                var sheetName = cell.Worksheet?.Name ?? "Unknown";
                var workbookName = ((Excel.Workbook)cell.Worksheet?.Parent)?.Name ?? "Unknown";

                return $"[{workbookName}]'{sheetName}'!{address}";
            }
            catch
            {
                return $"[Error]Unknown!{Guid.NewGuid()}";
            }
        }

        /// <summary>
        /// Gets the fully qualified address of a range for cycle detection.
        /// EPIC 1 TASK 1.2: Helper method for range node addressing.
        /// </summary>
        /// <param name="range">The Excel range.</param>
        /// <returns>Fully qualified address string.</returns>
        private string GetRangeFullAddress(Excel.Range range)
        {
            try
            {
                var address = range.Address[false, false];
                var sheetName = range.Worksheet?.Name ?? "Unknown";
                var workbookName = ((Excel.Workbook)range.Worksheet?.Parent)?.Name ?? "Unknown";

                return $"[{workbookName}]'{sheetName}'!{address}";
            }
            catch
            {
                return $"[Error]Unknown!{Guid.NewGuid()}";
            }
        }

        /// <summary>
        /// Creates an error node with the specified message and exception details.
        /// </summary>
        /// <param name="message">The error message to display.</param>
        /// <param name="exception">The exception that caused the error (optional).</param>
        /// <returns>A TraceNode representing the error.</returns>
        private TraceNode CreateErrorNode(string message, Exception exception = null)
        {
            return new TraceNode
            {
                NodeType = NodeType.Error,
                DisplayText = "Error",
                DisplayValue = message,
                HasError = true,
                ErrorDetails = exception?.ToString() ?? message,
                ChildrenLoaded = true // Error nodes don't have children
            };
        }

        #endregion

        #region Public Static Helper Methods

        /// <summary>
        /// Gets the formatted display value from Excel cell data, handling errors and formatting.
        /// PRIORITY 1.1: Rewritten to accept raw value and text separately to avoid truncation issues.
        /// Enhanced to handle array values properly.
        /// </summary>
        /// <param name="value2">The raw Value2 from the Excel cell.</param>
        /// <param name="text">The formatted Text from the Excel cell.</param>
        /// <returns>The formatted value string, or an error indicator if reading fails.</returns>
        public static string GetFormattedValue(object value2, string text)
        {
            if (value2 == null) return "[Empty]";

            try
            {
                // Handle array values first
                if (value2.GetType().IsArray)
                {
                    return "<array>";
                }

                // Handle Excel error codes
                if (value2 is int errorCode)
                {
                    switch (errorCode)
                    {
                        case -2146826281: return "#DIV/0!";
                        case -2146826246: return "#N/A";
                        case -2146826259: return "#NAME?";
                        case -2146826288: return "#NULL!";
                        case -2146826252: return "#NUM!";
                        case -2146826265: return "#REF!";
                        case -2146826273: return "#VALUE!";
                        default: return $"#ERROR({errorCode})";
                    }
                }

                // Handle blank text with non-null values (might be arrays)
                if (string.IsNullOrWhiteSpace(text) && value2 != null)
                {
                    if (value2.GetType().IsArray)
                    {
                        return "<array>";
                    }
                    return value2.ToString();
                }

                // For everything else, return the formatted text from the cell.
                // The UI will now handle formatting for numbers to avoid truncation.
                return text ?? value2.ToString();
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error formatting cell value");
                return "[Format Error]";
            }
        }

        #endregion
    }
}

// --- END OF FILE TracerEngine.cs ---
