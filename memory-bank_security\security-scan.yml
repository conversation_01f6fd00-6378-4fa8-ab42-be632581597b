name: Security Scan
on: 
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  secret-scan:
    runs-on: ubuntu-latest
    name: Secret Detection with TruffleHog
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog secret detection
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  stripe-security-audit:
    runs-on: ubuntu-latest
    name: Stripe Security Audit
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Audit npm packages
        run: npm audit --audit-level=moderate

      - name: Check for hardcoded Stripe keys
        run: |
          echo "🔍 Scanning for hardcoded Stripe keys..."
          if grep -r "sk_test_\|pk_test_\|sk_live_\|pk_live_" --include="*.ts" --include="*.js" --include="*.json" . ; then
            echo "❌ ERROR: Stripe keys found in code!"
            exit 1
          else
            echo "✅ No hardcoded Stripe keys found"
          fi

      - name: Check environment variable usage
        run: |
          echo "🔍 Checking proper environment variable usage..."
          if grep -r "process\.env\.STRIPE" --include="*.ts" --include="*.js" . ; then
            echo "✅ Environment variables properly used for Stripe"
          else
            echo "⚠️  WARNING: No environment variable usage found for Stripe"
          fi

      - name: Validate webhook endpoints
        run: |
          echo "🔍 Checking webhook endpoint security..."
          if grep -r "stripe.*signature" --include="*.ts" --include="*.js" . ; then
            echo "✅ Webhook signature validation found"
          else
            echo "❌ WARNING: No webhook signature validation found"
          fi

  dependency-scan:
    runs-on: ubuntu-latest
    name: Dependency Security Scan
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Snyk CLI
        run: npm install -g snyk

      - name: Authenticate Snyk
        run: snyk auth ${{ secrets.SNYK_TOKEN }}
        continue-on-error: true

      - name: Run Snyk security scan
        run: |
          if [ -f "package.json" ]; then
            snyk test --severity-threshold=high || echo "Snyk scan completed with findings"
          else
            echo "No package.json found, skipping Snyk scan"
          fi
        continue-on-error: true

  code-quality:
    runs-on: ubuntu-latest
    name: Code Quality and Security Linting
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: |
          if [ -f "package.json" ]; then
            npm ci
          else
            echo "No package.json found, creating minimal setup for linting"
            npm init -y
            npm install --save-dev eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin eslint-plugin-security
          fi

      - name: Run ESLint security plugin
        run: |
          echo "🔍 Running security-focused linting..."
          if [ -f "package.json" ]; then
            npx eslint . --ext .ts,.js --ignore-pattern node_modules/ || echo "Linting completed with findings"
          else
            echo "Skipping ESLint - no proper setup"
          fi
        continue-on-error: true

      - name: Check for console statements
        run: |
          echo "🔍 Checking for console statements in production code..."
          if grep -r "console\." --include="*.ts" --include="*.js" --exclude-dir=node_modules . ; then
            echo "⚠️  Console statements found - consider using proper logging"
          else
            echo "✅ No console statements found"
          fi

  stripe-api-security:
    runs-on: ubuntu-latest
    name: Stripe API Security Validation
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate Stripe integration patterns
        run: |
          echo "🔍 Validating Stripe integration security patterns..."
          
          # Check for idempotency key usage
          if grep -r "idempotencyKey\|idempotency" --include="*.ts" --include="*.js" . ; then
            echo "✅ Idempotency key usage found"
          else
            echo "⚠️  WARNING: No idempotency key usage found"
          fi
          
          # Check for rate limiting
          if grep -r "rate.*limit\|rateLimit" --include="*.ts" --include="*.js" . ; then
            echo "✅ Rate limiting implementation found"
          else
            echo "⚠️  WARNING: No rate limiting found"
          fi
          
          # Check for input validation
          if grep -r "validate\|validation" --include="*.ts" --include="*.js" . ; then
            echo "✅ Input validation found"
          else
            echo "⚠️  WARNING: Limited input validation found"
          fi
          
          # Check for error handling
          if grep -r "try.*catch\|\.catch\|throw" --include="*.ts" --include="*.js" . ; then
            echo "✅ Error handling patterns found"
          else
            echo "⚠️  WARNING: Limited error handling found"
          fi

  security-report:
    runs-on: ubuntu-latest
    name: Generate Security Report
    needs: [secret-scan, stripe-security-audit, dependency-scan, code-quality, stripe-api-security]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Generate security summary
        run: |
          echo "# 🔒 Security Scan Summary" > security-report.md
          echo "" >> security-report.md
          echo "**Scan Date:** $(date)" >> security-report.md
          echo "**Repository:** ${{ github.repository }}" >> security-report.md
          echo "**Branch:** ${{ github.ref_name }}" >> security-report.md
          echo "" >> security-report.md
          
          echo "## Scan Results" >> security-report.md
          echo "" >> security-report.md
          echo "| Check | Status |" >> security-report.md
          echo "|-------|---------|" >> security-report.md
          echo "| Secret Detection | ${{ needs.secret-scan.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |" >> security-report.md
          echo "| Stripe Security Audit | ${{ needs.stripe-security-audit.result == 'success' && '✅ PASSED' || '❌ FAILED' }} |" >> security-report.md
          echo "| Dependency Scan | ${{ needs.dependency-scan.result == 'success' && '✅ PASSED' || '⚠️ WARNINGS' }} |" >> security-report.md
          echo "| Code Quality | ${{ needs.code-quality.result == 'success' && '✅ PASSED' || '⚠️ WARNINGS' }} |" >> security-report.md
          echo "| Stripe API Security | ${{ needs.stripe-api-security.result == 'success' && '✅ PASSED' || '⚠️ WARNINGS' }} |" >> security-report.md
          echo "" >> security-report.md
          
          echo "## Security Recommendations" >> security-report.md
          echo "" >> security-report.md
          echo "- ✅ Use environment variables for all API keys" >> security-report.md
          echo "- ✅ Implement webhook signature verification" >> security-report.md
          echo "- ✅ Add rate limiting to API endpoints" >> security-report.md
          echo "- ✅ Use idempotency keys for Stripe operations" >> security-report.md
          echo "- ✅ Implement comprehensive input validation" >> security-report.md
          echo "- ✅ Add proper error handling and logging" >> security-report.md
          echo "- ✅ Regular dependency updates and security audits" >> security-report.md
          echo "" >> security-report.md
          
          echo "Generated security report:"
          cat security-report.md

      - name: Upload security report
        uses: actions/upload-artifact@v4
        with:
          name: security-report
          path: security-report.md
          retention-days: 30

      - name: Comment on PR (if applicable)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('security-report.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            });