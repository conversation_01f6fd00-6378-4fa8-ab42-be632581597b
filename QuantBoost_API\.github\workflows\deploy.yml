name: Build and Deploy to Azure

on:
  workflow_dispatch:  # Manual trigger only
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging
      deploy_reason:
        description: 'Reason for deployment'
        required: false
        default: 'Manual deployment'
        type: string
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: true
        type: boolean

env:
  CONTAINER_NAME: ca-quantboost-api
  RESOURCE_GROUP: rg-quantboost-api-prod
  CONTAINER_APP_NAME: ca-quantboost-api
  REGISTRY_URL: acrquantboostup46p0.azurecr.io
  IMAGE_NAME: ca-quantboost-api # Define image name for consistency
  DOCKERFILE_PATH: ./QuantBoost_API/Dockerfile # Define Dockerfile path

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write # Required for OIDC login to Azure
      contents: read   # Required to checkout the repo

    steps:
    - name: Deployment Info
      run: |
        echo "🚀 Manual API deployment triggered"
        echo "📍 Environment: ${{ github.event.inputs.environment }}"
        echo "📝 Reason: ${{ github.event.inputs.deploy_reason }}"
        echo "👤 Triggered by: ${{ github.actor }}"
        echo "📦 Deploying commit: ${{ github.sha }}"
        echo "🔄 Force rebuild: ${{ github.event.inputs.force_rebuild }}"

    - name: Checkout to the branch
      uses: actions/checkout@v4

    - name: Azure Login
      uses: azure/login@v1
      with:
        client-id: ${{ secrets.CAQUANTBOOSTAPI_AZURE_CLIENT_ID }}
        tenant-id: ${{ secrets.CAQUANTBOOSTAPI_AZURE_TENANT_ID }}
        subscription-id: ${{ secrets.CAQUANTBOOSTAPI_AZURE_SUBSCRIPTION_ID }}

    # --- STEP 1: Login to Azure Container Registry ---
    # We need to explicitly log in so Docker can push the image.
    - name: Login to ACR
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY_URL }}
        username: ${{ secrets.CAQUANTBOOSTAPI_REGISTRY_USERNAME }}
        password: ${{ secrets.CAQUANTBOOSTAPI_REGISTRY_PASSWORD }}

    # --- STEP 2: Build and Push Image with Conditional Cache ---
    # This is the most important step. We use the official Docker action
    # which gives us full control over the build process.
    - name: Build and push image
      uses: docker/build-push-action@v5
      with:
        context: ./QuantBoost_API # The path to your API's source code
        file: ${{ env.DOCKERFILE_PATH }}
        push: true
        tags: ${{ env.REGISTRY_URL }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        no-cache: ${{ github.event.inputs.force_rebuild == 'true' }} # Conditional cache based on input

    # --- STEP 3: Deploy Existing Image to Container App ---
    # Now we use the Azure action, but instead of building, we tell it
    # to deploy the image we just pushed to the registry.
    - name: Deploy to Azure Container App
      uses: azure/container-apps-deploy-action@v1
      with:
        containerAppName: ${{ env.CONTAINER_APP_NAME }}
        resourceGroup: ${{ env.RESOURCE_GROUP }}
        imageToDeploy: ${{ env.REGISTRY_URL }}/${{ env.IMAGE_NAME }}:${{ github.sha }}

    - name: Deployment Summary
      run: |
        echo "🚀 API deployment completed successfully!"
        echo "📦 Image: ${{ env.REGISTRY_URL }}/${{ env.IMAGE_NAME }}:${{ github.sha }}"
        echo "🏗️ Container App: ${{ env.CONTAINER_APP_NAME }}"
        echo "📍 Resource Group: ${{ env.RESOURCE_GROUP }}"
        echo "🌍 Environment: ${{ github.event.inputs.environment }}"
        echo "📝 Deploy Reason: ${{ github.event.inputs.deploy_reason }}"
        echo "👤 Deployed by: ${{ github.actor }}"
        echo "🔄 Force rebuild: ${{ github.event.inputs.force_rebuild }}"
        echo "🔗 Check deployment status in the Azure portal."