import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(req: NextRequest) {
  // Create Supabase client inside the function to avoid build-time issues
  const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_KEY!);
  try {
    const { userId, teamId } = await req.json();

    if (!userId || !teamId) {
      return NextResponse.json({ error: 'Missing userId or teamId' }, { status: 400 });
    }

    const { error } = await supabase
      .from('team_members')
      .delete()
      .eq('user_id', userId)
      .eq('team_id', teamId);

    if (error) {
      throw error;
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing team member:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}