import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Team License: only these allow adjustable_quantity
const TEAM_PRICE_IDS = [
  'price_1RC3MPE6FvhUKV1bki8tfo6G',    // Team Annual
  'price_1RCQgVE6FvhUKV1beXWkrsDI',    // Team Quarterly
];

export async function POST(req: NextRequest) {
  // Create Stripe client inside the function to avoid build-time issues
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-03-31.basil',
  });

  // Create Supabase client for database operations
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );

  try {
    const { priceId, userId, email, quantity = 1 } = await req.json();

    if (!priceId) {
      return NextResponse.json({ error: 'Missing priceId' }, { status: 400 });
    }

    // Check if this is a Team license price
    const isTeamPlan = TEAM_PRICE_IDS.includes(priceId);

    // Find or create Stripe customer
    let stripeCustomerId: string | undefined;

    if (userId && email) {
      // Check if user already has a Stripe customer ID
      const { data: profile } = await supabase
        .from('profiles')
        .select('stripe_customer_id')
        .eq('id', userId)
        .maybeSingle();

      if (profile?.stripe_customer_id) {
        stripeCustomerId = profile.stripe_customer_id;
      } else {
        // Create new Stripe customer
        const customer = await stripe.customers.create({
          email: email,
          metadata: {
            user_id: userId,
          },
        });
        stripeCustomerId = customer.id;

        // Update profile with Stripe customer ID
        await supabase
          .from('profiles')
          .update({ stripe_customer_id: stripeCustomerId })
          .eq('id', userId);
      }
    }

    // Prepare line item
    const lineItem: Stripe.Checkout.SessionCreateParams.LineItem = {
      price: priceId,
      quantity: 1,
    };

    if (isTeamPlan) {
      lineItem['adjustable_quantity'] = {
        enabled: true,
        minimum: 1,
        maximum: 50, // set as high/low as you wish
      };
    }

    // Build Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      client_reference_id: userId ?? undefined,
      customer: stripeCustomerId, // Link to existing/new Stripe customer
      metadata: {
        ...(email ? { email } : {}),
        ...(userId ? { user_id: userId } : {})
      },
      line_items: [lineItem],
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/dashboard?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/pricing`,
    });

    return NextResponse.json({ sessionId: session.id, url: session.url });
  } catch (error) {
    console.error('Error creating Stripe Checkout session:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}