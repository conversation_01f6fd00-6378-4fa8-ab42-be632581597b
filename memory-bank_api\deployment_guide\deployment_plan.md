### **Project: QuantBoost Add-in Installer Deployment (Full Production Plan)**

**Objective:** Create a single, professionally signed `.exe` bootstrapper that installs the QuantBoost VSTO add-ins for both PowerPoint and Excel. The entire process, from infrastructure provisioning to the final build, will be automated, secure, and repeatable.

---

### **Phase 1: Infrastructure & Project Foundation**

This phase establishes our cloud infrastructure and prepares the add-in projects for a release build.

*   **1. Provision Azure Infrastructure via Terraform**
    *   **1.1.** Execute the `main.tf` and `variables.tf` configuration files using the Terraform CLI (`terraform init`, `terraform plan`, `terraform apply`).
    *   **1.2.** This action will automatically provision the following critical resources in Azure:
        *   A dedicated Resource Group (`rg-quantboost-distribution-prod`) to house all distribution and signing assets.
        *   An Azure Storage Account and a `releases` Blob Container to host the installer file.
        *   An Azure CDN Profile and Endpoint to provide fast, global downloads.
        *   An **Azure Code Signing Account** (`csa-quantboost-prod`), our new managed service for signing.
        *   An **Azure Certificate Profile** (`csp-prod-publictrust`), the specific public-trust certificate we will use to sign our installer.

*   **2. Complete Manual Code Signing Validation (One-Time Human Task)**
    *   **2.1.** Using the output from the Terraform apply, navigate to the newly created **Azure Code Signing Account** in the Azure Portal.
    *   **2.2.** Select the **Certificate Profile** (`csp-prod-publictrust`).
    *   **2.3.** Initiate and complete the one-time organization identity validation process. This is a manual step required by the Certificate Authority to verify QuantBoost Inc. and may take several business days. **Signing will fail until this validation is complete.**

*   **3. Finalize VSTO Add-in Build Configuration**
    *   **3.1.** In the Visual Studio solution, ensure both VSTO projects (`QuantBoost.Excel` and `QuantBoost.PowerPoint`) are configured to build in `Release` mode.
        *   **3.1.1.** In the `.csproj` files for the `Release` configuration, confirm compiler optimizations are enabled (`<Optimize>true</Optimize>`).
        *   **3.1.2.** Confirm debugging symbols are not included for the release build (`<DebugType>none</DebugType>`).
    *   **3.2.** Perform a "Clean Solution" and then "Rebuild Solution" in Visual Studio to generate fresh, optimized release artifacts.

*   **4. Implement and Verify Versioning Strategy**
    *   **4.1.** Adhere to Semantic Versioning. For this soft-launch, set the version to `0.1.0`.
    *   **4.2.** Update the assembly and file versions in the `.csproj` files for both VSTO projects:
        ```xml
        <PropertyGroup>
          <AssemblyVersion>0.1.0.0</AssemblyVersion>
          <FileVersion>0.1.0.0</FileVersion>
        </PropertyGroup>
        ```

*   **5. Externalize and Verify Configuration**
    *   **5.1.** Confirm that no sensitive data or environment-specific URLs are hardcoded in the add-in source code.
    *   **5.2.** Ensure the `appsettings.json` file, containing settings like the API endpoint, is configured to be included in the project output by setting its `Copy to Output Directory` property to `Copy if newer`.

### **Phase 2: Installer Project Setup (WiX Toolset)**

This phase creates the installer project within our Visual Studio solution.

*   **1. Install WiX Toolset Prerequisites**
    *   **1.1.** Install the WiX Toolset build tools.
    *   **1.2.** Install the "WiX Toolset" Visual Studio extension.

*   **2. Create the WiX Setup Project**
    *   **2.1.** In the QuantBoost solution, add a new `Setup Project for VSTO` (or a standard `Setup Project`).
    *   **2.2.** Name the project `QuantBoost.Installer`.
    *   **2.3.** Add project references from `QuantBoost.Installer` to both `QuantBoost.Excel` and `QuantBoost.PowerPoint` to establish the correct build order.

### **Phase 3: Building the Installer Payload & Logic (WiX)**

This phase involves configuring the `Product.wxs` file to define the installer's contents and behavior.

*   **1. Define Core Product Information**
    *   **1.1.** In `Product.wxs`, configure the `<Product>` element:
        *   **Id:** A new, static GUID for this product version.
        *   **Name:** "QuantBoost for Office"
        *   **Language:** "1033" (English-US)
        *   **Version:** "0.1.0"
        *   **Manufacturer:** "QuantBoost Inc."
        *   **UpgradeCode:** A new, static GUID that will **never change** across future versions to ensure smooth upgrades.

*   **2. Define Prerequisite Checks (Bootstrapper)**
    *   **2.1.** Create a `Bundle.wxs` file to define the `.exe` bootstrapper.
    *   **2.2.** In the `<Chain>` element, define the required runtimes. This ensures they are installed if missing on the user's machine.
        ```xml
        <Chain>
            <!-- .NET Desktop Runtime (Update version as needed for your add-in's target framework) -->
            <PackageGroupRef Id="NetCoreDesktopRuntime" /> 
            
            <!-- VSTO Runtime -->
            <PackageGroupRef Id="VSTORuntime" /> 

            <!-- Our main MSI package -->
            <MsiPackage SourceFile="$(var.QuantBoost.Installer.TargetPath)" />
        </Chain>
        ```
    *   **2.3.** Add references to `WixNetFxExtension` and `WixUtilExtension` in the installer project.

*   **3. Define File and Component Structure**
    *   **3.1.** Define the installation directory (e.g., `[ProgramFilesFolder]\QuantBoost`).
    *   **3.2.** Create a `<ComponentGroup>` for all `QuantBoost.Excel` files (`.dll`, `.vsto`, `.dll.manifest`, `appsettings.json`, etc.).
    *   **3.3.** Create a separate `<ComponentGroup>` for all `QuantBoost.PowerPoint` files.

*   **4. Configure VSTO Registry Keys for Office Integration**
    *   **4.1.** Create a dedicated `<Component>` for the Excel add-in's registry keys. These keys tell Excel how to load the add-in.
        ```xml
        <Component Id="Registry.Excel" Guid="[NEW-GUID-HERE]">
            <RegistryKey Root="HKCU" Key="Software\Microsoft\Office\Excel\Addins\QuantBoost.Excel">
                <RegistryValue Name="Description" Value="QuantBoost Excel Add-in" Type="string" />
                <RegistryValue Name="FriendlyName" Value="QuantBoost" Type="string" />
                <RegistryValue Name="LoadBehavior" Value="3" Type="integer" />
                <RegistryValue Name="Manifest" Value="[#QuantBoost.Excel.vsto]" Type="string" />
            </RegistryKey>
        </Component>
        ```
    *   **4.2.** Create a similar `<Component>` for the PowerPoint add-in's registry keys.

*   **5. Define Features and UI**
    *   **5.1.** In `Product.wxs`, create a `<Feature>` that references all the ComponentGroups and ComponentRefs from the previous steps.
    *   **5.2.** Reference a standard WiX UI library, such as `WixUI_InstallDir`, to provide a professional installation wizard.
    *   **5.3.** Create a `License.rtf` file containing the End-User License Agreement and reference it in the `Bundle.wxs`.

### **Phase 4: Finalization, Signing, and Building**

This phase brings everything together to produce the final, signed executable.

*   **1. Integrate Azure Code Signing into the Build Process**
    *   **1.1.** This step will be configured in the project's CI/CD pipeline (e.g., Azure DevOps YAML or GitHub Actions workflow). It will execute *after* the WiX project has successfully built the unsigned `QuantBoost.exe`.
    *   **1.2.** The pipeline will use the **AzureSignTool** (.NET Global Tool) to sign the installer.
    *   **1.3.** The command will be configured with the following parameters, retrieved from our Terraform outputs:
        *   The path to the unsigned `QuantBoost.exe`.
        *   Azure Code Signing Account Name: `csa-quantboost-prod`
        *   Certificate Profile Name: `csp-prod-publictrust`
        *   Azure Tenant ID.
    *   **1.4.** **Authorization:** The Service Principal used by the CI/CD pipeline must be granted the **"Code Signing Certificate Profile Signer"** IAM role on the Certificate Profile resource in Azure.

*   **2. Build the Final Installer**
    *   **2.1.** Set the solution's build configuration to `Release`.
    *   **2.2.** Execute the build for the `QuantBoost.Installer` project.
    *   **2.3.** The pipeline will then execute the AzureSignTool step, transforming the unsigned `.exe` into a fully signed, trusted installer.

### **Phase 5: Quality Assurance & Distribution**

This final phase ensures the installer is flawless and gets into the hands of our beta testers.

*   **1. Perform Rigorous QA Testing**
    *   **1.1.** On a **clean virtual machine** (e.g., Windows Sandbox), execute the final signed `QuantBoost.exe`.
    *   **1.2.** **Test Case: Fresh Install.** Verify that all prerequisites are handled, files are installed correctly, and registry keys are created. Launch Excel and PowerPoint to confirm the add-ins load and function as expected.
    *   **1.3.** **Test Case: Clean Uninstall.** Use "Add or remove programs" to uninstall the application. Verify that all files, directories, and registry keys are completely removed.
    *   **1.4.** **Test Case: Upgrade (Future).** For the next version (e.g., `0.1.1`), confirm that running the new installer performs a smooth, in-place upgrade over the existing `0.1.0` version.

*   **2. Upload the Signed Installer for Distribution**
    *   **2.1.** As a final step in the CI/CD pipeline, upload the signed `QuantBoost.exe`.
    *   **2.2.** The destination will be the Azure Blob Storage container (`releases`) that was provisioned by our Terraform script. The exact storage account name is available in the `upload_instructions` Terraform output.

*   **3. Prepare for Beta Distribution**
    *   **3.1.** The primary download link for the website and all communications will be the custom domain URL from our Terraform output: **`https://download.quantboost.ai/QuantBoost.exe`**.
    *   **3.2.** Draft a welcome email for beta testers that includes the download link, instructions, and a clear channel for providing feedback.