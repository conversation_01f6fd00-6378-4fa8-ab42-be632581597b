"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import {
  <PERSON>ton,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui";
import { apiClient, isApiSuccess, handleApiError } from '@/lib/api';
import Stripe from 'stripe';

interface BillingOverview {
  nextBillingDate?: string;
  billingInterval?: string;
  totalSpent: number;
  invoiceCount: number;
  lastPaymentDate?: string;
  lastPaymentAmount?: number;
}

export default function BillingPage() {
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState<Stripe.Invoice[]>([]);
  const [overview, setOverview] = useState<BillingOverview | null>(null);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabaseClient();

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('User not authenticated');
        return;
      }

      // Get user's Stripe customer ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('stripe_customer_id')
        .eq('id', user.id)
        .single();

      if (profileError || !profileData?.stripe_customer_id) {
        setError('No billing information found. Please ensure you have an active subscription.');
        return;
      }

      // Fetch invoices
      const invoicesResponse = await fetch('/api/billing/invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ customerId: profileData.stripe_customer_id }),
      });

      if (!invoicesResponse.ok) {
        throw new Error('Failed to fetch invoices');
      }

      const invoiceData = await invoicesResponse.json();
      setInvoices(invoiceData);

      // Calculate overview
      const totalSpent = invoiceData.reduce((sum: number, invoice: Stripe.Invoice) => 
        sum + (invoice.amount_paid || 0), 0
      );

      const lastPaidInvoice = invoiceData
        .filter((invoice: Stripe.Invoice) => invoice.status === 'paid')
        .sort((a: Stripe.Invoice, b: Stripe.Invoice) => b.created - a.created)[0];

      // Get next billing date from subscriptions
      const subscriptionsResponse = await apiClient.getUserSubscriptions();
      let nextBillingDate: string | undefined;
      let billingInterval: string | undefined;

      if (isApiSuccess(subscriptionsResponse)) {
        const activeSubscription = subscriptionsResponse.data.find(sub => 
          sub.status === 'active' || sub.status === 'trialing'
        );
        if (activeSubscription) {
          nextBillingDate = activeSubscription.current_period_end;
          // Extract billing interval from plan_id if available
          billingInterval = activeSubscription.plan_id.includes('month') ? 'monthly' : 
                           activeSubscription.plan_id.includes('year') ? 'yearly' : 'unknown';
        }
      }

      setOverview({
        nextBillingDate,
        billingInterval,
        totalSpent: totalSpent / 100, // Convert from cents
        invoiceCount: invoiceData.length,
        lastPaymentDate: lastPaidInvoice?.created ? new Date(lastPaidInvoice.created * 1000).toISOString() : undefined,
        lastPaymentAmount: lastPaidInvoice?.amount_paid ? lastPaidInvoice.amount_paid / 100 : undefined,
      });

    } catch (error) {
      console.error('Error fetching billing data:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setLoading(true);
      const response = await apiClient.createBillingPortalSession(window.location.href);
      if (isApiSuccess(response)) {
        window.location.href = response.data.url;
      } else {
        setError(handleApiError(response, 'Failed to create billing portal session'));
      }
    } catch (error) {
      console.error('Error managing subscription:', error);
      setError('Failed to redirect to billing portal');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'open':
        return 'bg-yellow-100 text-yellow-800';
      case 'void':
        return 'bg-gray-100 text-gray-800';
      case 'uncollectible':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Billing History</h3>
          <p className="text-sm text-muted-foreground">Loading billing information...</p>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-6 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Billing History</h3>
          <p className="text-sm text-muted-foreground">
            View your billing information and invoice history.
          </p>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Billing Data</CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={fetchBillingData} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Billing History</h3>
        <p className="text-sm text-muted-foreground">
          View your billing information and invoice history.
        </p>
      </div>

      {/* Billing Overview */}
      {overview && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Next Billing Date</CardTitle>
              <span className="text-2xl">📅</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview.nextBillingDate 
                  ? new Date(overview.nextBillingDate).toLocaleDateString()
                  : 'N/A'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                {overview.billingInterval || 'Unknown interval'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <span className="text-2xl">💰</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${overview.totalSpent.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                Across {overview.invoiceCount} invoice{overview.invoiceCount !== 1 ? 's' : ''}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Last Payment</CardTitle>
              <span className="text-2xl">💳</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {overview.lastPaymentAmount ? `$${overview.lastPaymentAmount.toFixed(2)}` : 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                {overview.lastPaymentDate 
                  ? new Date(overview.lastPaymentDate).toLocaleDateString()
                  : 'No payments yet'
                }
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Manage Billing</CardTitle>
              <span className="text-2xl">⚙️</span>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={handleManageSubscription}
                className="w-full"
                size="sm"
              >
                Billing Portal
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>Invoice History</CardTitle>
          <CardDescription>
            View and download your past invoices.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {invoices.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">No invoices found.</p>
              <p className="text-xs text-muted-foreground mt-1">
                Invoices will appear here once you have an active subscription.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-medium">
                      {invoice.number || `Invoice ${invoice.id?.slice(-8) || 'unknown'}`}
                    </TableCell>
                    <TableCell>
                      {new Date(invoice.created * 1000).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      ${((invoice.amount_paid || invoice.amount_due || 0) / 100).toFixed(2)}
                    </TableCell>
                    <TableCell>
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(invoice.status || 'unknown')}`}>
                        {invoice.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {invoice.invoice_pdf && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(invoice.invoice_pdf!, '_blank')}
                          >
                            Download PDF
                          </Button>
                        )}
                        {invoice.hosted_invoice_url && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(invoice.hosted_invoice_url!, '_blank')}
                          >
                            View Online
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}