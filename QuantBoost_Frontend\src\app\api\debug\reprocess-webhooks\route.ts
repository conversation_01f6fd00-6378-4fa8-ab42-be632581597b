// Debug endpoint to reprocess recent webhook events
// This helps fix missing subscription data after webhook processing issues

import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

export async function POST(req: NextRequest) {
  // Only allow in development/staging
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-03-31.basil',
  });

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );

  try {
    const { eventIds } = await req.json();
    
    if (!eventIds || !Array.isArray(eventIds)) {
      return NextResponse.json({ error: 'Missing eventIds array' }, { status: 400 });
    }

    const results = [];

    for (const eventId of eventIds) {
      try {
        // Retrieve the event from Stripe
        const event = await stripe.events.retrieve(eventId);
        
        if (event.type === 'checkout.session.completed') {
          const session = event.data.object as Stripe.Checkout.Session;
          
          if (session.mode === 'subscription' && session.customer) {
            // Get customer details
            const customer = await stripe.customers.retrieve(session.customer as string);

            if (typeof customer === 'object' && !customer.deleted && customer.email) {
              // Find profile by email
              const { data: profile } = await supabase
                .from('profiles')
                .select('id, stripe_customer_id')
                .eq('email', customer.email)
                .maybeSingle();

              if (profile && !profile.stripe_customer_id) {
                // Update profile with Stripe customer ID
                const { error: updateError } = await supabase
                  .from('profiles')
                  .update({ stripe_customer_id: session.customer as string })
                  .eq('id', profile.id);

                if (!updateError) {
                  results.push({
                    eventId,
                    action: 'updated_profile',
                    profileId: profile.id,
                    email: customer.email,
                    stripeCustomerId: session.customer
                  });
                } else {
                  results.push({
                    eventId,
                    action: 'error',
                    error: updateError.message
                  });
                }
              } else if (profile) {
                results.push({
                  eventId,
                  action: 'already_linked',
                  profileId: profile.id,
                  stripeCustomerId: profile.stripe_customer_id
                });
              } else {
                results.push({
                  eventId,
                  action: 'no_profile_found',
                  email: customer.email
                });
              }
            }
          }
        } else if (event.type === 'customer.subscription.created') {
          const subscription = event.data.object as Stripe.Subscription;
          
          // Get customer details
          const customer = await stripe.customers.retrieve(subscription.customer as string);

          if (typeof customer === 'object' && !customer.deleted && customer.email) {
            // Check if subscription already exists
            const { data: existingSub } = await supabase
              .from('subscriptions')
              .select('id')
              .eq('stripe_subscription_id', subscription.id)
              .maybeSingle();

            if (!existingSub) {
              // Find profile by email and update with customer ID if needed
              const { data: profile } = await supabase
                .from('profiles')
                .select('id, stripe_customer_id')
                .eq('email', customer.email)
                .maybeSingle();

              if (profile) {
                // Update customer ID if missing
                if (!profile.stripe_customer_id) {
                  await supabase
                    .from('profiles')
                    .update({ stripe_customer_id: subscription.customer as string })
                    .eq('id', profile.id);
                }

                // Create subscription record
                const firstItem = subscription.items.data[0];
                const { data: subData, error: subError } = await supabase
                  .from('subscriptions')
                  .insert({
                    user_id: profile.id,
                    stripe_subscription_id: subscription.id,
                    status: subscription.status,
                    quantity: firstItem?.quantity ?? 1,
                    current_period_start: new Date(firstItem?.current_period_start * 1000).toISOString(),
                    current_period_end: new Date(firstItem?.current_period_end * 1000).toISOString(),
                    trial_start_date: subscription.trial_start ? new Date(subscription.trial_start * 1000).toISOString() : null,
                    trial_end_date: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
                    cancel_at_period_end: subscription.cancel_at_period_end,
                  })
                  .select('id')
                  .single();

                if (!subError) {
                  results.push({
                    eventId,
                    action: 'created_subscription',
                    subscriptionId: subData.id,
                    profileId: profile.id
                  });
                } else {
                  results.push({
                    eventId,
                    action: 'error',
                    error: subError.message
                  });
                }
              }
            } else {
              results.push({
                eventId,
                action: 'subscription_exists',
                subscriptionId: existingSub.id
              });
            }
          }
        }
      } catch (error) {
        results.push({
          eventId,
          action: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({ 
      success: true, 
      processed: results.length,
      results 
    });

  } catch (error) {
    console.error('Error reprocessing webhooks:', error);
    return NextResponse.json({ 
      error: 'Internal Server Error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
