import Stripe from 'stripe';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createHash, randomBytes } from 'crypto';

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private readonly stripe: Stripe;
  private readonly webhookSecret: string;
  private readonly rateLimit = new Map<string, number[]>();
  
  // Security configuration
  private readonly ALLOWED_CUSTOMER_DOMAINS = [
    'gmail.com', 'outlook.com', 'company.com'
    // Add your allowed domains
  ];

  private readonly BLOCKED_EMAILS = [
    '<EMAIL>',
    '<EMAIL>',
    'noreply@',
    'admin@'
  ];

  constructor(private configService: ConfigService) {
    this.stripe = new Stripe(this.configService.get<string>('STRIPE_SECRET_KEY'), {
      apiVersion: '2023-10-16',
    });
    this.webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET');
  }

  /**
   * Validates webhook signature to prevent request forgery
   */
  validateWebhookSignature(payload: string | Buffer, signature: string): Stripe.Event {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        this.webhookSecret
      );
      
      this.logger.log(`Webhook signature validated for event: ${event.type}`);
      return event;
    } catch (err) {
      this.logger.error(`Webhook signature verification failed: ${err.message}`);
      throw new Error(`Webhook signature verification failed: ${err.message}`);
    }
  }

  /**
   * Validates customer email against security policies
   */
  validateCustomerEmail(email: string): boolean {
    // Block known test/guest emails
    if (this.BLOCKED_EMAILS.some(blocked => email.includes(blocked))) {
      throw new Error(`Blocked email pattern: ${email}`);
    }
    
    // Validate domain whitelist (optional)
    const domain = email.split('@')[1];
    if (this.ALLOWED_CUSTOMER_DOMAINS.length > 0 && !this.ALLOWED_CUSTOMER_DOMAINS.includes(domain)) {
      this.logger.warn(`Unusual domain detected: ${domain}`);
    }
    
    return true;
  }

  /**
   * Rate limiting for Stripe API operations
   */
  checkRateLimit(customerId: string, eventType: string): void {
    const key = `${customerId}:${eventType}`;
    const now = Date.now();
    const windowMs = 60000; // 1 minute
    const maxRequests = 5;
    
    if (!this.rateLimit.has(key)) {
      this.rateLimit.set(key, []);
    }
    
    const requests = this.rateLimit.get(key);
    const recentRequests = requests.filter(time => now - time < windowMs);
    
    if (recentRequests.length >= maxRequests) {
      throw new Error(`Rate limit exceeded for ${key}`);
    }
    
    recentRequests.push(now);
    this.rateLimit.set(key, recentRequests);
  }

  /**
   * Generate idempotency key for Stripe requests
   */
  generateIdempotencyKey(customerId: string, amount: number, currency: string): string {
    const timestamp = Date.now();
    const data = `${customerId}-${amount}-${currency}-${timestamp}`;
    return createHash('sha256').update(data).digest('hex').substring(0, 32);
  }

  /**
   * Create payment intent with idempotency and security checks
   */
  async createPaymentIntent(params: {
    amount: number;
    currency: string;
    customerId: string;
    customerEmail: string;
    metadata?: Record<string, string>;
  }): Promise<Stripe.PaymentIntent> {
    try {
      // Security validations
      this.validateCustomerEmail(params.customerEmail);
      this.checkRateLimit(params.customerId, 'payment_intent');

      // Generate idempotency key
      const idempotencyKey = this.generateIdempotencyKey(
        params.customerId,
        params.amount,
        params.currency
      );

      const paymentIntentParams: Stripe.PaymentIntentCreateParams = {
        amount: params.amount,
        currency: params.currency,
        customer: params.customerId,
        metadata: {
          ...params.metadata,
          security_validated: 'true',
          validation_timestamp: new Date().toISOString(),
        },
        automatic_payment_methods: {
          enabled: true,
        },
      };

      const paymentIntent = await this.stripe.paymentIntents.create(
        paymentIntentParams,
        {
          idempotencyKey,
        }
      );

      this.logger.log(`Payment intent created: ${paymentIntent.id} for customer: ${params.customerId}`);
      return paymentIntent;
    } catch (error) {
      this.logger.error(`Failed to create payment intent: ${error.message}`);
      await this.sendSecurityAlert({
        severity: 'medium',
        details: `Payment intent creation failed: ${error.message}`,
        actions: ['Logged error', 'Customer notified'],
        customerId: params.customerId,
      });
      throw error;
    }
  }

  /**
   * Create subscription with enhanced security
   */
  async createSubscription(params: {
    customerId: string;
    priceId: string;
    customerEmail: string;
    trialPeriodDays?: number;
    metadata?: Record<string, string>;
  }): Promise<Stripe.Subscription> {
    try {
      // Security validations
      this.validateCustomerEmail(params.customerEmail);
      this.checkRateLimit(params.customerId, 'subscription');

      // Generate idempotency key
      const idempotencyKey = this.generateIdempotencyKey(
        params.customerId,
        0, // subscriptions don't have fixed amounts
        'subscription'
      );

      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: params.customerId,
        items: [{ price: params.priceId }],
        trial_period_days: params.trialPeriodDays,
        metadata: {
          ...params.metadata,
          security_validated: 'true',
          validation_timestamp: new Date().toISOString(),
        },
        expand: ['latest_invoice.payment_intent'],
      };

      const subscription = await this.stripe.subscriptions.create(
        subscriptionParams,
        {
          idempotencyKey,
        }
      );

      this.logger.log(`Subscription created: ${subscription.id} for customer: ${params.customerId}`);
      return subscription;
    } catch (error) {
      this.logger.error(`Failed to create subscription: ${error.message}`);
      await this.sendSecurityAlert({
        severity: 'medium',
        details: `Subscription creation failed: ${error.message}`,
        actions: ['Logged error', 'Customer notified'],
        customerId: params.customerId,
      });
      throw error;
    }
  }

  /**
   * Create or retrieve customer with security validation
   */
  async createOrRetrieveCustomer(params: {
    email: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<Stripe.Customer> {
    try {
      // Security validations
      this.validateCustomerEmail(params.email);
      this.checkRateLimit(params.email, 'customer_create');

      // Check if customer already exists
      const existingCustomers = await this.stripe.customers.list({
        email: params.email,
        limit: 1,
      });

      if (existingCustomers.data.length > 0) {
        this.logger.log(`Existing customer found: ${existingCustomers.data[0].id}`);
        return existingCustomers.data[0] as Stripe.Customer;
      }

      // Generate idempotency key for new customer
      const idempotencyKey = this.generateIdempotencyKey(
        params.email,
        0,
        'customer'
      );

      const customerParams: Stripe.CustomerCreateParams = {
        email: params.email,
        name: params.name,
        metadata: {
          ...params.metadata,
          security_validated: 'true',
          validation_timestamp: new Date().toISOString(),
        },
      };

      const customer = await this.stripe.customers.create(
        customerParams,
        {
          idempotencyKey,
        }
      );

      this.logger.log(`New customer created: ${customer.id}`);
      return customer;
    } catch (error) {
      this.logger.error(`Failed to create/retrieve customer: ${error.message}`);
      await this.sendSecurityAlert({
        severity: 'low',
        details: `Customer creation failed: ${error.message}`,
        actions: ['Logged error'],
        customerId: params.email,
      });
      throw error;
    }
  }

  /**
   * Anomaly detection for suspicious patterns
   */
  detectAnomalies(recentEvents: Stripe.Event[]): string[] {
    const alerts: string[] = [];

    const suspiciousPatterns = {
      rapidCancellations: (events: Stripe.Event[]) => {
        const cancellations = events.filter(e => e.type.includes('canceled'));
        return cancellations.length > 3;
      },
      
      guestEmails: (events: Stripe.Event[]) => {
        return events.some(e => e.data?.object?.['email'] === '<EMAIL>');
      },
      
      unusualAmounts: (events: Stripe.Event[]) => {
        const amounts = events.map(e => e.data?.object?.['amount'] || 0);
        return amounts.some(amount => amount !== 12000); // $120 expected
      }
    };

    Object.entries(suspiciousPatterns).forEach(([pattern, detector]) => {
      if (detector(recentEvents)) {
        alerts.push(`Suspicious pattern detected: ${pattern}`);
      }
    });

    if (alerts.length > 0) {
      this.logger.warn(`Anomalies detected: ${alerts.join(', ')}`);
    }

    return alerts;
  }

  /**
   * Send security alert to monitoring system
   */
  async sendSecurityAlert(incident: {
    severity: 'low' | 'medium' | 'high' | 'critical';
    details: string;
    actions: string[];
    customerId?: string;
  }): Promise<void> {
    const alert = {
      severity: incident.severity,
      type: 'stripe_security_incident',
      details: incident.details,
      timestamp: new Date().toISOString(),
      actions_taken: incident.actions,
      customer_id: incident.customerId,
    };

    try {
      // Send to monitoring system if webhook URL is configured
      const webhookUrl = this.configService.get<string>('SECURITY_WEBHOOK_URL');
      if (webhookUrl) {
        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(alert),
        });
      }

      // Log to security audit trail
      this.logger.error(`SECURITY_INCIDENT: ${JSON.stringify(alert)}`);
    } catch (error) {
      this.logger.error(`Failed to send security alert: ${error.message}`);
    }
  }

  /**
   * Process webhook events with comprehensive security checks
   */
  async processWebhookEvent(event: Stripe.Event): Promise<void> {
    try {
      this.logger.log(`Processing webhook event: ${event.type}`);

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event);
          break;
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event);
          break;
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event);
          break;
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event);
          break;
        default:
          this.logger.log(`Unhandled webhook event type: ${event.type}`);
      }

      // Check for anomalies after processing
      const recentEvents = [event]; // In real implementation, fetch recent events
      const anomalies = this.detectAnomalies(recentEvents);
      if (anomalies.length > 0) {
        await this.sendSecurityAlert({
          severity: 'medium',
          details: `Anomalies detected: ${anomalies.join(', ')}`,
          actions: ['Logged anomalies', 'Reviewing patterns'],
        });
      }
    } catch (error) {
      this.logger.error(`Failed to process webhook event: ${error.message}`);
      await this.sendSecurityAlert({
        severity: 'high',
        details: `Webhook processing failed: ${error.message}`,
        actions: ['Logged error', 'Manual review required'],
      });
      throw error;
    }
  }

  /**
   * Handle successful payment intent
   */
  private async handlePaymentIntentSucceeded(event: Stripe.Event): Promise<void> {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    this.logger.log(`Payment succeeded: ${paymentIntent.id}`);
    
    // Add business logic here (e.g., fulfill order, update database)
    // Ensure idempotency by checking if already processed
  }

  /**
   * Handle failed payment intent
   */
  private async handlePaymentIntentFailed(event: Stripe.Event): Promise<void> {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    this.logger.warn(`Payment failed: ${paymentIntent.id}`);
    
    await this.sendSecurityAlert({
      severity: 'low',
      details: `Payment failed for intent: ${paymentIntent.id}`,
      actions: ['Customer notified', 'Retry logic triggered'],
      customerId: paymentIntent.customer as string,
    });
  }

  /**
   * Handle subscription creation
   */
  private async handleSubscriptionCreated(event: Stripe.Event): Promise<void> {
    const subscription = event.data.object as Stripe.Subscription;
    this.logger.log(`Subscription created: ${subscription.id}`);
    
    // Add business logic here (e.g., provision access, update user account)
  }

  /**
   * Handle subscription updates
   */
  private async handleSubscriptionUpdated(event: Stripe.Event): Promise<void> {
    const subscription = event.data.object as Stripe.Subscription;
    this.logger.log(`Subscription updated: ${subscription.id}`);
    
    // Handle subscription changes (e.g., plan changes, status updates)
  }

  /**
   * Handle subscription deletion
   */
  private async handleSubscriptionDeleted(event: Stripe.Event): Promise<void> {
    const subscription = event.data.object as Stripe.Subscription;
    this.logger.log(`Subscription deleted: ${subscription.id}`);
    
    // Revoke access, clean up resources
    await this.sendSecurityAlert({
      severity: 'low',
      details: `Subscription cancelled: ${subscription.id}`,
      actions: ['Access revoked', 'Customer notified'],
      customerId: subscription.customer as string,
    });
  }

  /**
   * Handle successful invoice payment
   */
  private async handleInvoicePaymentSucceeded(event: Stripe.Event): Promise<void> {
    const invoice = event.data.object as Stripe.Invoice;
    this.logger.log(`Invoice payment succeeded: ${invoice.id}`);
    
    // Handle successful recurring payment
  }

  /**
   * Handle failed invoice payment
   */
  private async handleInvoicePaymentFailed(event: Stripe.Event): Promise<void> {
    const invoice = event.data.object as Stripe.Invoice;
    this.logger.warn(`Invoice payment failed: ${invoice.id}`);
    
    await this.sendSecurityAlert({
      severity: 'medium',
      details: `Invoice payment failed: ${invoice.id}`,
      actions: ['Customer notified', 'Dunning process initiated'],
      customerId: invoice.customer as string,
    });
  }

  /**
   * Get secure API keys from Azure Key Vault
   */
  async getSecureApiKey(): Promise<string> {
    try {
      // In production, integrate with Azure Key Vault
      // const { SecretClient } = require("@azure/keyvault-secrets");
      // const { DefaultAzureCredential } = require("@azure/identity");
      
      // const credential = new DefaultAzureCredential();
      // const vaultName = "quantboost-keyvault";
      // const url = `https://${vaultName}.vault.azure.net`;
      // const client = new SecretClient(url, credential);
      
      // const secret = await client.getSecret("stripe-secret-key");
      // return secret.value;
      
      // For now, return from environment
      return this.configService.get<string>('STRIPE_SECRET_KEY');
    } catch (error) {
      this.logger.error(`Failed to retrieve secure API key: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rotate API keys (placeholder for automated rotation)
   */
  async rotateApiKeys(environment: string): Promise<void> {
    this.logger.log(`Starting key rotation for ${environment}`);
    
    try {
      // 1. Generate new keys in Stripe Dashboard (manual process)
      // 2. Update Azure Key Vault with new keys
      // 3. Deploy to environment
      // 4. Verify functionality
      // 5. Revoke old keys
      // 6. Send notification
      
      this.logger.log(`Key rotation completed for ${environment}`);
    } catch (error) {
      this.logger.error(`Key rotation failed: ${error.message}`);
      await this.sendSecurityAlert({
        severity: 'critical',
        details: `Key rotation failed for ${environment}: ${error.message}`,
        actions: ['Manual intervention required', 'Security team notified'],
      });
      throw error;
    }
  }
}