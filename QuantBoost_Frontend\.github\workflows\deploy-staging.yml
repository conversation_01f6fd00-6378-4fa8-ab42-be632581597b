name: Deploy Frontend to Azure App Service (Staging)

on:
  workflow_dispatch:  # Manual trigger only
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      deploy_reason:
        description: 'Reason for deployment'
        required: false
        default: 'Manual deployment'
        type: string
      skip_build:
        description: 'Skip build and deploy existing artifacts'
        required: false
        default: false
        type: boolean

env:
  AZURE_WEBAPP_NAME: app-quantboost-frontend-staging
  AZURE_WEBAPP_PACKAGE_PATH: './QuantBoost_Frontend'
  NODE_VERSION: '18'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    name: Build and Deploy Job

    steps:
    - name: Deployment Info
      run: |
        echo "🚀 Manual frontend deployment triggered"
        echo "📍 Environment: ${{ github.event.inputs.environment }}"
        echo "📝 Reason: ${{ github.event.inputs.deploy_reason }}"
        echo "👤 Triggered by: ${{ github.actor }}"
        echo "📦 Deploying commit: ${{ github.sha }}"
        echo "⏭️ Skip build: ${{ github.event.inputs.skip_build }}"

    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      if: github.event.inputs.skip_build != 'true'
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'QuantBoost_Frontend/package-lock.json'

    - name: Install dependencies
      if: github.event.inputs.skip_build != 'true'
      run: |
        cd QuantBoost_Frontend
        npm ci

    - name: Build application
      if: github.event.inputs.skip_build != 'true'
      run: |
        cd QuantBoost_Frontend
        npm run build
      env:
        # Build-time environment variables
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        NEXT_PUBLIC_BASE_URL: "https://app-quantboost-frontend-staging.azurewebsites.net"

    - name: Prepare deployment package
      if: github.event.inputs.skip_build != 'true'
      run: |
        cd QuantBoost_Frontend
        # Remove development dependencies and files to reduce package size
        rm -rf node_modules/.cache || true
        rm -rf .next/cache || true
        rm -rf coverage || true
        rm -rf .nyc_output || true
        
        # Remove unnecessary files
        find . -name "*.test.js" -delete || true
        find . -name "*.test.ts" -delete || true
        find . -name "*.test.tsx" -delete || true
        find . -name "*.spec.js" -delete || true
        find . -name "*.spec.ts" -delete || true
        find . -name "*.spec.tsx" -delete || true
        
        echo "📦 Package prepared for deployment"
        du -sh . || true

    - name: Deploy to Azure App Service
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_STAGING }}
        package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}

    - name: Deployment Summary
      run: |
        echo "🚀 Frontend deployment completed successfully!"
        echo "🌍 Environment: ${{ github.event.inputs.environment }}"
        echo "📝 Deploy Reason: ${{ github.event.inputs.deploy_reason }}"
        echo "👤 Deployed by: ${{ github.actor }}"
        echo "🏗️ App Service: ${{ env.AZURE_WEBAPP_NAME }}"
        echo "⏭️ Build skipped: ${{ github.event.inputs.skip_build }}"
        echo "🔗 Check deployment at: https://app-quantboost-frontend-staging.azurewebsites.net"