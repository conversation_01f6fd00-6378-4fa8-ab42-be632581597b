'use client';

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button } from '@/components/ui';
import Link from 'next/link';

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [email, setEmail] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>('');
  const [isSuccess, setIsSuccess] = useState(false);
  const supabase = useSupabaseClient();

  useEffect(() => {
    const emailParam = searchParams.get('email');
    const paymentIntentId = searchParams.get('payment_intent');
    const autoLoginFailed = searchParams.get('auto_login_failed');

    if (emailParam) {
      setEmail(emailParam);
    }

    // Check if user is already authenticated
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        // User is already logged in, redirect to dashboard
        router.push('/dashboard?payment=success');
        return;
      }

      // If auto-login failed, show appropriate message
      if (autoLoginFailed === 'true') {
        setMessage('Automatic login encountered an issue. Please use the manual login option below.');
        return;
      }

      // If we have email and payment intent, attempt automatic login (legacy flow)
      if (emailParam && paymentIntentId) {
        await attemptAutomaticLogin(emailParam, paymentIntentId);
      }
    };

    checkAuth();
  }, [searchParams, router]);

  const attemptAutomaticLogin = async (email: string, paymentIntentId: string) => {
    try {
      setIsLoading(true);
      setMessage('Logging you in automatically...');

      // Set a timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        setMessage('Taking longer than expected. Please use the manual login option below.');
        setIsLoading(false);
      }, 10000); // 10 second timeout

      // Try the new direct authentication API first
      const directResponse = await fetch('/api/auth/post-payment-direct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          paymentIntentId,
        }),
      });

      const directData = await directResponse.json();
      clearTimeout(timeoutId);

      // If direct auth successful, set session and redirect
      if (directResponse.ok && directData.directAuth && directData.session) {
        setMessage('Authentication successful! Redirecting to dashboard...');
        
        // Set the session in Supabase client
        const { error: sessionError } = await supabase.auth.setSession({
          access_token: directData.session.access_token,
          refresh_token: directData.session.refresh_token
        });

        if (sessionError) {
          console.error('Error setting session:', sessionError);
          throw new Error('Failed to establish session');
        }

        console.log('✅ Direct authentication successful, redirecting to dashboard');
        
        // Small delay to show success message
        setTimeout(() => {
          router.push(`/dashboard?payment=success&email=${encodeURIComponent(email)}&payment_intent=${paymentIntentId}`);
        }, 1000);
        
        return;
      }

      // Fallback to magic link approach
      console.log('Direct auth not available, falling back to magic link');
      const response = await fetch('/api/auth/post-payment-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          paymentIntentId,
        }),
      });

      const data = await response.json();

      if (response.ok && data.loginUrl) {
        if (data.redirectToDashboard) {
          // New auto-login flow - redirect directly to magic link for automatic authentication
          setMessage('Redirecting to your dashboard...');
          window.location.href = data.loginUrl;
        } else {
          // Legacy flow - redirect to magic link
          setMessage('Redirecting to your dashboard...');
          window.location.href = data.loginUrl;
        }
      } else {
        // If automatic login fails, fall back to manual magic link
        console.warn('Automatic login failed:', data.error);
        setMessage('Please use the login option below to access your dashboard.');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Automatic login error:', error);
      setMessage('Please use the login option below to access your dashboard.');
      setIsLoading(false);
    }
  };

  const handleSendMagicLink = async () => {
    if (!email) {
      setMessage('Email address is required.');
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
          shouldCreateUser: true, // Allow user creation since payment was successful
          emailRedirectTo: `${window.location.origin}/dashboard?payment=success`
        }
      });

      if (error) {
        console.error('Magic link error:', error);
        
        // Handle the specific "Signups not allowed for otp" error
        if (error.message.includes('Signups not allowed')) {
          setMessage('Account setup in progress. Please try the automated login above or contact support.');
        } else {
          setMessage(error.message || 'Failed to send magic link. Please try again.');
        }
      } else {
        setIsSuccess(true);
        setMessage('Success! Check your email for a magic link to access your dashboard.');
      }
    } catch (error) {
      console.error('Unexpected magic link error:', error);
      setMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <span className="text-green-600 text-2xl">✅</span>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              Payment Successful!
            </CardTitle>
            <CardDescription className="text-gray-600">
              {isLoading && message.includes('automatically')
                ? 'Your payment has been processed successfully. We\'re logging you in automatically...'
                : 'Your payment has been processed successfully. To access your dashboard, please log in to your account.'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isLoading && message.includes('automatically') ? (
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
                <p className="text-gray-600">{message}</p>
                <p className="text-sm text-gray-500">This should only take a moment...</p>
              </div>
            ) : !isSuccess ? (
              <>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your email address"
                    disabled={isLoading}
                  />
                </div>

                {message && (
                  <div className={`p-4 rounded-md ${
                    message.includes('Success') || message.includes('Check your email')
                      ? 'bg-green-50 border border-green-200 text-green-700'
                      : 'bg-red-50 border border-red-200 text-red-700'
                  }`}>
                    <p className="text-sm">{message}</p>
                  </div>
                )}

                <Button
                  onClick={handleSendMagicLink}
                  disabled={isLoading || !email}
                  className="w-full"
                >
                  {isLoading ? 'Sending...' : 'Send Magic Link to Access Dashboard'}
                </Button>

                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Already have access?{' '}
                    <Link href="/auth/login" className="font-medium text-blue-600 hover:text-blue-500">
                      Sign in here
                    </Link>
                  </p>
                </div>
              </>
            ) : (
              <div className="text-center space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-green-700">{message}</p>
                </div>
                <p className="text-sm text-gray-600">
                  Didn't receive the email?{' '}
                  <button
                    onClick={handleSendMagicLink}
                    className="font-medium text-blue-600 hover:text-blue-500"
                    disabled={isLoading}
                  >
                    Send again
                  </button>
                </p>
                <Link href="/auth/login">
                  <Button variant="outline" className="w-full">
                    Back to Login
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  );
}
