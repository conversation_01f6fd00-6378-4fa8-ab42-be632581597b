---
title: Known Issues & TODOs for QuantBoost_Frontend
purpose: To list known bugs, TODOs, FIXMEs, and areas needing attention in the QuantBoost_Frontend project.
projects: ["QuantBoost_Frontend"]
source_analysis: Codebase analysis of QuantBoost_Frontend
status: bootstrapped-incomplete
last_updated: 2025-05-13T00:00:00.000Z
tags: ["frontend", "issues", "todos", "bugs"]
---

## QuantBoost_Frontend Known Issues & TODOs

This document lists issues, TODOs, and areas for improvement identified during the codebase analysis of the QuantBoost_Frontend project.

### Critical TODOs / Incomplete Features

1.  **Implement Trial Start Logic (`src/app/start-trial/page.tsx`):**
    *   **Location:** `src/app/start-trial/page.tsx`
    *   **Description:** The entire backend and API interaction for starting a new trial is marked as a `TODO`. This includes:
        *   Email validation.
        *   Calling an API endpoint (e.g., `/api/auth/start-trial`).
        *   Checking if the email already exists in Supabase.
        *   Creating a new user account in Supabase.
        *   Potentially creating a Stripe customer and a trial subscription.
        *   Redirecting the user to the dashboard or login page.
    *   **Impact:** Core feature (trial signup) is not functional.

2.  **Implement User-Facing Error Handling for Stripe Checkout (`src/app/pricing/page.tsx`):**
    *   **Location:** `src/app/pricing/page.tsx` (within `handleBuyNow` function)
    *   **Description:** Comments `// Handle error - show message to user?` indicate that if the Stripe checkout session creation fails (either in the frontend or the backend API call), there is no user-facing message to inform them of the problem.
    *   **Impact:** Poor user experience if payment initiation fails.

### Content & Configuration Issues

3.  **Placeholder YouTube Demo URLs (`src/app/features/*`):**
    *   **Location:**
        *   `src/app/features/slide-size-analyzer/page.tsx`
        *   `src/app/features/keyboard-shortcuts/page.tsx`
        *   `src/app/features/excel-trace/page.tsx`
        *   `src/app/features/clean-excel/page.tsx`
    *   **Description:** All feature pages use placeholder YouTube URLs like `https://www.youtube.com/embed/your_slide_size_analyzer_demo`.
    *   **Impact:** Demo videos are not functional.

4.  **Embedded Interactive Demos from Macabacus.com (`src/app/features/*`):**
    *   **Location:** Same feature pages as above.
    *   **Description:** Feature pages embed iframes from `macabacus.com` for "Interactive Demos". The URL `https://macabacus.com/features/link-excel-to-powerpoint` is used for multiple, different features (Slide Size Analyzer, Keyboard Shortcuts, Excel Trace, Clean Excel).
    *   **Impact:**
        *   Dependency on an external, potentially competitor, website.
        *   The same generic demo URL is used for different features, which is likely incorrect or misleading.
        *   Content could change or become unavailable.
    *   **Recommendation:** Create custom interactive demos or find more appropriate, stable sources.

### Code Structure & Refactoring Opportunities

5.  **Duplicate Page Components in `src/app/page.tsx`:**
    *   **Location:** `src/app/page.tsx` defines `DownloadPage`, `HelpCenterPage`, `TrainingPage`, and `PricingPage` components.
    *   **Description:** Dedicated route files also exist for these pages (e.g., `src/app/pricing/page.tsx`, `src/app/help/page.tsx`). This leads to two versions of these pages, with the ones in `src/app/page.tsx` being simpler and likely outdated compared to the more functional versions in their specific route directories (e.g., the pricing page in `src/app/pricing/page.tsx` has Stripe integration, the one in `src/app/page.tsx` does not).
    *   **Impact:** Code duplication, confusion, potential for inconsistent content.
    *   **Recommendation:** Remove the page components (`DownloadPage`, `HelpCenterPage`, `TrainingPage`, `PricingPage`) from `src/app/page.tsx`. The `HomePage` component should remain. Functionality should be consolidated into the `page.tsx` files within their respective route directories (e.g., all pricing logic in `src/app/pricing/page.tsx`).

### Potential Minor Issues / Areas for Review

6.  **Login/Signup Flow:**
    *   While Supabase auth is set up, the actual login/signup UI pages/components were not explicitly part of this review. These should be verified for completeness and user experience.
7.  **Middleware Logic (`middleware.ts`):**
    *   The content of `middleware.ts` was not reviewed. Its logic for route protection, redirects, or header manipulation is important and should be verified.
8.  **API Endpoint Robustness (`/api/checkout/create-session`):**
    *   The code for this Next.js API route was not reviewed. Its error handling, security (e.g., validating inputs, ensuring user is authenticated before creating a session), and interaction with Supabase/Stripe are critical.
