"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSupabaseClient } from '../../hooks/useSupabaseClient';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button } from "@/components/ui";
import { apiClient, Subscription, isApiSuccess, handleApiError } from '@/lib/api';
import Link from 'next/link';

interface DashboardStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  totalLicenses: number;
  activeLicenses: number;
  assignedLicenses: number;
  availableLicenses: number;
}

function DashboardContent() {
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<{ email?: string } | null>(null);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    totalLicenses: 0,
    activeLicenses: 0,
    assignedLicenses: 0,
    availableLicenses: 0,
  });
  const [error, setError] = useState<string | null>(null);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const supabase = useSupabaseClient();

  // Check for payment success parameter
  useEffect(() => {
    const paymentStatus = searchParams.get('payment');
    if (paymentStatus === 'success') {
      setShowPaymentSuccess(true);
      // Auto-hide the success message after 5 seconds
      setTimeout(() => setShowPaymentSuccess(false), 5000);
    }
  }, [searchParams]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          // Check if this is a post-payment redirect
          const paymentStatus = searchParams.get('payment');
          if (paymentStatus === 'success') {
            // Redirect to payment success page for authentication
            window.location.href = '/auth/payment-success';
            return;
          }
          throw new Error('User not authenticated');
        }
        setUser(user);

        // Fetch subscriptions from Azure API
        const subscriptionsResponse = await apiClient.getUserSubscriptions();
        if (!isApiSuccess(subscriptionsResponse)) {
          const errorMsg = handleApiError(subscriptionsResponse, 'Failed to fetch subscriptions');
          setError(errorMsg);
          return;
        }

        const userSubscriptions = subscriptionsResponse.data;
        setSubscriptions(userSubscriptions);

        // Calculate stats
        const activeSubscriptions = userSubscriptions.filter(sub => 
          sub.status === 'active' || sub.status === 'trialing'
        );

        let totalLicenses = 0;
        let activeLicenses = 0;
        let assignedLicenses = 0;

        // Fetch licenses for each subscription
        for (const subscription of userSubscriptions) {
          const licensesResponse = await apiClient.getTeamLicenses(subscription.id);
          if (isApiSuccess(licensesResponse)) {
            const licenses = licensesResponse.data;
            totalLicenses += licenses.length;
            activeLicenses += licenses.filter(l => l.status === 'active').length;
            assignedLicenses += licenses.filter(l => l.status === 'assigned').length;
          }
        }

        setStats({
          totalSubscriptions: userSubscriptions.length,
          activeSubscriptions: activeSubscriptions.length,
          totalLicenses,
          activeLicenses,
          assignedLicenses,
          availableLicenses: totalLicenses - activeLicenses - assignedLicenses,
        });

      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [supabase]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">Loading your account overview...</p>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Loading...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <p className="text-muted-foreground">Welcome back!</p>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Dashboard</CardTitle>
            <CardDescription className="text-red-600">{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Payment Success Notification */}
      {showPaymentSuccess && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800 flex items-center gap-2">
              <span>✅</span>
              Payment Successful!
            </CardTitle>
            <CardDescription className="text-green-600">
              Your payment has been processed successfully. Your subscription is now active.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => setShowPaymentSuccess(false)}
              variant="outline"
              className="border-green-300 text-green-700 hover:bg-green-100"
            >
              Dismiss
            </Button>
          </CardContent>
        </Card>
      )}

      <div>
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <p className="text-muted-foreground">
          Welcome back{user?.email ? `, ${user.email}` : ''}!
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <span className="text-2xl">💳</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              of {stats.totalSubscriptions} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Licenses</CardTitle>
            <span className="text-2xl">🔑</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeLicenses}</div>
            <p className="text-xs text-muted-foreground">
              of {stats.totalLicenses} total licenses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned Licenses</CardTitle>
            <span className="text-2xl">👥</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.assignedLicenses}</div>
            <p className="text-xs text-muted-foreground">
              awaiting activation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Licenses</CardTitle>
            <span className="text-2xl">🆓</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.availableLicenses}</div>
            <p className="text-xs text-muted-foreground">
              ready to assign
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for managing your account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link href="/dashboard/team">
              <Button className="w-full justify-start" variant="outline">
                <span className="mr-2">👥</span>
                Manage Team Members
              </Button>
            </Link>
            <Link href="/dashboard/subscription">
              <Button className="w-full justify-start" variant="outline">
                <span className="mr-2">💳</span>
                View Subscription Details
              </Button>
            </Link>
            <Link href="/dashboard/billing">
              <Button className="w-full justify-start" variant="outline">
                <span className="mr-2">📄</span>
                Billing History
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Subscriptions</CardTitle>
            <CardDescription>
              Your current subscription plans
            </CardDescription>
          </CardHeader>
          <CardContent>
            {subscriptions.length === 0 ? (
              <p className="text-sm text-muted-foreground">No active subscriptions</p>
            ) : (
              <div className="space-y-3">
                {subscriptions.slice(0, 3).map((subscription) => (
                  <div key={subscription.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{subscription.plan_id}</p>
                      <p className="text-xs text-muted-foreground">
                        {subscription.quantity} seat{subscription.quantity !== 1 ? 's' : ''}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        subscription.status === 'active' 
                          ? 'bg-green-100 text-green-800'
                          : subscription.status === 'trialing'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {subscription.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DashboardContent />
    </Suspense>
  );
}
