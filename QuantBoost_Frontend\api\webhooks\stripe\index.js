// Azure Static Web App API Route: /api/webhooks/stripe
// Handles Stripe webhook events for subscription management

const Stripe = require('stripe');
const { createClient } = require('@supabase/supabase-js');

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-03-31.basil',
});

// Initialize Supabase admin client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY,
  {
    auth: { persistSession: false }
  }
);

// Webhook signing secret
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Utility functions
function toIsoDate(secOrMs) {
  if (!secOrMs) return null;
  return new Date(secOrMs * 1000).toISOString();
}

function generateLicenseKey() {
  return require('crypto').randomUUID();
}

module.exports = async function (context, req) {
  context.log('🚀 Stripe webhook function called');
  // Set CORS headers for webhook endpoint
  context.res = {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type, stripe-signature',
    }
  };

  if (req.method !== 'POST') {
    context.res.status = 405;
    context.res.body = 'Method not allowed';
    return;
  }

  // Get raw body and signature
  const rawBody = req.rawBody || req.body;
  const signature = req.headers['stripe-signature'];

  if (!signature) {
    context.log.error('❌ Missing Stripe signature header');
    context.res.status = 400;
    context.res.body = 'Missing Stripe signature';
    return;
  }

  let event;
  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(rawBody, signature, webhookSecret);
    context.log(`✅ Stripe event verified: ${event.type}, ID: ${event.id}`);
  } catch (err) {
    context.log.error('❌ Stripe signature verification failed:', err.message);
    context.res.status = 400;
    context.res.body = `Webhook signature verification failed: ${err.message}`;
    return;
  }

  try {
    // Check for duplicate events (idempotency protection)
    const { data: existingEvent } = await supabase
      .from('webhook_events')
      .select('id')
      .eq('stripe_event_id', event.id)
      .maybeSingle();

    if (existingEvent) {
      context.log(`⚠️ Duplicate event detected, skipping: ${event.id}`);
      context.res.status = 200;
      context.res.body = JSON.stringify({ received: true, message: 'Event already processed' });
      return;
    }

    // Log the event for tracking
    const { data: eventLog, error: eventLogError } = await supabase
      .from('webhook_events')
      .insert({
        stripe_event_id: event.id,
        event_type: event.type,
        processed_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (eventLogError) {
      context.log.error('Failed to log webhook event:', eventLogError);
      context.res.status = 500;
      context.res.body = 'Failed to log event';
      return;
    }

    // Process the event based on type
    await processStripeEvent(context, event);

    // Update event log as successfully processed
    await supabase
      .from('webhook_events')
      .update({ 
        status: 'processed',
        processed_at: new Date().toISOString()
      })
      .eq('id', eventLog.id);

    context.res.status = 200;
    context.res.body = JSON.stringify({ received: true });

  } catch (error) {
    context.log.error('❌ Webhook processing error:', error);
    
    // Update event log with error status
    if (eventLog?.id) {
      await supabase
        .from('webhook_events')
        .update({ 
          status: 'failed',
          error_message: error.message,
          processed_at: new Date().toISOString()
        })
        .eq('id', eventLog.id);
    }

    context.res.status = 500;
    context.res.body = 'Webhook processing failed';
  }
};

async function processStripeEvent(context, event) {
  switch (event.type) {
    case 'checkout.session.completed': {
      context.log('👉 Handling checkout.session.completed');
      const session = event.data.object;
      
      if (session.mode !== 'subscription') {
        context.log('Skipping non-subscription checkout session');
        break;
      }

      // Get customer email from session
      const customerEmail = session.customer_details?.email || session.customer_email;
      if (!customerEmail) {
        context.log.error('No customer email found in checkout session');
        break;
      }

      // Find or create profile
      let { data: profile, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', customerEmail)
        .maybeSingle();

      if (error) {
        context.log.error('Error searching for profile:', error);
        break;
      }

      if (!profile) {
        // Create new profile
        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert({
            email: customerEmail,
            stripe_customer_id: session.customer,
            created_at: new Date().toISOString(),
          })
          .select('id')
          .single();

        if (createError) {
          context.log.error('Error creating profile:', createError);
          break;
        }
        profile = newProfile;
      } else {
        // Update existing profile with Stripe customer ID
        await supabase
          .from('profiles')
          .update({ stripe_customer_id: session.customer })
          .eq('id', profile.id);
      }

      context.log(`✅ Checkout session processed for customer: ${customerEmail}`);
      break;
    }

    case 'customer.subscription.created':
    case 'customer.subscription.updated': {
      context.log(`👉 Handling ${event.type}`);
      const subscription = event.data.object;
      const firstItem = subscription.items.data[0];

      // Find profile by Stripe customer ID
      let { data: profile, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', subscription.customer)
        .maybeSingle();

      if (error) {
        context.log.error('Error searching for profile:', error);
        break;
      }

      if (!profile) {
        context.log.error(`No profile found for Stripe customer: ${subscription.customer}`);
        break;
      }

      // Upsert subscription data
      const subscriptionData = {
        id: subscription.id,
        user_id: profile.id,
        stripe_subscription_id: subscription.id,
        stripe_customer_id: subscription.customer,
        status: subscription.status,
        price_id: firstItem?.price?.id,
        quantity: firstItem?.quantity || 1,
        current_period_start: toIsoDate(subscription.current_period_start),
        current_period_end: toIsoDate(subscription.current_period_end),
        trial_start_date: toIsoDate(subscription.trial_start),
        trial_end_date: toIsoDate(subscription.trial_end),
        cancel_at_period_end: subscription.cancel_at_period_end,
        canceled_at: toIsoDate(subscription.canceled_at),
        created_at: toIsoDate(subscription.created),
        updated_at: new Date().toISOString(),
      };

      const { data: subData, error: subError } = await supabase
        .from('subscriptions')
        .upsert(subscriptionData, { onConflict: 'id' })
        .select('id, quantity')
        .single();

      if (subError) {
        context.log.error('Error upserting subscription:', subError);
        break;
      }

      // Handle license creation/updates
      const newQuantity = subData.quantity;
      const { count: existingCount } = await supabase
        .from('licenses')
        .select('*', { count: 'exact', head: true })
        .eq('subscription_id', subData.id);

      if (existingCount < newQuantity) {
        // Create additional licenses
        const licensesToCreate = Array.from({ length: newQuantity - existingCount }, () => ({
          subscription_id: subData.id,
          license_key: generateLicenseKey(),
          status: subscription.status === 'active' || subscription.status === 'trialing' ? 'active' : 'inactive',
          created_at: new Date().toISOString(),
        }));

        const { error: licenseError } = await supabase
          .from('licenses')
          .insert(licensesToCreate);

        if (licenseError) {
          context.log.error('Error creating licenses:', licenseError);
        } else {
          context.log(`✅ Created ${licensesToCreate.length} new licenses`);
        }
      }

      // Update license statuses based on subscription status
      const statusMap = {
        active: 'active',
        trialing: 'active',
        canceled: 'canceled',
        unpaid: 'inactive',
        past_due: 'inactive',
        incomplete: 'inactive'
      };
      const newLicenseStatus = statusMap[subscription.status] || 'inactive';

      await supabase
        .from('licenses')
        .update({ status: newLicenseStatus })
        .eq('subscription_id', subData.id);

      context.log(`✅ Subscription ${event.type} processed: ${subscription.id}`);
      break;
    }

    case 'customer.subscription.deleted': {
      context.log('👉 Handling customer.subscription.deleted');
      const subscription = event.data.object;
      
      const { data: subData, error: subError } = await supabase
        .from('subscriptions')
        .update({ status: 'canceled' })
        .eq('stripe_subscription_id', subscription.id)
        .select('id')
        .single();

      if (subError) {
        context.log.error('Error updating subscription as canceled:', subError);
        break;
      }

      if (subData?.id) {
        await supabase
          .from('licenses')
          .update({ status: 'canceled' })
          .eq('subscription_id', subData.id);
      }

      context.log(`✅ Subscription deleted: ${subscription.id}`);
      break;
    }

    case 'invoice.paid': {
      context.log('👉 Handling invoice.paid');
      const invoice = event.data.object;

      if (!invoice.subscription) {
        context.log('Skipping non-subscription invoice');
        break;
      }

      // Validate this is for our QuantBoost product
      const validPriceIds = [
        'price_1RCQeBE6FvhUKV1bUN94Oihf', // Quarterly
        'price_1RC3HTE6FvhUKV1bE9D6zf6e'  // Annual
      ];

      const quantBoostLineItem = invoice.lines.data.find(item =>
        item.price && validPriceIds.includes(item.price.id)
      );

      if (!quantBoostLineItem) {
        context.log('Invoice is not for QuantBoost product, skipping');
        break;
      }

      // Update subscription status to active
      await supabase
        .from('subscriptions')
        .update({ 
          status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq('stripe_subscription_id', invoice.subscription);

      context.log(`✅ Invoice payment processed for subscription: ${invoice.subscription}`);
      break;
    }

    case 'invoice.payment_failed': {
      context.log('👉 Handling invoice.payment_failed');
      const invoice = event.data.object;

      if (!invoice.subscription) {
        context.log('Skipping non-subscription invoice');
        break;
      }

      // Update subscription status
      await supabase
        .from('subscriptions')
        .update({ 
          status: 'past_due',
          updated_at: new Date().toISOString()
        })
        .eq('stripe_subscription_id', invoice.subscription);

      context.log(`✅ Invoice payment failure processed for subscription: ${invoice.subscription}`);
      break;
    }

    default:
      context.log(`👀 Unhandled Stripe event type: ${event.type}`);
      break;
  }
}
