"use client";

import Link from 'next/link';

export default function HomePage() {
  return (
    <>
      {/* Fixed Top Nav */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b shadow-sm flex justify-between items-center px-6 py-3">
        <img src="/QuantBoost_LeftLogo_v0.png" alt="QuantBoost Logo" className="h-10" />
        {/* App Service Deployment Test */}
        <nav className="hidden md:flex gap-6">
          <div className="relative group">
            <button className="hover:text-emerald-600">Features ▾</button>
            <div className="absolute invisible opacity-0 scale-95 group-hover:visible group-hover:opacity-100 group-hover:scale-100 transition-all duration-200 origin-top bg-white border shadow rounded mt-2 w-48 text-left z-50">
              <a href="/features/excel-link" className="block px-4 py-2 hover:bg-gray-50">Excel Link</a>
              <a href="/features/excel-trace" className="block px-4 py-2 hover:bg-gray-50">Excel Trace</a>
              <a href="/features/worksheet-size-analyzer" className="block px-4 py-2 hover:bg-gray-50">Worksheet Size Analyzer</a>
              <a href="/features/slide-size-analyzer" className="block px-4 py-2 hover:bg-gray-50">Slide Size Analyzer</a>
              <a href="/features/clean-excel" className="block px-4 py-2 hover:bg-gray-50">Clean Excel</a>
              <a href="/features/keyboard-shortcuts" className="block px-4 py-2 hover:bg-gray-50">Keyboard Shortcut Manager</a>
            </div>
          </div>
          <div className="relative group">
            <button className="hover:text-emerald-600">Resources ▾</button>
            <div className="absolute invisible opacity-0 scale-95 group-hover:visible group-hover:opacity-100 group-hover:scale-100 transition-all duration-200 origin-top bg-white border shadow rounded mt-2 w-56 text-left z-50">
              <a href="/download" className="block px-4 py-2 hover:bg-gray-50">Download QuantBoost</a>
              <a href="/help" className="block px-4 py-2 hover:bg-gray-50">Help Center</a>
              <a href="/training" className="block px-4 py-2 hover:bg-gray-50">Training</a>
            </div>
          </div>
          <a href="/pricing" className="hover:text-emerald-600">Pricing</a>
        </nav>
        <div className="flex gap-3">
          <Link href="/auth/login" className="px-4 py-2 border rounded hover:bg-gray-50">Log In</Link>
          <a href="/start-trial" className="px-4 py-2 rounded bg-emerald-500 text-white hover:bg-emerald-600">Get Started</a>
        </div>
      </header>

      <main className="flex flex-col items-center justify-center min-h-screen p-6 pt-24">
        {/* Hero Section */}
        <section className="text-center py-12">
          <img src="/QuantBoost_Logo_v0.png" alt="QuantBoost Logo" className="mx-auto mb-4 max-h-48" />
          <p className="text-lg mb-6 max-w-2xl mx-auto">
            Unlock the full power of Excel and PowerPoint with QuantBoost's suite of productivity plug-ins.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/start-trial" className="px-6 py-3 rounded-lg bg-emerald-500 text-white hover:bg-emerald-600 transition">
              Get Started
            </a>
            <a href="/pricing" className="px-6 py-3 rounded-lg border border-emerald-500 text-emerald-600 hover:bg-emerald-50 transition">
              View Pricing
            </a>
          </div>
        </section>

        {/* Core Features Section */}
        <section className="py-12 max-w-6xl mx-auto border-t">
          <h2 className="text-center text-2xl font-bold mb-8">Everything You Need — Without the Bloat</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <a href="/features/excel-link" className="p-6 border rounded-lg transition transform hover:scale-105 hover:shadow-lg">
              <h3 className="font-semibold mb-2">🔗 Excel Link</h3>
              <p>Link Excel models to PowerPoint slides for instant updates and accuracy.</p>
            </a>
            <a href="/features/excel-trace" className="p-6 border rounded-lg transition transform hover:scale-105 hover:shadow-lg">
              <h3 className="font-semibold mb-2">🔍 Excel Trace</h3>
              <p>Trace complex formulas and dependencies to reduce errors fast.</p>
            </a>
            <a href="/features/worksheet-size-analyzer" className="p-6 border rounded-lg transition transform hover:scale-105 hover:shadow-lg">
              <h3 className="font-semibold mb-2">📊 Worksheet Size Analyzer</h3>
              <p>Identify and fix bloated Excel files to improve performance.</p>
            </a>
            <a href="/features/slide-size-analyzer" className="p-6 border rounded-lg transition transform hover:scale-105 hover:shadow-lg">
              <h3 className="font-semibold mb-2">📈 Slide Size Analyzer</h3>
              <p>Optimize PowerPoint decks by finding oversized images and content.</p>
            </a>
            <a href="/features/clean-excel" className="p-6 border rounded-lg transition transform hover:scale-105 hover:shadow-lg">
              <h3 className="font-semibold mb-2">🧹 Clean Excel</h3>
              <p>Remove hidden junk, fix formatting, and streamline your workbooks.</p>
            </a>
            <a href="/features/keyboard-shortcuts" className="p-6 border rounded-lg transition transform hover:scale-105 hover:shadow-lg">
              <h3 className="font-semibold mb-2">⌨️ Keyboard Shortcut Manager</h3>
              <p>Customize and manage 100+ Excel shortcuts to speed up your workflow.</p>
            </a>
          </div>
        </section>

        {/* Pricing Comparison Section */}
        <section className="py-12 max-w-5xl mx-auto border-t">
          <h2 className="text-center text-2xl font-bold mb-8">All the Essentials at a Fraction of the Price of the Competition</h2>
          <div className="flex flex-col md:flex-row items-center justify-center gap-6">
            <div className="p-6 border-2 border-emerald-500 rounded-lg text-center flex-1 w-70 flex flex-col justify-between">
              <h3 className="text-xl font-semibold mb-2">QuantBoost</h3>
              <p className="text-3xl font-bold mb-2">$120<span className="text-base font-normal">/year/license</span></p>
              <p className="whitespace-nowrap overflow-hidden">Key features professionals actually use.</p>
            </div>
            <div className="text-3xl font-bold text-gray-500">vs</div>
            <div className="p-6 border-2 border-blue-500 rounded-lg text-center flex-1 w-70 flex flex-col justify-between opacity-70">
              <h3 className="text-xl font-semibold mb-2">Macabacus</h3>
              <p className="text-3xl font-bold mb-2">$360+<span className="text-base font-normal">/year/license</span></p>
              <p className="whitespace-nowrap overflow-hidden">Loaded with features no one actually uses.</p>
            </div>
          </div>
          <p className="mt-6 text-center max-w-3xl mx-auto">By focusing on the features you actually care about, QuantBoost delivers incredible value without the clutter.</p>
        </section>

        {/* Trusted By Section (moved down) */}
        <section className="py-12 w-full border-t">
          <h2 className="text-center text-xl font-semibold mb-6">Trusted by professionals at</h2>
          <div className="flex flex-wrap justify-center gap-8 opacity-70">
            <img src="/company1.svg" alt="Company 1" className="h-8" />
            <img src="/company2.svg" alt="Company 2" className="h-8" />
            <img src="/company3.svg" alt="Company 3" className="h-8" />
            <img src="/company4.svg" alt="Company 4" className="h-8" />
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-12 max-w-4xl mx-auto border-t">
          <h2 className="text-center text-2xl font-bold mb-8">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="p-6 border rounded-lg">
              <p className="mb-2">"Quant Boost saves me hours every week and helps me avoid embarrassing errors."</p>
              <p className="font-semibold">— Sarah, Investment Banking Analyst</p>
            </div>
            <div className="p-6 border rounded-lg">
              <p className="mb-2">"Our presentations look polished and consistent, and the Excel tools are a game changer."</p>
              <p className="font-semibold">— James, Private Equity Associate</p>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-12 text-center border-t">
          <h2 className="text-2xl font-bold mb-4">Ready to boost your productivity?</h2>
          <a href="/start-trial" className="inline-block px-8 py-4 rounded-lg bg-emerald-500 text-white hover:bg-emerald-600 transition">
            Start Your Free Trial
          </a>
        </section>

        {/* Footer */}
        <footer className="py-8 text-center text-sm text-gray-500 border-t w-full">
          <p>&copy; 2025 QuantBoost. All rights reserved. Visit us at <a href="https://quantboost.ai" className="underline hover:text-emerald-600">quantboost.ai</a></p>
          <div className="mt-2 flex justify-center gap-4">
            <a href="/pricing" className="hover:underline">Pricing</a>
            <a href="/support" className="hover:underline">Support</a>
            <a href="/terms" className="hover:underline">Terms</a>
            <a href="/privacy" className="hover:underline">Privacy</a>
          </div>
        </footer>
      </main>
    </>
  );
}

