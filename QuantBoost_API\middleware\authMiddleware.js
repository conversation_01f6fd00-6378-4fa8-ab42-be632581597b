const crypto = require('crypto');
const { sendError } = require('../utils/responseHelpers');
const { supabase } = require('../supabaseClient'); // Added: require for supabaseClient

function authenticateApiKey(req, res, next) {
    const apiKey = req.headers['x-api-key'];
    const masterKey = process.env.MASTER_API_KEY;

    if (!apiKey || !masterKey) {
        // Log this event, as it's a server misconfiguration.
        console.error('[authMiddleware] MASTER_API_KEY is not set or no API key was provided.');
        return sendError(res, 'API Key Required', 401);
    }

    try {
        // Use crypto.timingSafeEqual for constant-time comparison to prevent timing attacks.
        // Both buffers must be of the same length.
        const keyBuffer = Buffer.from(apiKey, 'utf8');
        const masterKeyBuffer = Buffer.from(masterKey, 'utf8');

        if (keyBuffer.length !== masterKeyBuffer.length || !crypto.timingSafeEqual(keyBuffer, masterKeyBuffer)) {
            return sendError(res, 'Invalid API Key', 401);
        }

        // If the keys match, proceed.
        next();
    } catch (error) {
        console.error('[authMiddleware] Error during API key validation:', error);
        return sendError(res, 'Internal Server Error during authentication', 500);
    }
}

async function authenticateJWT(req, res, next) {
    const authHeader = req.headers.authorization;

    // 1. Check if the Authorization header exists and is correctly formatted.
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return sendError(res, 'Authorization header missing or malformed', 401);
    }

    const token = authHeader.split(' ')[1];

    try {
        // 2. Validate the token with Supabase.
        const { data: { user }, error } = await supabase.auth.getUser(token);

        // 3. If there's an error or no user is returned, the token is invalid. Reject the request.
        if (error || !user) {
            console.error('JWT Verification Error:', error ? error.message : 'No user found for token');
            return sendError(res, 'Invalid or expired token', 403); // Use 403 Forbidden
        }

        // 4. If the token is valid, attach the user object to the request and proceed.
        console.log(`[authMiddleware] SUCCESS: Validated token for user.id: ${user.id}, email: ${user.email}`); // <-- ADD THIS LINE
        req.user = user;
        next();
    } catch (err) {
        // Handle unexpected exceptions during the process.
        console.error('JWT Verification Exception:', err.message);
        return sendError(res, 'Token validation error', 500);
    }
}

module.exports = {
    authenticateApiKey,
    authenticateJWT // Added: export authenticateJWT
};
