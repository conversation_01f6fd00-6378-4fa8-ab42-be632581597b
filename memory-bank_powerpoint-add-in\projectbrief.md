---
title: Project Brief for QuantBoost Suite
project_name: QuantBoost_PPTX
projects:
  - powerpoint-add-in
  - excel-add-in
status: bootstrapping
purpose: Provides a high-level overview of the QuantBoost project, its goals, and its constituent parts.
source_analysis: Initial setup based on user input and project pitch.
last_updated: 2025-05-13T12:00:00Z
tags: ["Global", "Core", "Brief"]
---
## Project Pitch

QuantBoost powerpoint add-in is part of a suite of productivity software across Powerpoint and Excel. It is gated through a SAAS subscription model, and is modeled after the competing "Macabacus" company, however we offer our service at a discount to them, by focusing our product offering on only certain key features (cutting costs by not paying for features you never use). We are launching with Excel Link (links excel worksheets to powerpoint) and Analyze Powerpoint feature (which gives you a detailed report on file size of individual powerpoint slides) in the powerpoint add-in and Excel trace and Excel file size analyzer in the Excel add-in.

## Constituent Projects

*   **powerpoint-add-in:** Corresponds to the `QuantBoost_PPTX` VSTO solution. Handles features like Excel Link and PowerPoint file analysis. (Current focus for MB bootstrapping)
*   **excel-add-in:** Planned Excel add-in with features like Excel trace and file size analyzer. (Codebase not yet provided for analysis)