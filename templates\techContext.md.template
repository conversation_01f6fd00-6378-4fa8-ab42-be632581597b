# techContext.md - Technology Stack and Setup (Multi-Project Workspace)

**Purpose:** Details the specific technologies, libraries, tools, and setup procedures required for development **for each major subproject** within this workspace. Essential for onboarding and consistent development environments across components.

**Instructions for Dane (Initialization & Updates):**
*   During initialization, identify the main logical subprojects in the workspace based on directory structure and/or the Project Requirements Document (PRD).
*   For **each** identified subproject, create a distinct Level 2 Heading (`##`) section using a clear, descriptive name (e.g., `## Web Frontend (React)`, `## Primary API (Node.js)`, `## Data Processing Service (Python)`).
*   Populate the `subprojects` list in the YAML frontmatter with the identifiers used for the H2 headings.
*   Within each subproject's section, populate the subsections (Core Technologies, Dependencies, Setup, Tooling) with information *specific* to that subproject, gathered from the PRD, existing configuration files (e.g., `package.json`, `.csproj`, `requirements.txt`), or code analysis.
*   Use placeholders like `[Specify details]` where information is initially unknown but needs to be filled in.
*   If technologies or setup steps are truly shared across *multiple* subprojects, consider creating a `## Shared Workspace Context` section and reference it using Markdown links (e.g., `[See Shared Node.js Version](#shared-workspace-context)`) from the relevant subproject sections.
*   Reference specific manifest files using relative paths from the workspace root (e.g., `subproject-name/package.json`).
*   Be precise with versions and commands relevant to each subproject's context.
*   Update the `last_updated` field in the YAML frontmatter whenever significant changes are made to any section.

---

<!-- Optional Section for Shared Items - Create ONLY if truly shared elements exist -->
<!--
## Shared Workspace Context

### Shared Technologies/Tools
*   [e.g., Monorepo Tool: Specify Name/Version]
*   [e.g., Shared Language Runtime: Specify Name/Version]
*   [e.g., Global Docker Requirement: Specify Version]

### Shared Setup Steps
1.  [e.g., Install global prerequisites: List tools/versions]
2.  [e.g., Configure root `.env` file: List key shared variables]
-->

---

## [Subproject Name 1] ([Subproject Type, e.g., React Frontend])

<!-- #[Subproject1ID]Tech -->
### Core Technologies
*   **Languages:** [e.g., TypeScript 5.x, JavaScript ES2022+, HTML5, CSS3]
*   **Frameworks & Major Libraries:** [e.g., React 18.x, Vite 4.x, React Router 6.x]
*   **UI Library:** [e.g., Material UI v5, Tailwind CSS v3, None]
*   **State Management:** [e.g., Zustand, Redux Toolkit, Context API]
*   **Databases & Storage:** [e.g., LocalStorage, IndexedDB, None]

<!-- #[Subproject1ID]Dependencies -->
### Key Dependencies
*   **Package Management:** [e.g., npm 9.x, yarn 1.x, pip, NuGet]
*   **Manifest File(s):** `[path/to/subproject1/manifest.ext]` (e.g., `frontend/package.json`)
*   **Critical External Services Consumed:** [e.g., Primary API, Auth Service - reference other sections if defined]

<!-- #[Subproject1ID]Setup -->
### Development Environment Setup
1.  **Prerequisites:** [e.g., Node.js 18+, Specific Browser] ([Reference Shared Context if applicable])
2.  **Install Dependencies:** `cd [path/to/subproject1] && [command]` (e.g., `npm install`)
3.  **Environment Variables:** Create `[path/to/subproject1/.env]` from `[.../.env.example]`. Requires: `[List key variables]`. Sourced from: `[Where to find values]`.
4.  **Run Development Server:** `cd [path/to/subproject1] && [command]` (e.g., `npm run dev`)
5.  **Access Application:** `[URL:Port]` (e.g., `http://localhost:5173`)

<!-- #[Subproject1ID]Tooling -->
### Tooling
*   **Linters & Formatters:** [Tool Names] (Configs: `[path/to/config]`) - Run via `[command]`
*   **Build Tools:** [Tool Name] (Config: `[path/to/config]`) - Build via `[command]`
*   **Testing Frameworks:** [Framework Names] (Configs: `[path/to/config]`) - Test via `[command]`

---

## [Subproject Name 2] ([Subproject Type, e.g., Node.js Backend API])

<!-- #[Subproject2ID]Tech -->
### Core Technologies
*   **Languages:** [e.g., Node.js 18.x, TypeScript 5.x]
*   **Frameworks & Major Libraries:** [e.g., Express 4.x, NestJS 9.x, Fastify]
*   **Databases & Storage:** [e.g., PostgreSQL 15.x, MongoDB 6.x, Redis 7.x]
*   **ORM/Database Client:** [e.g., Prisma 5.x, TypeORM, node-postgres]

<!-- #[Subproject2ID]Dependencies -->
### Key Dependencies
*   **Package Management:** [e.g., npm 9.x, yarn 1.x]
*   **Manifest File(s):** `[path/to/subproject2/manifest.ext]` (e.g., `backend/package.json`)
*   **Critical External Services Consumed:** [e.g., Stripe SDK, AWS SDK, Third-party API X]

<!-- #[Subproject2ID]Setup -->
### Development Environment Setup
1.  **Prerequisites:** [e.g., Node.js 18+, Docker] ([Reference Shared Context if applicable])
2.  **Run Database/Cache (if needed):** `cd [path/to/subproject2] && [command]` (e.g., `docker-compose up -d db redis`)
3.  **Install Dependencies:** `cd [path/to/subproject2] && [command]` (e.g., `npm install`)
4.  **Environment Variables:** Create `[path/to/subproject2/.env]` from `[.../.env.example]`. Requires: `[List key variables]`. Sourced from: `[Where to find values]`.
5.  **Database Migrations (if needed):** `cd [path/to/subproject2] && [command]` (e.g., `npx prisma migrate dev`)
6.  **Run Development Server:** `cd [path/to/subproject2] && [command]` (e.g., `npm run start:dev`)
7.  **API Access:** `[URL:Port]` (e.g., `http://localhost:3000`)

<!-- #[Subproject2ID]Tooling -->
### Tooling
*   **Linters & Formatters:** [Tool Names] (Configs: `[path/to/config]`) - Run via `[command]`
*   **Build Tools:** [Tool Name, e.g., tsc] (Config: `[path/to/config]`) - Build via `[command]`
*   **Testing Frameworks:** [Framework Names, e.g., Jest, Mocha, Supertest] (Configs: `[path/to/config]`) - Test via `[command]`

---

<!-- Add more H2 sections for other identified subprojects as needed, following the structure above -->