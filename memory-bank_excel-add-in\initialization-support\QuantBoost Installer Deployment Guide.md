# Quant Boost Suite  
# Installer Packaging & Deployment Guide  
**Version:** 1.0  
**Date:** March 30, 2025  
  
---  
  
## Overview  
  
This document describes how to **build, configure, and distribute** the unified QuantBoost Suite installer, which deploys both the Excel and PowerPoint plug-ins, along with the shared licensing components. It covers developer build steps, IT deployment techniques, upgrade/uninstall considerations, and troubleshooting tips.  
  
The installer supports:  
  
- **Unified packaging:** installs all modules for both Office apps  
- **One-time activation:** user activates once, unlocks all features  
- **Silent, scripted, or interactive installs**  
- Flexibility for **bulk enterprise rollouts**  
  
---  
  
## Table of Contents  
  
1. [Packaging & Build Artifacts](#1-packaging--build-artifacts)  
2. [Installer Build Process](#2-installer-build-process)  
3. [Installer Configuration Options](#3-installer-configuration-options)  
4. [Testing the Installer](#4-testing-the-installer)  
5. [Silent & Enterprise Deployment](#5-silent--enterprise-deployment)  
6. [Upgrade & Uninstall](#6-upgrade--uninstall)  
7. [Prerequisites & Dependencies](#7-prerequisites--dependencies)  
8. [Troubleshooting](#8-troubleshooting)  
  
---  
  
## 1. Packaging & Build Artifacts  
  
### Components included  
  
| Component                                 | Purpose                                                                        |  
|------------------------------------------|--------------------------------------------------------------------------------|  
| **QuantBoostXL.dll / xl manifest**       | Excel plugin with multiple feature modules                                    |  
| **QuantBoostPPT.dll / ppt manifest**     | PowerPoint plugin with modules                                                |  
| **Licensing SDK & resources**            | Handles activation, validation, feature gating                                |  
| **Shared configs/assets**                | Images, templates, resources                                                  |  
| **Bootstrapper Executable (optional)**   | Wrapper EXE for prerequisites + MSI **(optional but recommended)**            |  
  
### Output  
  
- Preferred: **Single MSI (QuantBoostSuite_x.y.z.msi)** including multiple add-ins  
- Or, **bootstrapper EXE + MSI**  
  
### Signing  
  
- All DLLs must be **code signed** with Quant Boost trusted certificate  
- MSI and EXEs must be **signed** to avoid security prompts during install  
  
---  
  
## 2. Installer Build Process  
  
### 2.1 Authoring tool  
  
- WiX Toolset (preferred)  
- Advanced installer  
- Visual Studio Installer Projects (acceptable for early development)  
  
### 2.2 Build steps overview  
  
```plaintext
Build both add-ins →   
Prepare manifests →  
Bundle Licensing SDK + assets →  
Compose WiX/Installer project:  
    - Register Excel and PowerPoint VSTO add-ins  
    - Add pre-checks for prerequisites (VSTO runtime etc.)  
Sign all components →  
Build MSI →  
Test signed MSI on clean VM  
```  
  
### 2.3 Key WiX configurations  
  
- **Two Office add-in <COMAddin> registrations**  
- Add to both **Excel** and **PowerPoint** via:  
    - registry keys under `HKCU\Software\Microsoft\Office\<app>\Addins`  
- Set **LoadBehavior=3** (autoload enabled)  
- Bundle prerequisites or force install failure if missing  
  
---  
  
## 3. Installer Configuration Options  
  
| Option                     | Description                                                                            |  
|----------------------------|----------------------------------------------------------------------------------------|  
| `/qn`                      | Run silent install without UI (MSI switch)                                            |  
| `ALLUSERS=1`               | Per-machine install                                                                   |  
| `REINSTALL=ALL`            | Force reinstall all features                                                          |  
| `LICENSEKEY=XXXXX-YYYYY`   | (Optional) Pass license key during silent install (future enhancement)               |  
| `TELEMETRYOPTIN=1`         | (Optional) Set telemetry opt-in default                                               |  
  
_By default:_  
  
- Activations happen **after install**, **prompted inside the plugin UI**  
- User/license key stored in local user profile  
  
---  
  
## 4. Testing the Installer  
  
- Always **test on a clean VM** with only Office installed  
- Verify:  
  
  - Both Excel and PowerPoint load the plugins: check **Add-ins pane**  
  - User license prompts appear as expected  
  - Activation unlocks all modules  
  - Uninstall removes both add-ins cleanly  
  - Upgrade install preserves activation cache  
  
- Test **enterprise deployments**:  
  - Non-admin user installs  
  - Admin/IT silent install via SCCM/Intune  
  
---  
  
## 5. Silent & Enterprise Deployment  
  
### Silent installation  
  
```bash
msiexec /i QuantBoostSuite_x.y.z.msi /qn ALLUSERS=1 /norestart  
```  
  
- **/qn** = no UI  
- **ALLUSERS=1** installs for all users on machine (requires admin rights)  
  
### Deployment tools  
  
- SCCM, Intune, Group Policy Software Distribution  
- Wrap MSI in a **bootstrapper EXE (+ prerequisites check)** if end user experience preferred  
  
### Activation  
  
- Bulk activation currently **NOT** embedded in MSI  
- User will activate plug-in at first run  
- (Future) support license provisioning/token push for zero-touch deployment  
  
### Network Considerations  
  
- Ensure firewall/proxy permits access to `https://quantboost.ai/api/v1`  
  
---  
  
## 6. Upgrade & Uninstall  
  
### Upgrade  
  
- MSI installer supports **in-place upgrade**  
- Keep **same GUID** with **increased version number**  
- Existing activation/entitlements remain (cached license untouched)  
  
### Uninstall  
  
- Via **Control Panel → Programs** or  
```bash
msiexec /x QuantBoostSuite_x.y.z.msi  
```  
- Removes both add-ins and cache files under AppData/Roaming/QuantBoost  
  
---  
  
## 7. Prerequisites & Dependencies  
  
| Component                     | Required Version                   | Auto-installed or manual?   |  
|-------------------------------|------------------------------------|-----------------------------|  
| **MS Office/Office 365**     | 2016 or newer                      | User responsibility         |  
| **.NET Framework**            | 4.7.2+ or .NET 6+ if relevant      | Auto (Visual Studio redis)  |  
| **VSTO Runtime**              | Latest                             | MSI check or install        |  
| **VC++ Runtime**              | 2015-2019                         | MSI check                   |  
  
**Bootstrapper** should check and install dependencies as needed.  
  
---  
  
## 8. Troubleshooting  
  
| **Symptom**                                | **Likely Cause**                              | **Resolution**                                                                  |  
|--------------------------------------------|-----------------------------------------------|--------------------------------------------------------------------------------|  
| Add-in not visible in Excel/PPT            | Registry keys missing or Office blocked it    | Check Add-Ins manager → enable. Repair install.                                |  
| Activation prompt never appears            | Licensing SDK failed to load or init          | Look at plugin logs (%AppData%\QuantBoost\logs). Reinstall                     |  
| Activation fails due to connectivity       | Proxy/firewall/SaaS outage                    | Check API access, offline grace state                                          |  
| Office security warning                    | Missing/invalid digital signatures            | Re-sign DLLs & MSI, trusted publisher cert                                     |  
| Add-ins disabled after crash               | Office disabled plugins protection            | Re-enable manually in COM Add-ins dialog, fix startup exceptions               |  
  
---  
  
## Appendices  
  
### Common MSI Commands  
  
- Quiet install:  
```bash
msiexec /i QuantBoostSuite.msi /qn  
```  
  
- Uninstall:  
```bash
msiexec /x QuantBoostSuite.msi /qn  
```  
  
- Repair:  
```bash
msiexec /fa QuantBoostSuite.msi /qn  
```  
  
### Useful Registry Paths  
  
- **Excel Add-in:**  
`HKCU\Software\Microsoft\Office\Excel\Addins\QuantBoostXL`  
  
- **PowerPoint Add-in:**  
`HKCU\Software\Microsoft\Office\PowerPoint\Addins\QuantBoostPPT`  
  
Set **LoadBehavior=3** for always load.  
  
---  
  
# Summary  
  
- Build a **signed, unified MSI** to install both Excel & PPT plugins  
- License activation happens once, via plugin UI  
- Supports **interactive, silent, and bulk deployment** scenarios  
- Upgrades preserve existing activation  
- Test in clean environments before distribution  
  
---  
  
**End of Quant Boost Suite Installer & Deployment Guide**