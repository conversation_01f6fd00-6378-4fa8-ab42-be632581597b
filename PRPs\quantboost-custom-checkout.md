# PRP: QuantBoost Custom Checkout

## Feature:
- Replace the stock Supabase checkout with a custom **QuantBoost** branded checkout.

## Research Process

### 1. Codebase Analysis
- **Existing Checkout Flow:** The current implementation uses Stripe's hosted checkout. The process is initiated from the `pricing/page.tsx` component, which calls the `/api/checkout/create-session` API route. This route creates a Stripe Checkout session and redirects the user to the Stripe-hosted page.
- **Relevant Files:**
    - `QuantBoost_Frontend/src/app/pricing/page.tsx`: Initiates the checkout process.
    - `QuantBoost_Frontend/src/app/api/checkout/create-session/route.ts`: Creates the Stripe Checkout session.
    - `memory-bank_api/supabase_tables.md`: Provides the database schema.
- **Conventions:** The frontend uses Next.js, TypeScript, and Tailwind CSS. The backend is a Next.js API route that interacts with the Stripe API.

### 2. External Research
- **Stripe Elements:** To create a custom checkout flow, we will use Stripe Elements, which provides pre-built UI components for collecting payment information.
- **Documentation:**
    - Stripe Elements Overview: [https://stripe.com/docs/elements](https://stripe.com/docs/elements)
    - Stripe React Elements: [https://github.com/stripe/react-stripe-js](https://github.com/stripe/react-stripe-js)
    - Stripe Custom Payment Flow: [https://stripe.com/docs/payments/quickstart](https://stripe.com/docs/payments/quickstart)

## Implementation Blueprint

### Pseudocode
1.  **Create a new checkout page:**
    - Create a new route `app/checkout/[priceId]/page.tsx`.
    - This page will display the selected plan and a form to collect payment information.
2.  **Integrate Stripe Elements:**
    - Use the `@stripe/react-stripe-js` and `@stripe/stripe-js` libraries to embed the Stripe Card Element in the checkout form.
3.  **Update the API:**
    - Create a new API route `/api/checkout/create-payment-intent` that creates a Stripe PaymentIntent. This route will return a `client_secret` to the frontend.
4.  **Handle form submission:**
    - On form submission, the frontend will use the `client_secret` and the Stripe.js library to confirm the payment.
5.  **Update the pricing page:**
    - Modify the "Buy Now" buttons on the pricing page to link to the new checkout page.

### Implementation Tasks
1.  **Install Stripe libraries:**
    ```bash
    npm install @stripe/react-stripe-js @stripe/stripe-js
    ```
2.  **Create the checkout page:**
    - Create the file `QuantBoost_Frontend/src/app/checkout/[priceId]/page.tsx`.
    - This component will fetch the product details based on the `priceId` and display a checkout form with the Stripe Card Element.
3.  **Create the PaymentIntent API route:**
    - Create the file `QuantBoost_Frontend/src/app/api/checkout/create-payment-intent/route.ts`.
    - This route will create a Stripe PaymentIntent and return the `client_secret`.
4.  **Update the pricing page:**
    - In `QuantBoost_Frontend/src/app/pricing/page.tsx`, change the `handleBuyNow` function to navigate to the new checkout page.
5.  **Implement the checkout form:**
    - In the new checkout page, create a form that uses the Stripe Card Element.
    - On submission, call the `create-payment-intent` API to get the `client_secret`.
    - Use `stripe.confirmCardPayment` to process the payment.
6.  **Handle successful payment:**
    - After a successful payment, redirect the user to the `/dashboard` page.

### Validation Gates
```bash
# Syntax/Style (run from the QuantBoost_Frontend directory)
npm run lint

# Unit Tests (if any)
npm test
```

## Quality Checklist
- [x] All necessary context included
- [x] Validation gates are executable by AI
- [x] References existing patterns
- [x] Clear implementation path
- [x] Error handling documented

**Confidence Score:** 9/10