Title: Stripe Payment Element

URL Source: https://docs.stripe.com/payments/payment-element

Markdown Content:
The Payment Element is a UI component for the web that accepts 40+ payment methods, validates input, and handles errors. Use it alone or with other elements in your web app’s front end.

Customer location

Size

Theme

Layout

This demo only displays Google Pay or Apple Pay if you have an active card with either wallet.

Compatible APIs![Image 1](https://b.stripecdn.com/docs-statics-srv/assets/fcc3a1c24df6fcffface6110ca4963de.svg)
---------------------------------------------------------------------------------------------------------------

Stripe offers two core payments APIs compatible with Elements that give you the flexibility to accept various types of payments from your customers. You can integrate these APIs into Stripe’s prebuilt payment interfaces. The APIs serve different use cases depending on how you choose to structure your checkout flow and how much control you require. For most use cases, we recommend using [Checkout Sessions](https://docs.stripe.com/api/checkout/sessions).

*   Use the [Checkout Sessions API](https://docs.stripe.com/api/checkout/sessions) to model your customer’s complete checkout flow, including the line items in their purchase, billing and shipping addresses, applicable tax rates, and coupons or discounts. The Checkout Session allows you to create subscriptions, calculate tax rates with Stripe Tax, and initiate payments using a single integration.

Build a [checkout page with the Checkout Session API](https://docs.stripe.com/checkout/custom/quickstart).

*   Use the [Payment Intents API](https://docs.stripe.com/api/payment_intents) to model just the payments step with more granular control. Unlike the Checkout Sessions API, which requires line item details, you only pass in the final amount you want to charge. This is suitable for advanced payment flows where you want to manually compute the final amount. When using Payment Intents, you must build separate integrations with the Stripe Tax API if you want to use Stripe to calculate applicable taxes or with the Subscriptions API if you want to use Stripe to create subscriptions.

Build an [advanced integration with the Payment Intents API](https://docs.stripe.com/payments/advanced).

Checkout Sessions API

Payment Intents API

Customer

Shipping

Taxes

Discounts and coupons

Payment

Combine elements![Image 2](https://b.stripecdn.com/docs-statics-srv/assets/fcc3a1c24df6fcffface6110ca4963de.svg)
----------------------------------------------------------------------------------------------------------------

The Payment Element interoperates with other elements. For instance, this form uses one additional element to [autofill checkout details](https://docs.stripe.com/payments/link), and another to [collect the shipping address](https://docs.stripe.com/elements/address-element).

#### Note

You can’t remove the Link legal agreement because it’s required to ensure compliance with proper user awareness of terms of services and privacy policies. The [terms](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-terms) object doesn’t apply to the Link legal agreement.

![Image 3: A form with contact info, shipping address, and payment fields. The contact info is labeled Link Authentication Element, the shipping address is labeled Address Element, and the payment fields are labeled Payment Element](https://b.stripecdn.com/docs-statics-srv/assets/link-with-elements.f60af275f69b6e6e73c766d1f9928457.png)

For the complete code for this example, see [Add Link to an Elements integration](https://docs.stripe.com/payments/link/add-link-elements-integration).

You can also combine the Payment Element with the [Express Checkout Element](https://docs.stripe.com/elements/express-checkout-element). In this case, wallet payment methods such as Apple Pay and Google Pay are only displayed in the Express Checkout Element to avoid duplication.

Payment methods![Image 4](https://b.stripecdn.com/docs-statics-srv/assets/fcc3a1c24df6fcffface6110ca4963de.svg)
---------------------------------------------------------------------------------------------------------------

Stripe enables certain payment methods for you by default. We might also enable additional payment methods after notifying you. Use the [Dashboard](https://dashboard.stripe.com/settings/payment_methods) to enable or disable payment methods at any time. With the Payment Element, you can use [Dynamic payment methods](https://docs.stripe.com/payments/payment-methods/dynamic-payment-methods) to:

*   Manage payment methods in the [Dashboard](https://dashboard.stripe.com/settings/payment_methods) without coding
*   Dynamically display the most relevant payment options based on factors such as location, currency, and transaction amount

For instance, if a customer in Germany is paying in EUR, they see all the active payment methods that accept EUR, starting with ones that are widely used in Germany.

![Image 5: A variety of payment methods.](https://b.stripecdn.com/docs-statics-srv/assets/payment-element-methods.26cae03aff199d6f02b0d92bd324c219.png)

Show payment methods in order of relevance to your customer

To further customize how payment methods render, such as by filtering card brands that you don’t want to support, see [Customize payment methods](https://docs.stripe.com/payments/customize-payment-methods). To add payment methods integrated outside of Stripe, you can use [custom payment methods](https://docs.stripe.com/payments/payment-element/custom-payment-methods).

If your integration requires you to list payment methods manually, see [Manually list payment methods](https://docs.stripe.com/payments/payment-methods/integration-options#listing-payment-methods-manually).

Layout![Image 6](https://b.stripecdn.com/docs-statics-srv/assets/fcc3a1c24df6fcffface6110ca4963de.svg)
------------------------------------------------------------------------------------------------------

You can customize the Payment Element’s layout to fit your checkout flow. The following image is the same Payment Element rendered using different layout configurations.

![Image 7: Examples of the three checkout forms. The image shows the tab option, where customers pick from payment methods shown as tabs or the two accordion options, where payment methods are vertically listed. You can choose to either display radio buttons or not in the accordion view. ](https://b.stripecdn.com/docs-statics-srv/assets/pe_layout_example.525f78bcb99b95e49be92e5dd34df439.png)

Payment Element with different layouts.

The tabs layout displays payment methods horizontally using tabs. To use this layout, set the value for [layout.type](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-layout-type) to `tabs`. You can also specify other properties, such as [layout.defaultCollapsed](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-layout-defaultCollapsed).

index.js

```
const stripe = Stripe('pk_test_TYooMQauvdEDq54NiTphI7jx');

const appearance = { /* appearance */ };
const options = {
  layout: {
    type: 'tabs',
    defaultCollapsed: false,
  }
};
```

Appearance![Image 8](https://b.stripecdn.com/docs-statics-srv/assets/fcc3a1c24df6fcffface6110ca4963de.svg)
----------------------------------------------------------------------------------------------------------

Use the Appearance API to control the style of all elements. Choose a theme or update specific details.

![Image 9: Examples of light and dark modes for the payment element checkout form.](https://b.stripecdn.com/docs-statics-srv/assets/appearance_example.e076cc750983bf552baf26c305e7fc90.png)

For instance, choose the “flat” theme and override the primary text color.

index.js

```
const stripe = Stripe('pk_test_TYooMQauvdEDq54NiTphI7jx');

const appearance = {
  theme: 'flat',
  variables: { colorPrimaryText: '#262626' }
};
```

See the [Appearance API](https://docs.stripe.com/elements/appearance-api) documentation for a full list of themes and variables.

Options![Image 10](https://b.stripecdn.com/docs-statics-srv/assets/fcc3a1c24df6fcffface6110ca4963de.svg)
--------------------------------------------------------------------------------------------------------

Stripe elements support more options than these. For instance, display your business name using the [business](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-business) option.

index.js

```
const stripe = Stripe('pk_test_TYooMQauvdEDq54NiTphI7jx');

const appearance = { /* appearance */};
const options = {
  business: { name: "RocketRides" }
};
```

The Payment Element supports the following options. See each options’s reference entry for more information.

[layout](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-layout)Layout for the Payment Element.
[defaultValues](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-defaultValues)Initial customer information to display in the Payment Element.
[business](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-business)Information about your business to display in the Payment Element.
[paymentMethodOrder](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-business)Order to list payment methods in.
[fields](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-fields)Whether to display certain fields.
[readOnly](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-readOnly)Whether payment details can be changed.
[terms](https://docs.stripe.com/js/elements_object/create_payment_element#payment_element_create-options-terms)Whether mandates or other legal agreements are displayed in the Payment Element. The default behavior is to show them only when necessary.
[wallets](https://docs.stripe.com/js/elements_object/create_payment_element)Whether to show wallets like Apple Pay or Google Pay. The default is to show them when possible.

Errors![Image 11](https://b.stripecdn.com/docs-statics-srv/assets/fcc3a1c24df6fcffface6110ca4963de.svg)
-------------------------------------------------------------------------------------------------------

Payment Element automatically shows localized customer-facing error messages during client confirmation for the following error codes:

*   `generic_decline`
*   `insufficient_funds`
*   `incorrect_zip`
*   `incorrect_cvc`
*   `invalid_cvc`
*   `invalid_expiry_month`
*   `invalid_expiry_year`
*   `expired_card`
*   `fraudulent`
*   `lost_card`
*   `stolen_card`
*   `card_velocity_exceeded`

To display messages for other types of errors, refer to [error codes](https://docs.stripe.com/error-codes) and [error handling](https://docs.stripe.com/error-handling).
