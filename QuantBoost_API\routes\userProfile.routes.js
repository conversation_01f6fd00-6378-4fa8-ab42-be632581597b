const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient'); // Shared Supabase client
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { authenticateJWT } = require('../middleware/authMiddleware');

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// GET /me/profile - Fetches the authenticated user's profile
router.get('/profile', async (req, res) => {
    const userId = req.user.id;

    if (!userId) {
        // This should ideally not happen if authenticateJWT is working correctly
        return sendError(res, 401, 'User not authenticated.');
    }

    try {
        const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

        if (error) {
            console.error('Error fetching profile:', error);
            if (error.code === 'PGRST116') { // PostgREST error for "Searched for one row, but found 0"
                return sendError(res, 404, 'Profile not found.');
            }
            return sendError(res, 500, 'Failed to fetch profile.');
        }

        if (!profile) {
            return sendError(res, 404, 'Profile not found.');
        }

        sendSuccess(res, profile);
    } catch (err) {
        console.error('Unexpected error fetching profile:', err);
        sendError(res, 500, 'An unexpected error occurred.');
    }
});

// PUT /me/profile - Updates the authenticated user's profile
router.put('/profile', async (req, res) => {
    const userId = req.user.id;

    if (!userId) {
        return sendError(res, 401, 'User not authenticated.');
    }

    const { first_name, last_name, avatar_url, company_name, ...otherFields } = req.body;
    const allowedUpdates = {};

    if (first_name !== undefined) {
        allowedUpdates.first_name = first_name;
    }
    if (last_name !== undefined) {
        allowedUpdates.last_name = last_name;
    }
    if (avatar_url !== undefined) {
        allowedUpdates.avatar_url = avatar_url;
    }
    if (company_name !== undefined) {
        allowedUpdates.company_name = company_name;
    }

    // Check if any other fields were sent
    if (Object.keys(otherFields).length > 0) {
        console.warn(`User ${userId} attempted to update disallowed fields: ${Object.keys(otherFields).join(', ')}`);
        // Optionally, you could send an error here if strict adherence is required:
        // return sendError(res, 400, `Updates only allowed for: first_name, last_name, avatar_url, company_name. Disallowed fields: ${Object.keys(otherFields).join(', ')}`);
    }
    
    if (Object.keys(allowedUpdates).length === 0) {
        return sendError(res, 400, 'No valid fields provided for update. Allowed fields: first_name, last_name, avatar_url, company_name.');
    }

    // Add the updated_at timestamp
    allowedUpdates.updated_at = new Date().toISOString();

    try {
        const { data: updatedProfile, error } = await supabase
            .from('profiles')
            .update(allowedUpdates)
            .eq('id', userId)
            .select()
            .single();

        if (error) {
            console.error('Error updating profile:', error);
            if (error.code === 'PGRST116') { // Not found
                return sendError(res, 404, 'Profile not found to update.');
            }
            return sendError(res, 500, 'Failed to update profile.');
        }
        
        if (!updatedProfile) {
             return sendError(res, 404, 'Profile not found to update (no data returned).');
        }

        sendSuccess(res, updatedProfile, 'Profile updated successfully.');
    } catch (err) {
        console.error('Unexpected error updating profile:', err);
        sendError(res, 500, 'An unexpected error occurred.');
    }
});

module.exports = router;
