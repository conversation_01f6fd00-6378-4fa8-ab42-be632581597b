---
title: Global Project Structure
purpose: "Defines the overall project name, lists constituent sub-projects, their root paths, and key directories/files within each. This serves as a map for navigating the codebase."
projects: ["Global"]
source_analysis: "Initial setup and ongoing analysis of powerpoint-add-in, QuantBoost.Licensing, QuantBoost_API, QuantBoost_Frontend, and QuantBoost_Excel."
status: active
last_updated: 2025-06-06T17:15:00.000Z
tags: ["global", "structure", "navigation"]
---

## Overall Project: QuantBoost Suite

### Constituent Projects & Root Paths
*   **QuantBoost_Shared (Shared Project files):** `c:\VS projects\QuantBoost\QuantBoost_Shared` (relative: `./QuantBoost_Shared`)
    *   Key Directories/Files:
        *   `UI/` - LicenseDetailsClient.cs & LicenseStatusClient.cs models. LicenseDialog.cs and related forms. Common license status handling.
        *   `Utilities/` - AsyncHelper.cs (Office thread synchronization). ErrorHandlingService.cs (centralized logging). Task extension methods. 
        *   `Security/` - TokenStorage.cs (DPAPI-based encryption). Authentication flow and token refresh logic. Device ID management.
        *   `Models/` - Shared exception types (AuthenticationRequiredException, etc.). Common data transfer objects. Licensing interfaces.
        *   `Configuration/` - Shared constants and config helpers. Common assembly binding redirects.
        
*   **QuantBoost_PPTX (PowerPoint Add-in):** `c:\VS projects\QuantBoost\QuantBoost_PPTX` (relative: `./QuantBoost_PPTX`)
    *   Key Directories/Files:
        *   `Analysis/` - Code for parsing and analyzing PowerPoint presentations.
        *   `ExcelLink/` - Manages linking dynamic Excel data into PowerPoint slides.
        *   `UI/` - User interface components (Ribbon, Task Panes, Dialogs).
        *   `Utilities/` - Common helper classes.
        *   `Security/` - Security-related components including token storage.
        *   `Resources/` - Application resources and assets.
        *   `Properties/` - Assembly information, resources, and project settings.
        *   `TokenStorage.cs` - Securely stores and retrieves authentication tokens (root level).
        *   `Security/TokenStorage.cs` - Additional token storage implementation.
        *   `ThisAddIn.cs` - Main entry point and core logic for the PowerPoint Add-in.
        *   `QuantBoost_Powerpoint.csproj`
        *   `QuantBoost_Powerpoint.sln`
        *   `packages.config`
        *   `copilot-instructions.md` - AI assistant instructions for development.

*   **QuantBoost_Licensing (Licensing SDK):** `c:\VS projects\QuantBoost\QuantBoost_Licensing` (relative: `./QuantBoost_Licensing`)
    *   Key Directories/Files:
        *   `LicensingSDK.cs` - Core SDK logic for license validation.
        *   `CustomExceptions.cs` - Defines custom exceptions like `AccessTokenExpiredException`.
        *   `QuantBoost.Licensing.csproj`
        *   `packages.config`
        *   `Properties/` - Assembly information and project properties.
        *   `bin/` - Compiled output directory.
        *   `obj/` - Build intermediate files.

*   **QuantBoost_Excel (Excel Add-in):** `c:\VS projects\QuantBoost\QuantBoost_Excel` (relative: `./QuantBoost_Excel`)
    *   Key Directories/Files:
        *   `ThisAddIn.cs` - Main entry point and core logic for the Excel Add-in.
        *   `ThisAddIn.Designer.cs` / `.xml` - Visual Studio designer files.
        *   `Features/` - Core feature implementations:
            *   `NameScrubber/` - Name scrubbing functionality.
            *   `ExcelTrace/` - Excel formula tracing capabilities.
            *   `SheetAnalyzer/` - Worksheet analysis tools.
        *   `Common/` - Shared utilities and helper classes.
        *   `Properties/` - Assembly information and project settings.
        *   `QuantBoost_excel-add-in.csproj`
        *   `QuantBoost_excel-add-in.sln`
        *   `bin/` - Compiled output directory.
        *   `obj/` - Build intermediate files.

*   **QuantBoost_API (Backend API):** `c:\VS projects\QuantBoost\QuantBoost_API` (relative: `./QuantBoost_API`)
    *   Key Directories/Files:
        *   `index.js` - Main entry point and Express server setup.
        *   `supabaseClient.js` - Supabase client configuration.
        *   `package.json` - Project dependencies and scripts.
        *   `README.md` - API documentation and setup instructions.
        *   `magic-link-relay.html` - Handles token transfer from Supabase magic link.
        *   `jest.config.js` - Testing configuration.
        *   `index.test.js` - Main API tests.
        *   `TODO LIST.txt` - Development task tracking.
        *   `routes/` - Modular route definitions:
            *   `auth.routes.js` - Authentication endpoints.
            *   `licenses.routes.js` - License validation endpoints.
            *   `userLicenses.routes.js` - User license management.
            *   `userProfile.routes.js` - User profile management.
            *   `userSubscriptions.routes.js` - User subscription management.
            *   `adminUsers.routes.js` - Admin user management.
            *   `adminLicenses.routes.js` - Admin license management.
            *   `adminSubscriptions.routes.js` - Admin subscription management.
            *   `adminActivations.routes.js` - Admin activation management.
            *   `teamAdmin.routes.js` - Team administration.
            *   `webhooks.routes.js` - Webhook handlers.
            *   `test.routes.js` - Testing endpoints.
        *   `middleware/` - Express middleware components.
        *   `utils/` - Utility functions and helpers.
        *   `tests/` - Test files and test data.

*   **QuantBoost_Frontend (Web Frontend):** `c:\VS projects\QuantBoost\QuantBoost_Frontend` (relative: `./QuantBoost_Frontend`)
    *   Key Directories/Files:
        *   `public/` - Static assets and public files.
        *   `src/` - Source code directory containing app logic and components.
        *   `SupabaseClient.ts` - Client-side Supabase configuration.
        *   `SupabaseServerClient.ts` - Server-side Supabase configuration.
        *   `middleware.ts` - Next.js middleware for authentication and routing.
        *   `package.json` - Project dependencies and scripts.
        *   `next.config.ts` - Next.js configuration.
        *   `tailwind.config.js` - Tailwind CSS configuration.
        *   `tsconfig.json` - TypeScript configuration.
        *   `eslint.config.mjs` - ESLint configuration.
        *   `postcss.config.js` / `.mjs` - PostCSS configuration.
        *   `components.json` - Component library configuration.

*   **memory-bank (Documentation & Knowledge Base):** `c:\VS projects\QuantBoost\memory-bank` (relative: `./memory-bank`)
    *   Key Directories/Files:
        *   `QuantBoost.code-workspace` - VS Code workspace configuration.
        *   `structure.md` - This global project structure document.
        *   `templates/` - Documentation templates for consistent project documentation.
        *   `memory-bank_api/` - API project documentation and knowledge base.
        *   `memory-bank_frontend/` - Frontend project documentation and knowledge base.
        *   `memory-bank_excel-add-in/` - Excel add-in project documentation and knowledge base.
        *   `memory-bank_powerpoint-add-in/` - PowerPoint add-in project documentation and knowledge base.
        *   `memory-bank_licensing/` - Licensing SDK documentation and knowledge base.

### Additional Project Files (Root Level)
*   `test_magic_link_final.html` - Magic link authentication testing page.
*   `test_magic_link_integration.html` - Integration testing for magic link functionality.
*   `test-api-response.js` - API response testing utility.
*   `test-response-structure.js` - Response structure validation testing.