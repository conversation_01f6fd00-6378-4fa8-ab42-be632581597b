// --- START OF COMPLETE index.js FILE ---

// Add environment configuration at the very top
if (process.env.NODE_ENV !== 'production') {
    require('dotenv').config();
}

const telemetry = require('./utils/telemetry');
const logger = require('./utils/logger');
const express = require('express');
const cors = require('cors');
const compression = require('compression');
const securityHeaders = require('./middleware/security');
const metricsMiddleware = require('./middleware/metrics');
const { rateLimiter } = require('./middleware/rateLimitMiddleware');
const { authenticateJWT } = require('./middleware/authMiddleware');
const authRoutes = require('./routes/auth.routes');
const licenseRoutes = require('./routes/licenses.routes');
const adminLicenseRoutes = require('./routes/adminLicenses.routes');
const adminUserRoutes = require('./routes/adminUsers.routes.js');
const adminSubscriptionRoutes = require('./routes/adminSubscriptions.routes.js');
const adminActivationRoutes = require('./routes/adminActivations.routes.js');
const webhooksRoutes = require('./routes/webhooks.routes.js');
const userProfileRoutes = require('./routes/userProfile.routes.js');
const userLicensesRoutes = require('./routes/userLicenses.routes.js');
const userSubscriptionsRoutes = require('./routes/userSubscriptions.routes.js');
const teamAdminRoutes = require('./routes/teamAdmin.routes.js');
const testRoutes = require('./routes/test.routes.js');
const healthRoutes = require('./routes/health.routes.js');

logger.info('Supabase Client Initialized', {
  url: process.env.SUPABASE_URL?.substring(0, 20) + '...'
});

const app = express();

// --- Global Middleware ---
// Security headers first
app.use(securityHeaders);

// Response compression
app.use(compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cors());

// Metrics collection
app.use(metricsMiddleware);

// Update request logger middleware with proper metadata
app.use((req, res, next) => {
  const requestId = req.headers['x-request-id'] || Math.random().toString(36).substr(2, 9);
  req.requestId = requestId;
  
  logger.info('Incoming request', {
    requestId,
    method: req.method,
    path: req.path,
    query: req.query,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('user-agent'),
    correlationId: req.headers['x-correlation-id']
  });
  
  // Log response when finished
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('Request completed', {
      requestId,
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      contentLength: res.get('content-length')
    });
  });
  
  next();
});

app.use(rateLimiter);

// --- Route Mounting ---
// Public routes - no JWT needed
app.use('/v1/auth', authRoutes);
app.use('/v1/webhooks', webhooksRoutes);
app.use('/health', healthRoutes);
app.use('/v1', testRoutes);

// Protected routes - JWT is now required
app.use('/v1/licenses', authenticateJWT, licenseRoutes);
app.use('/v1/me', authenticateJWT, userProfileRoutes);
app.use('/v1/me', authenticateJWT, userLicensesRoutes);
app.use('/v1/me', authenticateJWT, userSubscriptionsRoutes);
app.use('/v1/me/subscriptions/:subscriptionId', authenticateJWT, teamAdminRoutes);

const { authorizeAdmin } = require('./middleware/adminAuthMiddleware');

// ... (other requires)

// ... (app setup)

// --- Route Mounting ---
// Public routes - no JWT needed
app.use('/v1/auth', authRoutes);
app.use('/v1/webhooks', webhooksRoutes);
app.use('/health', healthRoutes);
app.use('/v1', testRoutes);

// Protected routes - JWT is required
app.use('/v1/licenses', authenticateJWT, licenseRoutes);
app.use('/v1/me', authenticateJWT, userProfileRoutes);
app.use('/v1/me', authenticateJWT, userLicensesRoutes);
app.use('/v1/me', authenticateJWT, userSubscriptionsRoutes);
app.use('/v1/me/subscriptions/:subscriptionId', authenticateJWT, teamAdminRoutes);

// Admin routes - Require both a valid JWT and an 'admin' role.
// The authorizeAdmin middleware MUST come after authenticateJWT.
app.use('/v1/admin/licenses', authenticateJWT, authorizeAdmin, adminLicenseRoutes);
app.use('/v1/admin/users', authenticateJWT, authorizeAdmin, adminUserRoutes);
app.use('/v1/admin/subscriptions', authenticateJWT, authorizeAdmin, adminSubscriptionRoutes);
app.use('/v1/admin/activations', authenticateJWT, authorizeAdmin, adminActivationRoutes);

// --- Global Error Handling ---
app.use((err, req, res, next) => {
    logger.error('Unhandled error', {
        error: err.message,
        stack: err.stack,
        statusCode: err.statusCode || 500,
        requestId: req.requestId,
        method: req.method,
        path: req.path,
        body: req.body,
        query: req.query
    });
    
    const statusCode = err.statusCode || 500;
    const message = process.env.NODE_ENV === 'production' && statusCode === 500
                  ? 'An unexpected error occurred.'
                  : err.message || 'Internal Server Error';
    
    res.status(statusCode).json({
        success: false,
        error: {
          message,
          requestId: req.requestId
        }
    });
});

// --- 404 Handler for unmatched routes ---
app.use((req, res, next) => {
    res.status(404).json({
        success: false,
        error: { message: `Cannot ${req.method} ${req.originalUrl} - Route not found.` }
    });
});


// --- Server Initialization ---
const PORT = process.env.PORT || 3000;
const serverInstance = app.listen(PORT, () => {
  logger.info('QuantBoost API server started', {
    port: PORT,
    environment: process.env.NODE_ENV,
    nodeVersion: process.version
  });
});

module.exports = { app, serverInstance };