import { createClient } from '@supabase/supabase-js';

// Types for API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  status: string;
  plan_id: string;
  quantity: number;
  current_period_end: string;
  stripe_subscription_id: string;
  created_at: string;
  updated_at: string;
}

export interface License {
  id: string;
  subscription_id: string;
  product_id: string;
  status: 'unassigned' | 'assigned' | 'active' | 'expired' | 'revoked';
  email?: string;
  user_id?: string;
  license_key: string;
  license_tier: string;
  expiry_date?: string;
  assigned_at?: string;
  activated_at?: string;
  max_activations: number;
  activation_count: number;
}

export interface TeamLicenseAssignment {
  license_id_to_assign: string;
  target_user_email: string;
}

export interface TeamInvitation {
  target_email: string;
}

class ApiClient {
  private baseURL: string;
  private supabase: any;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_AZURE_API_URL || '';
    if (!this.baseURL && typeof window !== 'undefined') {
      // Only warn in browser environment, not during build
      console.warn('NEXT_PUBLIC_AZURE_API_URL is not configured');
    }

    // Initialize Supabase client safely
    this.initializeSupabase();
  }

  private initializeSupabase() {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      // During build time or SSR, create a placeholder client
      this.supabase = createClient('https://placeholder.supabase.co', 'placeholder-key');
      return;
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase environment variables');
      this.supabase = createClient('https://placeholder.supabase.co', 'placeholder-key');
      return;
    }

    this.supabase = createClient(supabaseUrl, supabaseAnonKey);
  }

  private async getAuthToken(): Promise<string | null> {
    try {
      const { data: { session } } = await this.supabase.auth.getSession();
      return session?.access_token || null;
    } catch (error) {
      console.error('Failed to get auth token:', error);
      return null;
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }



      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, {
        ...options,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Subscription Management
  async getUserSubscriptions(): Promise<ApiResponse<Subscription[]>> {
    return this.makeRequest<Subscription[]>('/v1/me/subscriptions');
  }

  // Team License Management
  async getTeamLicenses(subscriptionId: string): Promise<ApiResponse<License[]>> {
    return this.makeRequest<License[]>(`/v1/me/subscriptions/${subscriptionId}/team-licenses`);
  }

  async assignTeamLicense(
    subscriptionId: string,
    assignment: TeamLicenseAssignment
  ): Promise<ApiResponse<License>> {
    return this.makeRequest<License>(`/v1/me/subscriptions/${subscriptionId}/team-licenses/assign`, {
      method: 'POST',
      body: JSON.stringify(assignment),
    });
  }

  async inviteTeamMember(
    subscriptionId: string,
    invitation: TeamInvitation
  ): Promise<ApiResponse<License>> {
    return this.makeRequest<License>(`/v1/me/subscriptions/${subscriptionId}/team-licenses/invite`, {
      method: 'POST',
      body: JSON.stringify(invitation),
    });
  }

  async unassignTeamLicense(
    subscriptionId: string,
    licenseId: string
  ): Promise<ApiResponse<License>> {
    return this.makeRequest<License>(`/v1/me/subscriptions/${subscriptionId}/team-licenses/unassign`, {
      method: 'POST',
      body: JSON.stringify({ license_id_to_unassign: licenseId }),
    });
  }

  // Billing Portal Integration
  async createBillingPortalSession(returnUrl: string): Promise<ApiResponse<{ url: string }>> {
    return this.makeRequest<{ url: string }>('/v1/me/billing/create-customer-portal-session', {
      method: 'POST',
      body: JSON.stringify({ return_url: returnUrl }),
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Utility functions for error handling
export const handleApiError = (response: ApiResponse<unknown>, fallbackMessage = 'An error occurred') => {
  if (!response.success) {
    console.error('API Error:', response.error);
    return response.error || fallbackMessage;
  }
  return null;
};

export const isApiSuccess = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { data: T } => {
  return response.success && response.data !== undefined;
};