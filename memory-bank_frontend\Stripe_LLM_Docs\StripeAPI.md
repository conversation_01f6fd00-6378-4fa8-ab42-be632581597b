# Stripe Documentation

## Docs
- [Testing](https://docs.stripe.com/testing.md): Simulate payments to test your integration.
- [API Reference](https://docs.stripe.com/api.md)
- [Receive payouts](https://docs.stripe.com/payouts.md): Set up your bank account to receive payouts.
- [Platforms and marketplaces with Stripe Connect](https://docs.stripe.com/connect.md): Build a SaaS platform or marketplace with Connect.
- [Supported currencies](https://docs.stripe.com/currencies.md): See what currencies you can use for making charges and for paying out to your bank account.
- [API upgrades](https://docs.stripe.com/upgrades.md): Keep track of changes and upgrades to the Stripe API.
- [Stripe SDKs](https://docs.stripe.com/sdks.md): Libraries and tools for interacting with your Stripe integration.
- [Receive Stripe events in your webhook endpoint](https://docs.stripe.com/webhooks.md): Listen to events in your Stripe account on your webhook endpoint so your integration can automatically trigger reactions.
- [Declines](https://docs.stripe.com/declines.md): Learn about payment declines and how to lower your decline rate.
- [Refund and cancel payments](https://docs.stripe.com/refunds.md): Learn how to cancel or refund a payment.
- [Security at Stripe](https://docs.stripe.com/security.md): Learn how Stripe handles security.
- [Stripe-hosted page](https://docs.stripe.com/checkout/quickstart.md)
- [Build an advanced integration](https://docs.stripe.com/payments/quickstart.md)
- [Prebuilt subscription page with Stripe Checkout](https://docs.stripe.com/billing/quickstart.md)
- [Set up and deploy a webhook](https://docs.stripe.com/webhooks/quickstart.md): Learn how to set up and deploy a webhook to listen to events from Stripe.
- [Types of events](https://docs.stripe.com/api/events/types.md)
- [Integration security guide](https://docs.stripe.com/security/guide.md): Ensure PCI compliance and secure customer-server communications.
- [Linked external accounts](https://docs.stripe.com/get-started/account/linked-external-accounts.md): Manage your linked external accounts.
- [Affirm payments](https://docs.stripe.com/payments/affirm.md): Offer your US and Canadian customers flexible financing while getting paid upfront with Affirm.
- [Integrate with events](https://docs.stripe.com/event-destinations.md): Send events from Stripe to webhook endpoints and cloud services.
- [Resolve webhook signature verification errors](https://docs.stripe.com/webhooks/signature.md): Learn how to fix a common error when listening to webhook events.
- [Afterpay and Clearpay payments](https://docs.stripe.com/payments/afterpay-clearpay.md): Offer your customers flexible financing while getting paid upfront with Afterpay (also known as Clearpay in the UK).
- [Start a team](https://docs.stripe.com/get-started/account/teams.md): Learn how to invite and interact with team members.
- [Errors](https://docs.stripe.com/api/errors.md)
- [Versioning](https://docs.stripe.com/api/versioning.md)

## Checkout
Build a low-code payment form and embed it on your site or host it on Stripe. Checkout creates a customizable form for collecting payments. You can redirect customers to a Stripe-hosted payment page, embed Checkout directly in your website, or create a customized checkout page with Stripe Elements. It supports one-time payments and subscriptions and accepts over 40 local payment methods. For a full list of Checkout features.

- [Stripe Checkout](https://docs.stripe.com/payments/checkout.md): Build a low-code payment form and embed it on your site or host it on Stripe.
- [Stripe-hosted page](https://docs.stripe.com/checkout/quickstart.md)
- [Embedded form](https://docs.stripe.com/checkout/embedded/quickstart.md)
- [How Checkout works](https://docs.stripe.com/payments/checkout/how-checkout-works.md): Learn how to use Checkout to collect payments on your website.
- [Customize Checkout](https://docs.stripe.com/payments/checkout/customization.md): Customize the appearance and behavior of Checkout.
- [The Setup Intents API](https://docs.stripe.com/payments/setup-intents.md): Learn more about the Setup Intents API for saving payment methods.
- [Fulfill orders](https://docs.stripe.com/checkout/fulfillment.md): Learn how to fulfill payments received with the Checkout Sessions API.
- [Customize redirect behavior](https://docs.stripe.com/payments/checkout/custom-success-page.md): Display a confirmation page with your customer's order information.
- [Add discounts](https://docs.stripe.com/payments/checkout/discounts.md): Reduce the amount charged to a customer by discounting their subtotal with coupons and promotion codes.
- [Use your custom domain](https://docs.stripe.com/payments/checkout/custom-domains.md): Learn how to bring your own custom domain to Stripe Checkout, Payment Links, and customer portal.
- [Limit customers to one subscription](https://docs.stripe.com/payments/checkout/limit-subscriptions.md): Direct customers to manage their subscription when they already have one.
- [Cross-sells](https://docs.stripe.com/payments/checkout/cross-sells.md): Enable customers to purchase complementary products at checkout by using cross-sells.
- [Overview](https://docs.stripe.com/products-prices/overview.md): Model your business on Stripe with products and prices.
- [Subscriptions](https://docs.stripe.com/payments/subscriptions.md): Create subscriptions for your customers.
- [Charge for shipping](https://docs.stripe.com/payments/during-payment/charge-shipping.md): Create different shipping rates for your customers.
- [Collect taxes](https://docs.stripe.com/payments/checkout/taxes.md): Learn how to collect taxes with Stripe Tax.
- [Manage payment methods](https://docs.stripe.com/payments/checkout/payment-methods.md): Use dynamic payment methods or manually define the payment methods to allow per checkout session.
- [Customize text and policies](https://docs.stripe.com/payments/checkout/customization/policies.md): Customize the text that your customers see, and the policies Checkout displays.
- [Add custom fields](https://docs.stripe.com/payments/checkout/custom-fields.md): Add additional fields to a prebuilt payment page with Checkout.
- [Collect physical addresses](https://docs.stripe.com/payments/collect-addresses.md): Learn how to collect billing and shipping addresses.
- [Customize checkout behavior](https://docs.stripe.com/payments/checkout/customization/behavior.md): Customize the behavior of the checkout process to increase conversion and revenue.
- [Save payment details during payment](https://docs.stripe.com/payments/checkout/save-during-payment.md): Learn how to accept a payment and save your customer's payment details for future purchases.
- [After the payment](https://docs.stripe.com/payments/checkout/after-the-payment.md): Customize the post-payment checkout process.
- [Set the billing cycle date](https://docs.stripe.com/payments/checkout/billing-cycle.md): Set a subscription's billing cycle anchor to a fixed date.
- [Let customers decide what to pay](https://docs.stripe.com/payments/checkout/pay-what-you-want.md): Accept tips and donations, or sell pay-what-you-want products and services.
- [Subscription upsells](https://docs.stripe.com/payments/checkout/upsells.md): Enable customers to upgrade their subscription plan at checkout by using upsells.
- [No-cost orders](https://docs.stripe.com/payments/checkout/no-cost-orders.md): Accept orders for no-cost line items or apply 100% off discounts for one-time payments.
- [Analyze your conversion funnel](https://docs.stripe.com/payments/checkout/analyze-conversion-funnel.md): Analyze your Stripe Checkout conversion funnel with Google Analytics 4.
- [Add discounts, upsells, and optional items](https://docs.stripe.com/payments/checkout/promotions.md): Boost sales with discounts and offers.
- [Manual currency prices](https://docs.stripe.com/payments/currencies/localize-prices/manual-currency-prices.md): Present local currencies to customers with manual currency prices.
- [Collect customer phone numbers](https://docs.stripe.com/payments/checkout/phone-numbers.md): Collect a phone number for shipping or invoicing when your customer makes a payment.
- [Dynamically customize shipping options](https://docs.stripe.com/payments/checkout/custom-shipping-options.md): Update shipping options based on a customer's shipping address.
- [One-time payments with Checkout](https://docs.stripe.com/payments/checkout/client.md): Learn how to accept one-time card payments with just a few lines of code.
- [Set up future payments](https://docs.stripe.com/payments/checkout/save-and-reuse.md): Learn how to save payment details in a Checkout session and charge your customers later.
- [Make line item quantities adjustable](https://docs.stripe.com/payments/checkout/adjustable-quantity.md): Enable your customers to adjust the quantity of items during checkout.
- [Elements with Checkout Sessions API beta changelog](https://docs.stripe.com/checkout/elements-with-checkout-sessions-api/changelog.md): Keep track of changes to the Elements with Checkout Sessions API beta integration.
- [Checkout prices migration guide](https://docs.stripe.com/payments/checkout/migrating-prices.md): Learn how to update your integration to use prices with Stripe Checkout.
- [Checkout Sessions](https://docs.stripe.com/api/checkout/sessions.md)
- [The Checkout Session object](https://docs.stripe.com/api/checkout/sessions/object.md)
- [Create a Checkout Session](https://docs.stripe.com/api/checkout/sessions/create.md)

## Payments
- [Acceptable verification documents by country](https://docs.stripe.com/acceptable-verification-documents.md): Learn which documents Stripe accepts for verification of identity, address, and legal entity.
- [The Payment Intents API](https://docs.stripe.com/payments/payment-intents.md): Learn how to use the Payment Intents API for Stripe payments.
- [Multi-currency settlement](https://docs.stripe.com/payouts/multicurrency-settlement.md): Accept, settle and pay out funds in multiple currencies.
- [Set up your development environment](https://docs.stripe.com/get-started/development-environment.md): Get familiar with the Stripe CLI and our server-side SDKs.
- [Decline codes](https://docs.stripe.com/declines/codes.md): Learn about decline codes and how to resolve them when a charge fails.
- [Place a hold on a payment method](https://docs.stripe.com/payments/place-a-hold-on-a-payment-method.md): Separate payment authorization and capture to create a charge now, but capture funds later.
- [Payment status updates](https://docs.stripe.com/payments/payment-intents/verifying-status.md): Monitor and verify payment status, so that you can respond to successful and failed payments.
- [Payment method integration options](https://docs.stripe.com/payments/payment-methods/integration-options.md): Learn about the different ways to integrate payment methods.
- [Add funds to your Stripe balance](https://docs.stripe.com/get-started/account/add-funds.md): Cover increased refunds and chargebacks by adding funds to your balance.
- [Web Dashboard](https://docs.stripe.com/dashboard/basics.md): Learn how to use the web version of the Stripe Dashboard.
- [Payment Methods API](https://docs.stripe.com/payments/payment-methods.md): Learn more about the API that powers a range of global payment methods.
- [Stripe Dashboard mobile app](https://docs.stripe.com/dashboard/mobile.md): Learn how to use the mobile app version of the Stripe Dashboard.
- [Buy now, pay later](https://docs.stripe.com/payments/buy-now-pay-later.md): Learn about buy now, pay later methods with Stripe.
- [Browse sample projects](https://docs.stripe.com/samples.md): Explore the library of sample projects using Stripe.
- [Perform searches in the Dashboard](https://docs.stripe.com/dashboard/search.md): Use the Dashboard to search for payments, customers, and more.
- [Test Apple and Google wallet rendering](https://docs.stripe.com/testing/wallets.md): Compare your integration against working demo integrations to identify possible rendering issues.
- [Bank Debits](https://docs.stripe.com/payments/bank-debits.md): Learn how to accept bank debits with Stripe.
- [Create an embeddable buy button](https://docs.stripe.com/payment-links/buy-button.md): Use Payment Links to create an embeddable buy button for your website.
- [After a payment link payment](https://docs.stripe.com/payment-links/post-payment.md): Learn what you can do after receiving a payment link payment.
- [Share a payment link](https://docs.stripe.com/payment-links/share.md): Share payment links across social media, emails, or your website.
- [Payment Intents](https://docs.stripe.com/api/payment_intents.md)
- [The PaymentIntent object](https://docs.stripe.com/api/payment_intents/object.md)
- [Create a PaymentIntent](https://docs.stripe.com/api/payment_intents/create.md)
- [Payment Methods](https://docs.stripe.com/api/payment_methods.md)
- [The PaymentMethod object](https://docs.stripe.com/api/payment_methods/object.md)
- [Create a PaymentMethod](https://docs.stripe.com/api/payment_methods/create.md)

## Link
*Link* allows your customers to select a saved payment method at checkout instead of entering payment information. Your customers can save their credit cards, debit cards, or US bank accounts for faster checkout at any Link-enabled business. Link also lets you accept Instant Bank Payments. All Link transactions confirm immediately, and successful payments settle to your Stripe balance on the same timeline as card payments, regardless of the payment method that funds the payment. Customers can make changes to their account, view their purchase history, or reach out to the Link customer support team by visiting https://www.link.com. For information about how your payment integration affects Link, see [Link in different payment integrations](https://docs.stripe.com.md). Link isn’t available in India. In Brazil and Thailand, the Payment Element doesn’t support Link.

- [Link in different payment integrations](https://docs.stripe.com/payments/link/link-payment-integrations.md): Use Link with dynamic payment methods and other integrations.
- [Instant Bank Payments](https://docs.stripe.com/payments/link/instant-bank-payments.md): Accept low cost bank payments with instant confirmation.
- [Link with Checkout](https://docs.stripe.com/payments/link/checkout-link.md): Use Link with Stripe's prebuilt checkout page.
- [Link in different payment integrations](https://docs.stripe.com/payments/link/link-payment-integrations.md): Use Link with dynamic payment methods and other integrations.
- [Link in the Payment Element](https://docs.stripe.com/payments/link/payment-element-link.md): Link in the Payment Element lets your customers check out faster.
- [Link in the Card Element](https://docs.stripe.com/payments/link/card-element-link.md): Enable checkout using Link with the Card Element.
- [Link in the Mobile Payment Element](https://docs.stripe.com/payments/link/mobile-payment-element-link.md): Add Link to your native iOS, Android, and React Native apps.
- [Link in the Express Checkout Element](https://docs.stripe.com/payments/link/express-checkout-element-link.md): Let customers check out faster with Link and the Express Checkout Element.
- [Link with Invoicing](https://docs.stripe.com/payments/link/invoicing.md): Speed up invoice payments by using Link with the Hosted Invoice Page.
- [Explore the Link Authentication Element](https://docs.stripe.com/payments/link/link-authentication-element.md): Create a single email input for both email collection and Link authentication.


## Billing
Create and manage subscriptions, track usage, and issue invoices.

Stripe Billing is a tool for managing subscriptions and invoicing. It automates recurring payments, creates custom pricing plans, and handles billing cycles such as trials and renewals. Learn more about [Billing](https://docs.stripe.com/billing/billing-apis.md) and its [features](https://docs.stripe.com/billing.md#features).

- [Recurring pricing models](https://docs.stripe.com/products-prices/pricing-models.md): Learn about the pricing models you can use with subscriptions.
- [Prebuilt subscription page with Stripe Checkout](https://docs.stripe.com/billing/quickstart.md)
- [Coupons and promotion codes](https://docs.stripe.com/billing/subscriptions/coupons.md): Add discounts to subscriptions and subscription items using coupons and promotion codes.
- [Cancel subscriptions](https://docs.stripe.com/billing/subscriptions/cancel.md): Learn how to cancel existing subscriptions.
- [Setting the subscription billing cycle date](https://docs.stripe.com/billing/subscriptions/billing-cycle.md): Learn how to set the billing date for subscriptions.
- [Embeddable pricing table for subscriptions](https://docs.stripe.com/payments/checkout/pricing-table.md): Display a subscription pricing table on your website and take customers directly to Stripe Checkout.
- [Change the price of existing subscriptions](https://docs.stripe.com/billing/subscriptions/change-price.md): Learn how to upgrade and downgrade subscriptions by changing the price.
- [Create subscriptions](https://docs.stripe.com/no-code/subscriptions.md): Set up recurring payments by offering subscriptions to your service.
- [Modify subscriptions](https://docs.stripe.com/billing/subscriptions/change.md): Change existing subscriptions to cancel, pause, apply prorated charges and credits, and more.
- [Customer Tax IDs](https://docs.stripe.com/billing/customer/tax-ids.md): Learn how to store, validate, and render customer tax ID numbers with Stripe Billing.
- [Set up usage-based billing with products and prices](https://docs.stripe.com/billing/subscriptions/usage-based/implementation-guide.md): Charge customers based on their usage of your product or service.
- [Entitlements](https://docs.stripe.com/billing/entitlements.md): Determine when you can grant or revoke product feature access to customers.
- [Set payment methods per-subscription](https://docs.stripe.com/billing/subscriptions/payment-methods-setting.md): Learn how to specify which payment methods are available for a subscription.
- [Recurring pricing models](https://docs.stripe.com/products-prices/pricing-models.md): Learn about the pricing models you can use with subscriptions.
- [Customers](https://docs.stripe.com/billing/customer.md): Learn how to use the Customer resource with Stripe Billing.
- [Record usage for billing](https://docs.stripe.com/billing/subscriptions/usage-based/recording-usage.md): Learn how to record customer usage data.
- [Designing an integration](https://docs.stripe.com/billing/subscriptions/designing-integration.md): Learn what choices you need to make to integrate subscriptions into your business.
- [Pause payment collection](https://docs.stripe.com/billing/subscriptions/pause-payment.md): Learn how to pause payment collection on subscriptions.
- [Tax rates](https://docs.stripe.com/billing/taxes/tax-rates.md): Learn how to collect and report taxes with tax rate objects.
- [Migrate subscriptions to Stripe Billing](https://docs.stripe.com/billing/subscriptions/migrate-subscriptions.md): Learn about migrating subscriptions from other sources to Stripe.
- [Set payment methods per-subscription](https://docs.stripe.com/billing/subscriptions/payment-methods-setting.md): Learn how to specify which payment methods are available for a subscription.
- [Subscriptions with multiple products](https://docs.stripe.com/billing/subscriptions/multiple-products.md): Create subscriptions with multiple products, all billed in a single invoice.
- [Migrate your customer data to Stripe](https://docs.stripe.com/get-started/data-migrations.md): Successfully migrate your customers' data to Stripe.
- [Revenue recovery](https://docs.stripe.com/billing/revenue-recovery.md): Learn about automated recovery features that reduce and recover failed subscription payments.
- [Migrate subscriptions to Stripe Billing using toolkit](https://docs.stripe.com/billing/subscriptions/import-subscriptions-toolkit.md): Learn how to migrate your existing subscriptions to Stripe using the toolkit.
- [About the Billing APIs](https://docs.stripe.com/billing/billing-apis.md): Understand how the Billing API objects work together.
- [Subscriptions](https://docs.stripe.com/api/subscriptions.md)
- [The Subscription object](https://docs.stripe.com/api/subscriptions/object.md)
- [Create a subscription](https://docs.stripe.com/api/subscriptions/create.md)
- [Customers](https://docs.stripe.com/api/customers.md)
- [The Customer object](https://docs.stripe.com/api/customers/object.md)
- [Customer Balance Transaction](https://docs.stripe.com/api/customer_balance_transactions.md)
- [The Customer Balance Transaction object](https://docs.stripe.com/api/customer_balance_transactions/object.md)

## Elements
Create your own checkout flows with prebuilt UI components.

[Stripe Elements](https://stripe.com/payments/elements) is a set of prebuilt UI components for building your web checkout flow. It’s available as a feature of [Stripe.js](https://docs.stripe.com/js), our foundational JavaScript library for building payment flows. Stripe.js tokenizes sensitive payment details within an Element without ever having them touch your server.

You can use Elements with:

- The [Checkout Sessions API](https://docs.stripe.com/api/checkout/sessions.md) to [build a checkout page](https://docs.stripe.com/payments/checkout/build-integration.md).
- The [Payment Intents API](https://docs.stripe.com/api/payment_intents.md) to [build an advanced integration](https://docs.stripe.com/payments/advanced.md).

- [Address Element](https://docs.stripe.com/elements/address-element.md): Use the Address Element to collect complete billing and shipping addresses.
- [Link Authentication Element](https://docs.stripe.com/payments/elements/link-authentication-element.md): Use the Link Authentication Element to integrate Link.
- [Listen for address input](https://docs.stripe.com/elements/address-element/collect-addresses.md): Collect addresses to use in custom ways using an event listener.
- [Mobile Payment Element](https://docs.stripe.com/payments/elements/mobile-payment-element.md): Accept payments in your mobile app.
- [Migrate to Confirmation Tokens](https://docs.stripe.com/payments/payment-element/migration-ct.md): Finalize payments on the server by using a ConfirmationToken instead of a PaymentMethod.
- [Migrate from the Basic Integration to the Mobile Payment Element](https://docs.stripe.com/payments/mobile/migrating-to-mobile-payment-element-from-basic-integration.md): Upgrade your legacy mobile SDK integration before we remove it from the SDK.
- [Control billing details collection](https://docs.stripe.com/payments/payment-element/control-billing-details-collection.md): Customize the billing details you collect within the Payment Element.
- [Address Element](https://docs.stripe.com/elements/mobile/address-element.md): Use the Address Element to collect complete billing and shipping addresses for in-app integrations.
- [Collect physical addresses and phone numbers](https://docs.stripe.com/payments/advanced/collect-addresses.md): Learn how to collect addresses and phone numbers during one-time payment flows.
- [Collect physical addresses and phone numbers](https://docs.stripe.com/payments/mobile/collect-addresses.md): Learn how to collect addresses and phone number in your mobile app.
- [Use Payment Element across multiple processors](https://docs.stripe.com/payments/forwarding-third-party-processors.md): Learn how to collect card details with Payment Element and use them with a third-party processor.
- [Customize appearance](https://docs.stripe.com/elements/appearance-api/mobile.md): Customize your mobile integration with the Appearance API.
- [Charge for shipping](https://docs.stripe.com/payments/mobile/charge-shipping.md): Create different shipping rates for your customers.

## Connect
Use Connect to build a platform, marketplace, or other business that manages payments and moves money between multiple parties.

- [1099-NEC form state requirements](https://docs.stripe.com/connect/1099-NEC.md): View the state requirements for 1099-NEC forms.
- [Create a charge](https://docs.stripe.com/connect/charges.md): Create a charge and split payments between your platform and your sellers or service providers.
- [Required verification information](https://docs.stripe.com/connect/required-verification-information.md): Learn what required verification information you need to collect for each country when using Connect.
- [Connect account types](https://docs.stripe.com/connect/accounts.md): Learn about older connected account configurations.
- [US tax reporting for Connect platforms](https://docs.stripe.com/connect/tax-reporting.md): Learn how to report the annual payments for your US-based connected accounts.
- [How Connect works](https://docs.stripe.com/connect/how-connect-works.md): Learn how Connect's features support multiparty integrations.
- [Payouts to connected accounts](https://docs.stripe.com/connect/payouts-connected-accounts.md): Manage payouts and external accounts for your platform's connected accounts.
- [1099-K form state requirements](https://docs.stripe.com/connect/1099-K.md): View the state requirements for 1099-K forms.
- [Making API calls for connected accounts](https://docs.stripe.com/connect/authentication.md): Learn how to add the right information to your API calls so you can make calls for your connected accounts.
- [Using Connect with Standard connected accounts](https://docs.stripe.com/connect/standard-accounts.md): Use Standard connected accounts to get started using Connect right away, and let Stripe handle the majority of the connected account experience.
- [Choose your onboarding configuration](https://docs.stripe.com/connect/onboarding.md): Learn about the different options for onboarding your connected accounts.
- [Using Connect with Custom connected accounts](https://docs.stripe.com/connect/custom-accounts.md): Use Custom connected accounts with Connect to control your connected accounts' entire experience.
- [Express Dashboard](https://docs.stripe.com/connect/express-dashboard.md): Learn about the features of the Express Dashboard.
- [Stripe-hosted onboarding](https://docs.stripe.com/connect/hosted-onboarding.md): Onboard connected accounts by redirecting them to a Stripe-hosted onboarding flow.
- [Payment details](https://docs.stripe.com/connect/supported-embedded-components/payment-details.md): Show details of a given payment and allow users to manage disputes and perform refunds.
- [Identity verification for connected accounts](https://docs.stripe.com/connect/identity-verification.md): Use identity verification to reduce risk on your platform when using Connect.
- [Using manual payouts](https://docs.stripe.com/connect/manual-payouts.md): Send manual payouts to your connected accounts.
- [Connect webhooks](https://docs.stripe.com/connect/webhooks.md): Learn how to use webhooks with Connect to be notified of Stripe activity.
- [Manage connected accounts with the Dashboard](https://docs.stripe.com/connect/dashboard.md): Learn about using the Stripe Dashboard to find and manage connected accounts, including those with open risk, onboarding, and compliance requirements.
- [Get started with tax reporting](https://docs.stripe.com/connect/get-started-tax-reporting.md): Use the Stripe 1099 tax reporting product to create, modify, file, and deliver tax forms for your connected accounts.
- [Platform pricing tool](https://docs.stripe.com/connect/platform-pricing-tools.md): Set platform processing fees for your connected accounts from your Stripe Dashboard.
- [Update and create 1099 tax forms](https://docs.stripe.com/connect/modify-tax-forms.md): Update and create 1099 tax forms for connected accounts.
- [Balance](https://docs.stripe.com/api/balance.md)
- [The Balance object](https://docs.stripe.com/api/balance/balance_object.md)

## Issuing
Use the Stripe Issuing API to create, manage, and distribute payment cards for your business. Issuing is available in the United States, United Kingdom, and European Economic Area. You can create your own card designs, and approve transactions in real time. You can build Stripe Issuing alongside [Stripe Treasury](https://docs.stripe.com/treasury.md) to attach cards to open loop wallets, and offer your users additional money movement options.

- [How Issuing works](https://docs.stripe.com/issuing/how-issuing-works.md): Learn how to start building a card program with Stripe Issuing.
- [Issuing merchant categories](https://docs.stripe.com/issuing/categories.md): Learn about the available categories that businesses are grouped in.
- [Cardholder authentication using 3D Secure](https://docs.stripe.com/issuing/3d-secure.md): Learn about 3D Secure, an additional layer of authentication used by businesses to combat fraud.
- [Physical cards](https://docs.stripe.com/issuing/cards/physical.md): Issue physical cards at Stripe.
- [Use digital wallets with Issuing](https://docs.stripe.com/issuing/cards/digital-wallets.md): Learn how to use Issuing to add cards to digital wallets.
- [Using Issuing Elements](https://docs.stripe.com/issuing/elements.md): Learn how to display card details in your web application in a PCI-compliant way.
- [Issuing product marketing, design, and compliance guidelines](https://docs.stripe.com/issuing/compliance-us.md): Learn how to keep your Issuing program and marketing campaigns compliant.
- [Virtual cards with Issuing](https://docs.stripe.com/issuing/cards/virtual.md): Learn about virtual cards created with Issuing.
- [Choose which type of card to issue](https://docs.stripe.com/issuing/choose-cards.md): Decide on physical or virtual cards for your cardholders.
- [Use Stripe Issuing in different countries](https://docs.stripe.com/issuing/global.md): Learn how different integration options vary by country.
- [Issuing and Treasury sample app](https://docs.stripe.com/baas/start-integration/sample-app.md): Learn how to onboard customers, issue cards, and make outbound payments.
- [B2B Payments integration guide](https://docs.stripe.com/baas/start-integration/integration-guides/b2b-payments.md): Build a B2B Payments integration with Issuing.
- [Testing Issuing](https://docs.stripe.com/issuing/testing.md): Learn how to test your integration and simulate purchases.
- [Issuing and Treasury sample app](https://docs.stripe.com/treasury/examples/sample-app.md): Use the Stripe Next.js sample app to start your own Issuing and Treasury integration.
- [Manage fraud with Stripe Issuing controls and tools](https://docs.stripe.com/issuing/manage-fraud.md): Understand how transaction fraud can impact your Issuing program and the steps you can take to combat it.
- [Add funds to your card program](https://docs.stripe.com/issuing/adding-funds-to-your-card-program.md): Learn about your options to fund card spend.
- [Stripe Issuing marketing guidelines](https://docs.stripe.com/issuing/marketing-guidance-europe-uk.md): Learn about marketing guidelines for Issuing programs in the United Kingdom and Europe.
- [Order a custom bundle](https://docs.stripe.com/issuing/cards/physical/order-custom-bundle.md): Order your custom physical bundle.
- [Use cards at automated teller machines (ATMs)](https://docs.stripe.com/issuing/purchases/atm-usage.md): Learn how you can use your Stripe Issuing cards at ATMs.
- [Create a design](https://docs.stripe.com/issuing/cards/physical/create-design.md): Create and name your bundle design.
- [Onboarding overview](https://docs.stripe.com/baas/start-integration/onboarding-overview.md): Take your integration live.
- [Replacement cards](https://docs.stripe.com/issuing/cards/replacements.md): Learn how to replace cards that are expired, damaged, lost, or stolen.
- [Choose your physical bundle](https://docs.stripe.com/issuing/cards/choose-bundle.md): Set up a standard or custom physical bundle.
- [Update the Issuing terms of service acceptance](https://docs.stripe.com/issuing/connect/tos_acceptance.md): Learn how to present accurate business information for your connected accounts and accept the Issuing terms of service.
- [Card bundle options](https://docs.stripe.com/issuing/cards/physical/card-bundle-selections.md): Make your card bundle selections.
- [Issuing watchlist](https://docs.stripe.com/issuing/issuing-watchlist.md): Learn about the Issuing watchlist process and best practices.


## Capital
Stripe Capital provides access to financing options for eligible users processing payments through Stripe. Financing offers for eligible users are available in the Stripe Dashboard or through participating Connect platforms.

- [How Stripe Capital works](https://docs.stripe.com/capital/how-stripe-capital-works.md): Learn how Stripe Capital provides financing for eligible businesses.
- [Stripe Capital eligibility](https://docs.stripe.com/capital/eligibility.md): Learn more about our offer eligibility criteria.
- [Set up Capital](https://docs.stripe.com/capital/getting-started.md): Determine which integration option to use when you set up Stripe Capital.
- [How Stripe Capital for Platforms works](https://docs.stripe.com/capital/how-capital-for-platforms-works.md): Learn the basics of Stripe Capital for Platforms.
- [Managing customer support](https://docs.stripe.com/capital/servicing.md): Support your Capital customers using Stripe-approved messaging.
- [Capital financing](https://docs.stripe.com/connect/supported-embedded-components/capital-financing.md): Allow a connected account to view and manage their active Capital financing.
- [Build a custom Capital program](https://docs.stripe.com/capital/api-integration.md): Integrate with our API to build a custom Capital program.
- [Capital financing application](https://docs.stripe.com/connect/supported-embedded-components/capital-financing-application.md): Show an end-to-end application flow for Capital financing.
- [Regulatory compliance guidelines](https://docs.stripe.com/capital/regulatory-compliance.md): Learn about the requirements and guidelines for regulatory compliance.
- [Marketing your Capital program](https://docs.stripe.com/capital/marketing.md): Build marketing assets for Capital.
- [Capital financing promotion](https://docs.stripe.com/connect/supported-embedded-components/capital-financing-promotion.md): Show promotional content about a connected account's Capital financing offer and launch a Capital application.
- [Capital metrics](https://docs.stripe.com/capital/reporting.md): Access financing offer data in the Stripe Dashboard.
- [Refills](https://docs.stripe.com/capital/refills.md): Learn how to enable refills for your Capital program.
- [Replacements](https://docs.stripe.com/capital/replacements.md): Learn how to handle financing offer replacements.

## Crypto
Pay with Crypto works with *Checkout*, *Elements*, or can be directly integrated through the *Payment Intents API*. When integrated, the option to pay with **Crypto** appears that redirects your customers to a page hosted by *crypto.link.com* to complete their payment.
- [Stablecoin payments](https://docs.stripe.com/crypto/stablecoin-payments.md): Let your customers pay with crypto that settle as fiat in your Stripe balance.
- [Accept a stablecoin payment](https://docs.stripe.com/crypto/accept-stablecoin-payments.md): Start accepting stablecoins by integrating the Crypto payment method.
- [Stripe-hosted, standalone onramp quickstart](https://docs.stripe.com/crypto/onramp/standalone-onramp-quickstart.md): Customize and generate a redirect URL to the Stripe-hosted, standalone onramp.
- [Set up an emeddable onramp integration](https://docs.stripe.com/crypto/onramp/emeddable-onramp-guide.md): Use this guide to fully customize the embeddable onramp.
- [Integrate crypto for mobile](https://docs.stripe.com/crypto/onramp/mobile-integration.md): Configure the onramp for mobile use.
- [Back-end integration best practices](https://docs.stripe.com/crypto/onramp/backend-best-practices.md): Safely integrate the onramp for different web3 use cases.
- [Install the Stripe Crypto SDK ES Module](https://docs.stripe.com/crypto/onramp/esmodule.md): Set up the Stripe crypto client-side SDK in your web application.

## Climate
Stripe Climate is the easiest way to help emerging permanent carbon removal technologies scale. Join a growing group of ambitious businesses changing the course of carbon removal.
- [Climate Commitments](https://docs.stripe.com/climate/commitments.md): Direct a fraction of your revenue to help advance carbon removal.
- [Climate Orders overview](https://docs.stripe.com/climate/orders.md): Pre-order carbon removal tons from Frontier's offtake portfolio
- [Order carbon removal](https://docs.stripe.com/climate/orders/order-carbon-removal.md): Pre-order carbon removal tons from Frontier's offtake portfolio.
- [Carbon removal inventory](https://docs.stripe.com/climate/orders/carbon-removal-inventory.md): Learn about available carbon removal inventory.
- [Webhooks for Climate Orders API](https://docs.stripe.com/climate/orders/webhooks.md): Learn about webhook events for products and orders.
- [Climate Orders quickstart](https://docs.stripe.com/climate/orders/quickstart.md): Enable your customers to buy carbon removal using the Climate API with your payments integration.
- [How Climate Orders work](https://docs.stripe.com/climate/orders/how-it-works.md): Learn how to create, monitor, and manage your carbon removal orders.


## Tax
Automate sales tax, VAT, and GST compliance on all your transactions—low or no code integrations available. You can integrate Stripe Tax with Payment Links or the Checkout Sessions API and use it for subscriptions and invoices. You can also create custom payment flows or integrate Stripe Tax as a platform with Connect.
- [Set up Stripe Tax](https://docs.stripe.com/tax/set-up.md): Enable Stripe Tax to automatically calculate and collect tax.
- [Product tax codes](https://docs.stripe.com/tax/tax-codes.md): Stripe Tax uses product tax codes to determine a product's tax rate.
- [How Tax works](https://docs.stripe.com/tax/how-tax-works.md): Learn how Stripe Tax helps you automate tax compliance.
- [Tax API for Sales Tax, GST, and VAT](https://docs.stripe.com/tax/custom.md): Use Stripe Tax APIs to implement tax calculations in your custom integration.
- [Specify product tax codes and tax behavior](https://docs.stripe.com/tax/products-prices-tax-codes-tax-behavior.md): Add tax codes and tax behavior to your products and prices to automatically calculate tax.
- [Zero tax amounts and reverse charges](https://docs.stripe.com/tax/zero-tax.md): Learn about cases when Stripe Tax calculates zero tax.
- [Countries supported by Stripe Tax](https://docs.stripe.com/tax/supported-countries.md): Learn where you can use Stripe Tax.
- [Automatically collect tax on Checkout sessions](https://docs.stripe.com/tax/checkout.md): Learn how to automatically calculate taxes in Checkout.
- [Calculate tax](https://docs.stripe.com/tax/calculating.md): Learn how to calculate tax with Stripe Tax.
- [Stripe Tax FAQ](https://docs.stripe.com/tax/faq.md): Read the frequently asked questions about Stripe Tax.
- [Automatically collect tax on invoices](https://docs.stripe.com/tax/invoicing.md): Learn how to automatically calculate tax on your invoices.
- [Use Stripe Tax with Connect](https://docs.stripe.com/tax/connect.md): Understand how Stripe Tax can help your platform and your connected accounts comply with tax obligations.
- [Testing Stripe Tax](https://docs.stripe.com/tax/testing.md): Learn how to test your Stripe Tax integration.
- [Collect taxes for recurring payments](https://docs.stripe.com/tax/subscriptions.md): Learn how to collect and report taxes for recurring payments.
- [Tax customizations](https://docs.stripe.com/tax/tax-customizations.md): Learn how to customize tax behavior using Stripe Tax.
- [Tax for software platforms](https://docs.stripe.com/tax/tax-for-platforms.md): Learn how to enable Stripe Tax for your connected accounts, and collect tax when the connected account is liable for paying the tax.
- [Collect customer tax IDs with Checkout](https://docs.stripe.com/tax/checkout/tax-ids.md): Learn how to collect VAT and other customer tax IDs with Checkout.
- [Automatically collect tax on Payment Links](https://docs.stripe.com/tax/payment-links.md): Learn how to calculate and collect tax on a payment page without writing any code.
- [Collect taxes for recurring payments](https://docs.stripe.com/billing/taxes/collect-taxes.md): Learn how to collect and report taxes for recurring payments.
- [Account and customer tax IDs with Invoicing](https://docs.stripe.com/tax/invoicing/tax-ids.md): Learn about storing, validating, and rendering tax ID numbers for Invoicing.
- [Update existing subscriptions](https://docs.stripe.com/tax/subscriptions/update.md): Learn how to update existing subscriptions to Stripe Tax.
- [Tax for marketplaces](https://docs.stripe.com/tax/tax-for-marketplaces.md): Learn about tax requirements for platforms and marketplaces, and how to enable Stripe Tax to collect tax on transactions when the Connect platform is liable.
- [Stripe Tax](https://docs.stripe.com/tax.md): Automate sales tax, VAT, and GST compliance on all your transactions—low or no code integrations available.
- [Use the Settings API to configure Stripe Tax](https://docs.stripe.com/tax/settings-api.md): Learn how to configure tax settings, and check whether an account is ready to perform tax calculations.
- [Tax settings](https://docs.stripe.com/connect/supported-embedded-components/tax-settings.md): Learn how to allow connected accounts to set up Stripe Tax.
- [Collect tax in Asia Pacific](https://docs.stripe.com/tax/supported-countries/asia-pacific.md): Learn how to use Stripe Tax to calculate, collect, and report tax in the Asia Pacific region.
- [Tax registrations](https://docs.stripe.com/connect/supported-embedded-components/tax-registrations.md): Learn how to allow connected accounts to manage their tax registrations for Stripe Tax.
- [Configure the extension](https://docs.stripe.com/use-stripe-apps/woocommerce/configuration.md): Configure the Stripe Tax Extension for WooCommerce.
- [Use the Registrations API to manage tax registrations](https://docs.stripe.com/tax/registrations-api.md): Learn how to add, schedule, and check active tax registrations.
- [Stripe Tax Extension for WooCommerce](https://docs.stripe.com/use-stripe-apps/woocommerce.md): Learn about the Stripe Tax Extension for WooCommerce.
- [Collect tax in Australia](https://docs.stripe.com/tax/supported-countries/asia-pacific/australia.md): Learn how to use Stripe Tax to calculate, collect, and report tax in Australia.
- [Collect tax in Mexico](https://docs.stripe.com/tax/supported-countries/latin-america-and-caribbean/mexico.md): Learn how to use Stripe Tax to calculate, collect, and report tax in Mexico.
- [The Tax ID object](https://docs.stripe.com/api/tax_ids/object.md)

## Invoicing
Create and manage invoices for one-time payments with Stripe Invoicing. Invoices provide an itemized list of goods and services rendered, which includes the cost, quantity, and taxes. You can send invoices to customers to collect payment or you can create an invoice and automatically charge a customer’s saved payment method. Subscriptions automatically generate invoices for each billing cycle. Learn more about the invoice lifecycle for subscriptions. You can use both the Dashboard and the API to create, edit, and manage invoices.
- [Taxes](https://docs.stripe.com/invoicing/taxes.md): Learn about Stripe Tax and how to use it with invoices.
- [Customize invoices](https://docs.stripe.com/invoicing/customize.md): Learn how to customize the content and branding of your invoices.
- [Send customer emails](https://docs.stripe.com/invoicing/send-email.md): Configure and send invoicing emails to your customers.
- [Automatic invoice advancement](https://docs.stripe.com/invoicing/integration/automatic-advancement-collection.md): Learn how Stripe Invoicing handles automatic advancement and collection.
- [Issue credit notes](https://docs.stripe.com/invoicing/dashboard/credit-notes.md): Use the Dashboard to adjust or refund finalized invoices with credit notes.
- [Integrate with the Invoicing API](https://docs.stripe.com/invoicing/integration.md): Learn how to create and send an invoice with code.
- [No-code Invoicing guide](https://docs.stripe.com/invoicing/no-code-guide.md): Get started with Stripe Invoicing—no code required.
- [Automatic charging](https://docs.stripe.com/invoicing/automatic-charging.md): Have Stripe automatically charge a customer's stored payment method.
- [Manage invoices](https://docs.stripe.com/invoicing/dashboard/manage-invoices.md): Learn how to manage your invoices in the Dashboard.
- [Create and send an invoice](https://docs.stripe.com/invoicing/integration/quickstart.md): Build an example Invoicing integration.
- [Invoice Rendering Templates](https://docs.stripe.com/invoicing/invoice-rendering-template.md): Use Invoice Rendering Templates to personalize your invoice appearance for different customers
- [Status transitions and finalization](https://docs.stripe.com/invoicing/integration/workflow-transitions.md): Learn about invoice status transitions and finalization.
- [Invoicing and ACH Direct Debit](https://docs.stripe.com/invoicing/ach-direct-debit.md): Configure, create, and process invoices using ACH Direct Debit.
- [Use invoices](https://docs.stripe.com/no-code/invoices.md): Send an invoice your customers can pay online.
- [Stripe Connector for NetSuite](https://docs.stripe.com/use-stripe-apps/netsuite/overview.md): Use the app to reconcile your Stripe activity into NetSuite.
- [Customer tax IDs](https://docs.stripe.com/invoicing/customer/tax-ids.md): Store, validate, and render customer tax ID numbers with Stripe Invoicing.
- [Best practices for global invoices](https://docs.stripe.com/invoicing/global-invoices.md): Learn the best practices for setting up invoices in non-US regions.
- [Customers](https://docs.stripe.com/invoicing/customer.md): Learn how to use the Customer resource with Stripe Invoicing.
- [Multi-currency customers](https://docs.stripe.com/invoicing/multi-currency-customers.md): Change the billable currency for any customer to accept multiple currencies.
- [Products and prices](https://docs.stripe.com/invoicing/products-prices.md): Use the Invoicing API to manage products and prices.
- [Test Stripe Invoicing](https://docs.stripe.com/invoicing/integration/testing.md): Learn how to test your Invoicing integration.
- [Scheduled payments](https://docs.stripe.com/invoicing/hosted-invoice-page/scheduled-payments.md): Let your customers schedule their payments through the Hosted Invoice Page.
- [Schedule invoice finalization to send or charge an invoice in the future](https://docs.stripe.com/invoicing/scheduled-finalization.md): Learn how to schedule an invoice to automatically charge or send to a customer.
- [Preview an invoice](https://docs.stripe.com/invoicing/preview.md): Learn how to create a preview of an invoice.
- [Troubleshoot the connector](https://docs.stripe.com/use-stripe-apps/netsuite/error-resolution.md): Learn how to troubleshoot errors with the Stripe Connector for NetSuite.
- [Send quotes](https://docs.stripe.com/no-code/quotes.md): Send a quote and convert it to a payment or subscription.
- [Account tax IDs](https://docs.stripe.com/invoicing/taxes/account-tax-ids.md): Store and render your tax IDs with Stripe Invoicing.
- [Stripe Billing and Invoicing automation](https://docs.stripe.com/use-stripe-apps/netsuite/invoice-automation.md): Use the connector to sync your Stripe invoices into NetSuite.
- [Manage bulk invoice line items](https://docs.stripe.com/invoicing/bulk-update-line-item.md): Add, update and remove multiple invoice line items with the Invoices API.
- [Tax rates and IDs](https://docs.stripe.com/invoicing/taxes/tax-rates.md): Assign tax rates to draft invoices for automatic tax calculation.
- [Deposit automation](https://docs.stripe.com/use-stripe-apps/netsuite/deposit-automation.md): Use the connector to automate the bank reconciliation process.
- [Generate credit notes programmatically](https://docs.stripe.com/invoicing/integration/programmatic-credit-notes.md): Use the Invoicing API to adjust or refund finalized invoices with credit notes.
- [Custom payment application](https://docs.stripe.com/use-stripe-apps/netsuite/custom-payment-application.md): Learn how to customize the way payments are recorded and applied using the Stripe Connector for NetSuite.
- [Invoices](https://docs.stripe.com/api/invoices.md)
- [The Invoice object](https://docs.stripe.com/api/invoices/object.md)


## Identity
Use Stripe Identity to confirm the identity of global users to prevent fraud, streamline risk operations, and increase trust and safety. Stripe Identity allows you to Verify the authenticity of ID documents from more than 120 countries, Capture IDs with a conversion-optimized verification flow, Match photo IDs with selfies, and validate Social Security numbers (SSNs), and Access collected images, and extracted data from ID documents

- [Verify your users’ identity documents](https://docs.stripe.com/identity/verify-identity-documents.md): Create sessions and collect identity documents.
- [Verification flows](https://docs.stripe.com/identity/verification-flows.md): Apply a reusable configuration across your integration.
- [Verification checks](https://docs.stripe.com/identity/verification-checks.md): Learn about the different verification checks supported by Stripe Identity.
- [Review tools](https://docs.stripe.com/identity/review-tools.md): Learn how to use manual reviews to supplement programmatic systems with human expertise.
- [Supported uses cases and locations for Stripe Identity](https://docs.stripe.com/identity/use-cases.md): Learn about the verification use cases and business locations supported by Stripe Identity.
- [Access verification results](https://docs.stripe.com/identity/access-verification-results.md): Learn how to access sensitive verification results.
- [Explain Identity to your customers](https://docs.stripe.com/identity/explaining-identity.md): Answer customer questions about ID verification and Stripe Identity.
- [The Verification Sessions API](https://docs.stripe.com/identity/verification-sessions.md): Learn more about the Verification Sessions API that powers Stripe Identity.
- [Before going live](https://docs.stripe.com/identity/before-going-live.md): Best practices to build a production-ready Stripe Identity integration.
- [Identity verification (redirect)](https://docs.stripe.com/samples/identity/redirect.md)

## Atlas
Use Stripe Atlas to incorporate your company in Delaware, obtain your company tax ID (EIN) from the IRS, issue founders equity, and file your 83(b) election. After you incorporate your business, you can open a business bank account and charge customers through Stripe payments.

Stripe Atlas doesn’t provide legal, tax, or accounting advice. If you have any unique considerations, consider talking to legal counsel before proceeding.
- [How to incorporate your company](https://docs.stripe.com/atlas/signup.md): Learn what you need to get started with Atlas.
- [Business bank accounts](https://docs.stripe.com/atlas/payments-business-bank.md): Apply to open a business bank account with our partners.
- [Business taxes](https://docs.stripe.com/atlas/business-taxes.md): Business tax basics for startup founders.
- [Company types](https://docs.stripe.com/atlas/company-types.md): Form a C corporation or an LLC using Stripe Atlas.
- [Section 83(b) elections](https://docs.stripe.com/atlas/83b-election.md): Learn about common considerations for Section 83(b) elections.
- [Incorporation documents](https://docs.stripe.com/atlas/incorporation-documents.md): Learn about the documents Atlas uses to incorporate your company.
- [File Section 83(b) elections as a non-US founder](https://docs.stripe.com/atlas/83b-elections-non-us-founders.md): Learn about filing an 83(b) election as a non-US founder.

## Financial Connections
Stripe Financial Connections allows users to securely share their financial data with your business. You can use one integration to instantly verify bank accounts for ACH payments, reduce underwriting risk with [balances](https://docs.stripe.com/financial-connections/balances.md) data, mitigate fraud by verifying account [ownership](https://docs.stripe.com/financial-connections/ownership.md) details, and build new fintech products with [transactions](https://docs.stripe.com/financial-connections/transactions.md) data.

Financial Connections enables your users to connect their accounts in fewer steps with [Link](https://support.stripe.com/questions/link-for-financial-connections-support-for-businesses), allowing them to save and quickly reuse their bank account details across Stripe businesses.
- [Collect a bank account to use ACH Direct Debit payments with account data](https://docs.stripe.com/financial-connections/ach-direct-debit-payments.md): Use account data such as balances with your payments integration.
- [Financial Connections use cases](https://docs.stripe.com/financial-connections/use-cases.md): View options for integrating Financial Connections and common use cases.
- [Financial Connections fundamentals](https://docs.stripe.com/financial-connections/fundamentals.md): Learn how Financial Connections works.
- [Supported institutions of Financial Connections](https://docs.stripe.com/financial-connections/supported-institutions.md): View the details of supported financial institutions.
- [Collect a bank account to enhance Connect payouts](https://docs.stripe.com/financial-connections/connect-payouts.md): Collect your connected account's bank account and use account data to enhance payouts.
- [Test Financial Connections](https://docs.stripe.com/financial-connections/testing.md): Learn how to test your integration with simulated Financial Connections accounts.
- [Disconnect a Financial Connections account](https://docs.stripe.com/financial-connections/disconnections.md): Use the Disconnect API to unlink customer bank accounts.

## Revenue Recognition
Automate your accrual accounting process with Stripe Revenue Recognition.Revenue recognition is a fundamental part of accrual accounting. Generally accepted accounting principles (GAAP) state that you recognize revenue when you realize and earn it, which might be earlier or later than when you actually receive cash. Correctly recognizing and deferring revenue enables you to have the most accurate insights into your business profitability and financial health. Revenue recognition is critical for many types of businesses, especially: Public companies or large businesses with over 25 million USD in annual revenue—because they’re legally required to comply with ASC 606 and GAAP and IFRS accounting standards, Startups that need to follow accrual accounting to raise money from investors or get a loan from a bank, Subscription and service-based businesses and Businesses where customers pay up front before receiving a good or service.

- [Revenue Recognition methodology](https://docs.stripe.com/revenue-recognition/methodology.md): Learn how revenue recognition works within Stripe.
- [Monthly summary](https://docs.stripe.com/revenue-recognition/reports/monthly-summary.md): Learn about the monthly summary report.
- [Revenue Recognition reports](https://docs.stripe.com/revenue-recognition/reports.md): Generate and export revenue reports using Stripe Revenue Recognition.
- [Recognize revenue with Stripe](https://docs.stripe.com/revenue-recognition/get-started.md): Learn how to use Stripe for your revenue recognition.
- [Revenue Recognition with subscriptions and invoicing](https://docs.stripe.com/revenue-recognition/methodology/subscriptions-and-invoicing.md): Learn how revenue recognition works with subscriptions and invoices.
- [Pricing](https://docs.stripe.com/revenue-recognition/pricing.md): Learn about fees and pricing tiers for Stripe Revenue Recognition.
- [Revenue waterfall](https://docs.stripe.com/revenue-recognition/reports/waterfall.md): View and analyze monthly revenue with the waterfall report.
- [Revenue Recognition examples](https://docs.stripe.com/revenue-recognition/examples.md): Learn about revenue recognition using some common examples.
- [Income statement](https://docs.stripe.com/revenue-recognition/reports/income-statement.md): Analyze revenue, expenses, and net income with the income statement report.
- [Revenue Recognition rules](https://docs.stripe.com/revenue-recognition/rules.md): Customize rules to handle revenue treatments to your business.
- [Map to your chart of accounts](https://docs.stripe.com/revenue-recognition/chart-of-accounts.md): Map transactions from the Stripe default accounts to the chart of accounts in your general ledger.
- [Audit your numbers](https://docs.stripe.com/revenue-recognition/reports/audit-numbers.md): Use the Stripe Dashboard to examine the details of your revenue numbers.
- [Revenue Recognition API](https://docs.stripe.com/revenue-recognition/api.md): Access Stripe Revenue Recognition reports programmatically to automate your accrual accounting.
- [Revenue Recognition accounting period control](https://docs.stripe.com/revenue-recognition/revenue-settings/accounting-period-control.md): Learn how to configure accounting periods for Stripe Revenue Recognition.
- [Stripe Connector for the Apple App Store](https://docs.stripe.com/revenue-recognition/data-import/apple-app-store.md): Manage your revenue recognition in Stripe by importing data from the Apple App Store.
- [Revenue Recognition with one-time payments](https://docs.stripe.com/revenue-recognition/methodology/one-time-payments.md): Manage revenue recognition for one-time payments by importing custom service periods.
- [Revenue Recognition for Connect platforms](https://docs.stripe.com/revenue-recognition/connect.md): Learn how revenue recognition works with Connect platforms.
- [Revenue Recognition with multiple currencies](https://docs.stripe.com/revenue-recognition/methodology/multi-currency.md): Understand the roles of presentment and settlement currencies in Stripe Revenue Recognition.
- [Trial balance](https://docs.stripe.com/revenue-recognition/reports/trial-balance.md): View and analyze account balances over specific periods with the trial balance report.
- [Data reconciliation with Stripe reports](https://docs.stripe.com/revenue-recognition/data-reconciliation.md): Learn how to reconcile revenue recognition data with other financial reports.
- [Revenue Recognition with refunds and disputes](https://docs.stripe.com/revenue-recognition/methodology/refunds-and-disputes.md): Understand how refunds and disputes impact your revenue.
- [Revenue Recognition Performance Obligations API](https://docs.stripe.com/revenue-recognition/performance-obligations-api.md): Learn how to model performance obligation fulfillment in Stripe Revenue Recognition.
- [Manage imported data](https://docs.stripe.com/revenue-recognition/data-import/manage-imported-data.md): Search for and manage existing imported data.
- [Revenue Recognition for direct charges](https://docs.stripe.com/revenue-recognition/connect/direct-charges.md): Learn how revenue recognition works with direct charges.
- [Revenue Recognition for destination charges](https://docs.stripe.com/revenue-recognition/connect/destination-charges.md): Learn how revenue recognition works with destination charges.
- [Stripe Connector for Google Play](https://docs.stripe.com/revenue-recognition/data-import/google-play.md): Manage your revenue recognition in Stripe by importing data from Google Play.
- [Revenue Recognition for separate charges and transfers](https://docs.stripe.com/revenue-recognition/connect/charges-transfers.md): Learn how revenue recognition works with separate charges and transfers.
- [Revenue Recognition transaction overrides](https://docs.stripe.com/revenue-recognition/overrides.md): Learn how to make manual corrections to your revenue recognition reports.
- [Error handling for data import](https://docs.stripe.com/revenue-recognition/data-import/error-handling.md): Learn how to handle and recover from errors received when importing revenue recognition data.
- [Custom reports by Sigma and SDP](https://docs.stripe.com/revenue-recognition/reports/sigma-and-sdp.md): Learn how to build your own Revenue Recognition reports using Sigma and SDP.

## Treasury
Learn how to provide financial services to connected accounts. Stripe Treasury is a Banking as a Service (BaaS) API for Stripe *Connect* platforms that allows you to embed financial services in your product. Stripe provides the infrastructure in partnership with trusted banks. Use Treasury to enable your connected accounts to hold funds, pay bills, earn cash back, and manage their cash flow. Many platforms using Connect also use [Stripe Issuing](https://docs.stripe.com/issuing.md) to issue cards for accessing Treasury accounts. To learn more about Treasury, see its [features](#features).

- [Treasury requirements](https://docs.stripe.com/treasury/requirements.md): Understand the requirements for using Stripe Treasury.
- [Using Treasury to move money](https://docs.stripe.com/treasury/examples/moving-money.md): Learn how to use SetupIntents, PaymentMethods, and verify bank accounts with Stripe Treasury.
- [Payouts and top-ups from Stripe Payments](https://docs.stripe.com/treasury/moving-money/payouts.md): Learn how to move money between Payments account balances and Treasury financial account balances.
- [Treasury product marketing, design, and compliance guidelines](https://docs.stripe.com/treasury/compliance.md): Learn how to keep your Treasury program and marketing campaigns compliant.
- [Money movement timelines](https://docs.stripe.com/treasury/money-movement/timelines.md): Learn about the timelines for various types of money movement in Treasury.
- [Moving money with Treasury using InboundTransfer objects](https://docs.stripe.com/treasury/moving-money/financial-accounts/into/inbound-transfers.md): Learn how to transfer money from another account you own into a Treasury financial account.
- [Working with SetupIntents, PaymentMethods, and BankAccounts](https://docs.stripe.com/treasury/moving-money/working-with-bankaccount-objects.md): Set up money movements in Treasury.
- [Working with Treasury financial accounts](https://docs.stripe.com/treasury/account-management/financial-accounts.md): Use financial accounts to store, send, and receive funds.
- [Moving money into financial accounts](https://docs.stripe.com/treasury/moving-money/moving-money-into-financial-accounts.md): Learn the requests available to move money into financial accounts.
- [Treasury fraud guide](https://docs.stripe.com/treasury/examples/fraud-guide.md): Learn best practices for managing fraud as a Treasury platform.
- [Working with balances and transactions](https://docs.stripe.com/treasury/account-management/working-with-balances-and-transactions.md): Learn about Treasury account balances and the effect transactions have on them.
- [Working with connected accounts in Treasury](https://docs.stripe.com/treasury/account-management/connected-accounts.md): Request the Treasury capability and collect onboarding requirements for your connected accounts.
- [Moving money with Treasury using OutboundPayment objects](https://docs.stripe.com/treasury/moving-money/financial-accounts/out-of/outbound-payments.md): Learn how to create outbound payments to move money out of Treasury financial accounts to third parties.
- [Customize your migration to Fifth Third Bank](https://docs.stripe.com/treasury/fifth-third-migration.md): Develop a custom plan to migrate your existing Stripe Treasury integration to Fifth Third Bank.
- [Use Treasury and Issuing to set up financial accounts and cards](https://docs.stripe.com/treasury/examples/financial-accounts.md): Follow a sample Treasury and Issuing integration that sets up a financial account and creates cards.
- [Treasury fees report](https://docs.stripe.com/reports/treasury-fees-reporting.md): Analyze Treasury fees at an itemized level.
- [Treasury connected account onboarding guide](https://docs.stripe.com/treasury/examples/onboarding-guide.md): Learn how to onboard your connected accounts.
- [Get started with API access to Treasury](https://docs.stripe.com/treasury/access.md): Test in a sandbox environment to experiment before going live.
- [Moving money with Treasury using OutboundTransfer objects](https://docs.stripe.com/treasury/moving-money/financial-accounts/out-of/outbound-transfers.md): Learn how to transfer money out of Treasury financial accounts to external accounts.
- [Marketing Treasury-based services](https://docs.stripe.com/treasury/marketing-treasury.md): Create precise messaging for your users that complies with regulations.
- [Platform financial accounts](https://docs.stripe.com/treasury/account-management/platform-financial-account.md): Learn about the financial account for your platform.
- [Regulatory receipts](https://docs.stripe.com/treasury/moving-money/regulatory-receipts.md): Learn about hosted transaction receipts.
- [Stripe Treasury accounts structure](https://docs.stripe.com/treasury/account-management/treasury-accounts-structure.md): Learn how the account components of Treasury interact.
- [Handling complaints](https://docs.stripe.com/treasury/handling-complaints.md): Learn how to properly handle complaints about Stripe Treasury or Stripe Issuing.
- [Moving money with Treasury using ReceivedCredit objects](https://docs.stripe.com/treasury/moving-money/financial-accounts/into/received-credits.md): Learn how to move money into a Treasury financial account from another Treasury financial account or bank account.
- [Working with Stripe Issuing cards](https://docs.stripe.com/treasury/account-management/issuing-cards.md): Learn how to integrate Stripe Issuing with Treasury.
- [Webhooks for Stripe Issuing and Stripe Treasury](https://docs.stripe.com/treasury/examples/webhooks.md): Learn about webhook events for Stripe Issuing and Stripe Treasury and why they occur.
- [Moving money out of Treasury financial accounts](https://docs.stripe.com/treasury/moving-money/moving-money-out-of-financial-accounts.md): Learn the requests available to move money out of financial accounts.
- [Build a new Treasury integration with Fifth Third Bank](https://docs.stripe.com/treasury/fifth-third-get-started.md): Get started with Stripe Treasury.
- [Moving money with Treasury using ReceivedDebit objects](https://docs.stripe.com/treasury/moving-money/financial-accounts/out-of/received-debits.md): Learn how external account holders can pull funds from a Treasury financial account.
- [Moving money with Treasury using CreditReversal objects](https://docs.stripe.com/treasury/moving-money/financial-accounts/into/credit-reversals.md): Learn how you can return funds from received credits that add money to your Treasury financial account.
- [Moving money with Treasury using DebitReversal objects](https://docs.stripe.com/treasury/moving-money/financial-accounts/out-of/debit-reversals.md): Learn how you can retrieve funds taken out of a Treasury financial account from an external account holder.
- [ACH Standard Entry Class (SEC) handling](https://docs.stripe.com/treasury/moving-money/standard-entry-class.md): Learn how SEC codes are determined for ACH transfers.
- [Connected account Treasury supportability](https://docs.stripe.com/treasury/account-management/supportability.md): Learn how Stripe evaluates connected accounts for Treasury supportability.
- [Testing financial account integration](https://docs.stripe.com/treasury/account-management/testing-financial-account-integration.md): Learn how to ensure your financial accounts are functioning correctly.
- [Remote check acceptance](https://docs.stripe.com/treasury/money-movement/remote-deposit-capture.md): Learn how to accept checks into your financial account.
- [ACH Notification of Change handling](https://docs.stripe.com/treasury/moving-money/notification-of-change.md): Learn how external account information is updated.
- [Test a remote check acceptance integration](https://docs.stripe.com/treasury/money-movement/remote-deposit-capture-testing.md): Use a sandbox to test your integration and transition statuses.
- [Remote deposit capture check review guidelines](https://docs.stripe.com/treasury/money-movement/remote-deposit-capture-check-review-guide.md): Learn best practices for reviewing checks.



## Sigma
Use Sigma to generate custom reports for charges, refunds, disputes, and more.

The available data within Sigma is read-only. Queries can’t modify existing data or create new transactions.

[Sigma](https://dashboard.stripe.com/sigma/queries) makes all your transactional data available within an interactive SQL environment in the Stripe Dashboard. It lets you create fully customized [reports](https://docs.stripe.com/stripe-reports.md) using information about your payments, subscriptions, customers, payouts, and so on.

With Sigma, you can:
- Get information that best reflects your business and Stripe integration.
- Export in CSV format to import into your tools.
- Fetch data on a schedule of your choosing.

- [View API request logs](https://docs.stripe.com/development/dashboard/request-logs.md): Filter API request logs and view log entries in the Developers Dashboard.
- [Acceptance analytics](https://docs.stripe.com/payments/analytics/acceptance.md): Understand what affects card payment acceptance and the reasons behind payment failures or declines.
- [View events and event object payloads](https://docs.stripe.com/development/dashboard/events.md): View events triggered by your account and their event object payload in the Developers Dashboard.
- [Scheduled reports](https://docs.stripe.com/reports/scheduled-reports.md): Set up report subscriptions to get notified when new data is ready.
- [Migrate queries](https://docs.stripe.com/stripe-data/migrate-queries.md): Migrate your Sigma queries from Presto to Trino.

## Payment Links
Sell online without building a digital storefront.

Accept a payment or sell subscriptions without building additional standalone websites or applications with Payment Links. Share the link as many times as you want on social media, in emails, or on your website.

Payment Links supports [more than 20 payment methods](https://docs.stripe.com/payments/payment-methods/payment-method-support.md#product-support)—including credit and debit cards, Apple Pay, and Google Pay. The payment link automatically uses your customer’s preferred browser language in [more than 30 languages](https://support.stripe.com/questions/supported-languages-for-stripe-checkout-and-payment-links). Learn more about the [features](#features), and create a payment link in the demo below.

- [Collect physical addresses and phone numbers](https://docs.stripe.com/payments/no-code/collect-addresses.md): Learn how to collect addresses and phone numbers without writing code.
- [Charge for shipping](https://docs.stripe.com/payments/no-code/charge-shipping.md): Create different shipping rates for your customers.
- [Payment Link](https://docs.stripe.com/api/payment-link.md)
- [The Payment Link object](https://docs.stripe.com/api/payment-link/object.md)

## Radar
Use Stripe Radar to protect your business against fraud.

[Stripe Radar](https://stripe.com/radar) provides real-time fraud protection and requires no additional development time. [Radar for Fraud Teams](https://stripe.com/radar/fraud-teams) adds customization capabilities and deeper insights and trend analysis for your business.

Radar evaluates transactions in real-time, using machine learning algorithms to assess the risk of fraud. Both pricing tiers of Radar [charge a fee](https://stripe.com/radar/pricing) for each transaction it evaluates. Radar screens all types of payment attempts such as successful, declined, blocked, and flagged for review.

- [Understand fraud](https://docs.stripe.com/disputes/prevention.md): Learn how to identify fraud, and design a strategy to prevent it.
- [Fraud prevention rules](https://docs.stripe.com/radar/rules.md): Use fraud prevention rules to guard your business.
- [Send complete fraud signals](https://docs.stripe.com/radar/integration.md): Learn about Stripe's recommendations for using Stripe Radar to send a complete set of fraud signals.
- [Supported attributes](https://docs.stripe.com/radar/rules/supported-attributes.md): Review a complete list of attributes supported in Radar rules.
- [Dispute and fraud card monitoring programs](https://docs.stripe.com/disputes/monitoring-programs.md): Learn about the monitoring programs operated by the card networks, and what to do if you're placed into one.
- [Protect yourself from card testing](https://docs.stripe.com/disputes/prevention/card-testing.md): Learn about this fraudulent activity and how to protect yourself against it.
- [Rules reference](https://docs.stripe.com/radar/rules/reference.md): Learn about the structure of rules and the order in which Radar processes them.
- [Best practices for preventing fraud](https://docs.stripe.com/disputes/prevention/best-practices.md): Learn how to use best practices to protect against disputes and fraudulent payments.
- [Card verification checks](https://docs.stripe.com/disputes/prevention/verification.md): Learn how to make use of card verification checks to protect against disputes and fraud.
- [Measuring disputes](https://docs.stripe.com/disputes/measuring.md): Learn about the potential issues that occur from excessive disputes, and how to avoid them.
- [Risk controls](https://docs.stripe.com/radar/risk-settings.md): Adjust how aggressively you block fraud for your business with Radar for Fraud Teams.
- [Advanced fraud detection](https://docs.stripe.com/disputes/prevention/advanced-fraud-detection.md): Learn about tools developers can use to maximize Stripe's ability to prevent fraudulent payments.
- [Common types of online fraud](https://docs.stripe.com/disputes/prevention/fraud-types.md): Learn about the different kinds of fraud and what your liability is.
- [Identifying potential fraud](https://docs.stripe.com/disputes/prevention/identifying-fraud.md): Learn about the most common fraud indicators.
- [Lists](https://docs.stripe.com/radar/lists.md): Create your own lists of information to block, allow, or review matching payments.
- [High risk merchant lists](https://docs.stripe.com/disputes/match.md): Learn the criteria for inclusion in MATCH and VMSS lists.
- [Risk insights](https://docs.stripe.com/radar/reviews/risk-insights.md): Understand risk factors and details about a particular payment.
- [Reviewing uncaptured payments](https://docs.stripe.com/radar/reviews/auth-and-capture.md): Learn how to use reviews if your Stripe integration uses auth and capture.
- [Provide Radar additional fraud data](https://docs.stripe.com/radar/radar-session.md): Learn how to provide critical data for improved fraud protection.
- [Testing Stripe Radar](https://docs.stripe.com/radar/testing.md): Use the following information to test your fraud prevention strategy.
- [Disputes on Connect platforms](https://docs.stripe.com/connect/disputes.md): Learn about the dispute responsibilities on Connect platforms.
- [Use the API to respond to disputes](https://docs.stripe.com/disputes/api.md): Learn how to manage disputes programmatically.
- [Radar analytics center](https://docs.stripe.com/radar/analytics.md): Review fraud patterns and their impact on your business in the Dashboard.
- [Visa Compelling Evidence 3.0 disputes](https://docs.stripe.com/disputes/api/visa-ce3.md): Use the API and Visa's Compelling Evidence 3.0 to respond to qualifying disputes.
- [Fraud insights](https://docs.stripe.com/radar/analytics/fraud-insights.md): Review fraud trends specific to your business so you can tailor your strategy.
- [Visa compliance disputes](https://docs.stripe.com/disputes/api/visa-compliance.md): Use the API to respond to Visa compliance disputes.


## Terminal
Use Stripe Terminal to accept in-person payments and extend Stripe payments to your point of sale.

Stripe Terminal allows businesses to accept in-person card payments using card readers. You can manage both in-person payments and online payments in a unified system in the [Dashboard](https://dashboard.stripe.com/). You can also integrate Terminal with your [Connect platform](#platform).

Learn more about Terminal’s [features](https://docs.stripe.com/terminal.md#features) and [availability by country](https://docs.stripe.com/terminal/overview.md#availability).

- [Stripe Terminal reader product sheets](https://docs.stripe.com/terminal/readers/product-sheets.md): Learn about Stripe Terminal hardware specifications.
- [Set up Stripe Reader M2](https://docs.stripe.com/terminal/payments/setup-reader/stripe-m2.md): Learn how to set up the Stripe Reader M2.
- [Accept in-person payments](https://docs.stripe.com/terminal/quickstart.md)
- [BBPOS WisePOS E](https://docs.stripe.com/terminal/readers/bbpos-wisepos-e.md): Learn about the BBPOS WisePOS E reader.
- [Collect and save payment details for future use](https://docs.stripe.com/terminal/features/saving-payment-details/overview.md): Use your Stripe Terminal integration to collect and save payment methods for returning customers.
- [Stripe Reader S700](https://docs.stripe.com/terminal/readers/stripe-reader-s700.md): Learn about Stripe Reader S700.
- [Apps on Devices](https://docs.stripe.com/terminal/features/apps-on-devices/overview.md): Learn about deploying your Android POS apps on Stripe smart readers.
- [Set up BBPOS WisePad 3](https://docs.stripe.com/terminal/payments/setup-reader/bbpos-wisepad3.md): Learn how to set up the BBPOS WisePad 3.
- [Provide receipts](https://docs.stripe.com/terminal/features/receipts.md): Use Stripe to provide your customers with receipts that meet card network rules.
- [Manage locations](https://docs.stripe.com/terminal/fleet/locations-and-zones.md): Group and manage your readers by physical location.
- [Example applications](https://docs.stripe.com/terminal/example-applications.md): Try Stripe Terminal by using the example applications and simulated reader.
- [Stripe Reader M2](https://docs.stripe.com/terminal/readers/stripe-m2.md): Learn about the Stripe Reader M2 Bluetooth reader.
- [Terminal network requirements](https://docs.stripe.com/terminal/network-requirements.md): Make sure your network is ready for Terminal, and troubleshoot common issues.
- [Stripe Terminal smart readers](https://docs.stripe.com/terminal/smart-readers.md): Learn about Stripe's pre-certified in-person payment readers.
- [Build and test your app](https://docs.stripe.com/terminal/features/apps-on-devices/build.md): Learn how to build and test your app using a DevKit.
- [Stripe Terminal mobile readers](https://docs.stripe.com/terminal/mobile-readers.md): Learn about Stripe's pre-certified in-person payment mobile readers.
- [BBPOS WisePad 3](https://docs.stripe.com/terminal/readers/bbpos-wisepad3.md): Learn about the BBPOS WisePad 3 reader.
- [Terminal SDK migration guide](https://docs.stripe.com/terminal/references/sdk-migration-guide.md): Learn how to migrate to the latest version of the Stripe Terminal SDK.

## Optional
- [Support](https://support.stripe.com)
- [Changelog](https://docs.stripe.com/changelog.md): Keep track of changes and upgrades to the Stripe API.