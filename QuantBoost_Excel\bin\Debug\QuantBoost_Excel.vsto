﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="QuantBoost_Excel.vsto" version="*******" publicKeyToken="35286dbe605b394c" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="QuantBoost_Excel" asmv2:product="QuantBoost_Excel" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.8.1" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="QuantBoost_Excel.dll.manifest" size="18400">
      <assemblyIdentity name="QuantBoost_Excel.dll" version="*******" publicKeyToken="35286dbe605b394c" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>oZTN+Xdnm7K1WiqTma+/mocTNxr7MpYmmGuEkhmS9iQ=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=M16R2\danep" issuerKeyHash="fb1cf8e3ea2558782aeb9ff223a32b39697e5d46" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>1fyg4m4r190BpO5tOiq4W5k5vJVyNp4/7tKHwpV8coc=</DigestValue></Reference></SignedInfo><SignatureValue>NZuh1oLxqoUfxkM2ZpvI3O1gZ4dY3HUcgvkVq74hJlrH4jULew5WMXEffs0JFpjQXsdv/bsg6/IIXrdHAtMShD9z7Dve9umADhF2riY75YRDZs2CA1tjSlWvRNNSLyWKfyq1ZfYPV+dh/lDdRokwY/1VD66pfOL/LQ60+3uIpmM=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>6ZS32si3AmGNwmMPpzPviLGMoZipu0qfnQTDciDdzqPQh1493u6/VBlWO8+uiiNK5H0TAwnKz9uy7hpyjytmS0CDV8VFpaj4CQTkhL1DfCmie+x0TFCil52Ge/a36oqY0q4WUlCD3o9ynvYCyELz6t67OwQvs2vaqqzcTUVMirU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="87727c95c287d2ee3f9e367295bc39995bb82a3a6deea401ddd72b6ee2a0fcd5" Description="" Url=""><as:assemblyIdentity name="QuantBoost_Excel.vsto" version="*******" publicKeyToken="35286dbe605b394c" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=M16R2\danep</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>n2IZ6Samnh4TY1l1krrSZMk2OsKhZmzewUEtKAikeNA=</DigestValue></Reference></SignedInfo><SignatureValue>z/mnQ9y5jHEmGiO2/joiX5FBX3MGZJuC+XV7DGei2iR5fEHhpPjMfbjMzO9gmfS3EyP6js+6yN0Kv5UhYP6h75pIH/OTgZC5vwXltlFXVjMSrb4H1Wrs07VYca3auteg9XWfE+9r2gWgyr97EZMjNT39MBpaaySNznsyEFjXiws=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>6ZS32si3AmGNwmMPpzPviLGMoZipu0qfnQTDciDdzqPQh1493u6/VBlWO8+uiiNK5H0TAwnKz9uy7hpyjytmS0CDV8VFpaj4CQTkhL1DfCmie+x0TFCil52Ge/a36oqY0q4WUlCD3o9ynvYCyELz6t67OwQvs2vaqqzcTUVMirU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIBxTCCAS6gAwIBAgIQefRSxrT3V4FPYvc4+lI2KjANBgkqhkiG9w0BAQsFADAhMR8wHQYDVQQDHhYATQAxADYAUgAyAFwAZABhAG4AZQBwMB4XDTI1MDYwNzIxMTIzOVoXDTI2MDYwODAzMTIzOVowITEfMB0GA1UEAx4WAE0AMQA2AFIAMgBcAGQAYQBuAGUAcDCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEA6ZS32si3AmGNwmMPpzPviLGMoZipu0qfnQTDciDdzqPQh1493u6/VBlWO8+uiiNK5H0TAwnKz9uy7hpyjytmS0CDV8VFpaj4CQTkhL1DfCmie+x0TFCil52Ge/a36oqY0q4WUlCD3o9ynvYCyELz6t67OwQvs2vaqqzcTUVMirUCAwEAATANBgkqhkiG9w0BAQsFAAOBgQAH9xipr0rjmSp9F74lEjLPk3sUU9U0VrNe2sd2VArGDPHK6Yfi7dRs+ebsbPyKuWjXIIMPpeyMeZw4a0ThIu/Ygqyoj2Tp8N8IRWbIZZ+6Ryt0HUrCqRMadBgLSrWAtNsR7FOLBzSjGJAQzwuPWJF+5nnUYTM2UE4RItIyIosfOg==</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>