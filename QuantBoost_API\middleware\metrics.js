const telemetry = require('../utils/telemetry');
const logger = require('../utils/logger');

const metricsMiddleware = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    // Track metrics
    telemetry.trackMetric('http_request_duration_ms', duration, {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode
    });
    
    telemetry.trackEvent('http_request', {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('user-agent'),
      ip: req.ip || req.connection.remoteAddress,
      requestId: req.requestId
    });

    // Track errors separately
    if (res.statusCode >= 400) {
      telemetry.trackEvent('http_error', {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        requestId: req.requestId
      });
    }
  });
  
  next();
};

module.exports = metricsMiddleware;