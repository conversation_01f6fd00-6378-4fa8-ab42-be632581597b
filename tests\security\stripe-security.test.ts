import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { StripeService } from '../../src/billing/stripe.service';
import { SecurityMonitoringService } from '../../src/security/security-monitoring.service';
import <PERSON>e from 'stripe';
import * as crypto from 'crypto';

describe('Stripe Security Implementation', () => {
  let stripeService: StripeService;
  let securityService: SecurityMonitoringService;
  let configService: ConfigService;
  let module: TestingModule;

  const mockWebhookSecret = 'whsec_test_secret';
  const mockStripeSecretKey = 'sk_test_123456789';

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        StripeService,
        SecurityMonitoringService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              switch (key) {
                case 'STRIPE_SECRET_KEY':
                  return mockStripeSecretKey;
                case 'STRIPE_WEBHOOK_SECRET':
                  return mockWebhookSecret;
                default:
                  return undefined;
              }
            }),
          },
        },
      ],
    }).compile();

    stripeService = module.get<StripeService>(StripeService);
    securityService = module.get<SecurityMonitoringService>(SecurityMonitoringService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('Webhook Signature Verification', () => {
    it('should validate correct webhook signature', () => {
      const payload = JSON.stringify({ type: 'payment_intent.succeeded' });
      const timestamp = Math.floor(Date.now() / 1000);
      const signature = generateValidSignature(payload, timestamp, mockWebhookSecret);

      expect(() => {
        stripeService.validateWebhookSignature(payload, signature);
      }).not.toThrow();
    });

    it('should reject invalid webhook signature', () => {
      const payload = JSON.stringify({ type: 'payment_intent.succeeded' });
      const invalidSignature = 't=1234567890,v1=invalid_signature';

      expect(() => {
        stripeService.validateWebhookSignature(payload, invalidSignature);
      }).toThrow('Webhook signature verification failed');
    });

    it('should reject missing signature', () => {
      const payload = JSON.stringify({ type: 'payment_intent.succeeded' });

      expect(() => {
        stripeService.validateWebhookSignature(payload, '');
      }).toThrow();
    });

    it('should reject tampered payload', () => {
      const originalPayload = JSON.stringify({ type: 'payment_intent.succeeded' });
      const tamperedPayload = JSON.stringify({ type: 'payment_intent.succeeded', amount: 999999 });
      const timestamp = Math.floor(Date.now() / 1000);
      const signature = generateValidSignature(originalPayload, timestamp, mockWebhookSecret);

      expect(() => {
        stripeService.validateWebhookSignature(tamperedPayload, signature);
      }).toThrow('Webhook signature verification failed');
    });
  });

  describe('Idempotency Key Generation', () => {
    it('should generate consistent keys for identical parameters', () => {
      const key1 = stripeService.generateIdempotencyKey('cus_123', 1000, 'usd');
      const key2 = stripeService.generateIdempotencyKey('cus_123', 1000, 'usd');
      
      expect(key1).toBe(key2);
    });

    it('should generate different keys for different customers', () => {
      const key1 = stripeService.generateIdempotencyKey('cus_123', 1000, 'usd');
      const key2 = stripeService.generateIdempotencyKey('cus_456', 1000, 'usd');
      
      expect(key1).not.toBe(key2);
    });

    it('should generate different keys for different amounts', () => {
      const key1 = stripeService.generateIdempotencyKey('cus_123', 1000, 'usd');
      const key2 = stripeService.generateIdempotencyKey('cus_123', 2000, 'usd');
      
      expect(key1).not.toBe(key2);
    });

    it('should generate different keys for different currencies', () => {
      const key1 = stripeService.generateIdempotencyKey('cus_123', 1000, 'usd');
      const key2 = stripeService.generateIdempotencyKey('cus_123', 1000, 'eur');
      
      expect(key1).not.toBe(key2);
    });

    it('should generate keys of correct format', () => {
      const key = stripeService.generateIdempotencyKey('cus_123', 1000, 'usd');
      
      expect(key).toMatch(/^[a-f0-9]{32}$/);
    });
  });

  describe('Email Validation', () => {
    it('should accept valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(() => {
          stripeService.validateCustomerEmail(email);
        }).not.toThrow();
      });
    });

    it('should reject blocked email patterns', () => {
      const blockedEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      blockedEmails.forEach(email => {
        expect(() => {
          stripeService.validateCustomerEmail(email);
        }).toThrow(`Blocked email pattern: ${email}`);
      });
    });

    it('should warn about unusual domains', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      stripeService.validateCustomerEmail('<EMAIL>');
      
      // Note: In the actual implementation, this would be a logger warning
      // For testing, we'd need to mock the logger properly
      
      consoleSpy.mockRestore();
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within rate limit', () => {
      const customerId = 'cus_test_123';
      const eventType = 'payment_intent';

      // Should allow first 5 requests
      for (let i = 0; i < 5; i++) {
        expect(() => {
          stripeService.checkRateLimit(customerId, eventType);
        }).not.toThrow();
      }
    });

    it('should block requests exceeding rate limit', () => {
      const customerId = 'cus_test_456';
      const eventType = 'payment_intent';

      // Make 5 requests to reach the limit
      for (let i = 0; i < 5; i++) {
        stripeService.checkRateLimit(customerId, eventType);
      }

      // 6th request should be blocked
      expect(() => {
        stripeService.checkRateLimit(customerId, eventType);
      }).toThrow(`Rate limit exceeded for ${customerId}:${eventType}`);
    });

    it('should have separate rate limits per customer', () => {
      const customer1 = 'cus_test_789';
      const customer2 = 'cus_test_abc';
      const eventType = 'payment_intent';

      // Exhaust rate limit for customer1
      for (let i = 0; i < 5; i++) {
        stripeService.checkRateLimit(customer1, eventType);
      }

      // customer2 should still have available requests
      expect(() => {
        stripeService.checkRateLimit(customer2, eventType);
      }).not.toThrow();
    });

    it('should have separate rate limits per event type', () => {
      const customerId = 'cus_test_def';
      const eventType1 = 'payment_intent';
      const eventType2 = 'subscription';

      // Exhaust rate limit for payment_intent
      for (let i = 0; i < 5; i++) {
        stripeService.checkRateLimit(customerId, eventType1);
      }

      // subscription events should still be allowed
      expect(() => {
        stripeService.checkRateLimit(customerId, eventType2);
      }).not.toThrow();
    });
  });

  describe('Anomaly Detection', () => {
    it('should detect rapid cancellations', () => {
      const events = [
        createMockEvent('customer.subscription.deleted'),
        createMockEvent('customer.subscription.deleted'),
        createMockEvent('customer.subscription.deleted'),
        createMockEvent('customer.subscription.deleted'),
      ];

      const anomalies = stripeService.detectAnomalies(events);
      
      expect(anomalies).toContain('Suspicious pattern detected: rapidCancellations');
    });

    it('should detect guest email usage', () => {
      const events = [
        createMockEvent('customer.created', { email: '<EMAIL>' }),
      ];

      const anomalies = stripeService.detectAnomalies(events);
      
      expect(anomalies).toContain('Suspicious pattern detected: guestEmails');
    });

    it('should detect unusual amounts', () => {
      const events = [
        createMockEvent('payment_intent.succeeded', { amount: 999999 }),
      ];

      const anomalies = stripeService.detectAnomalies(events);
      
      expect(anomalies).toContain('Suspicious pattern detected: unusualAmounts');
    });

    it('should return empty array for normal activity', () => {
      const events = [
        createMockEvent('payment_intent.succeeded', { amount: 12000 }),
        createMockEvent('customer.created', { email: '<EMAIL>' }),
      ];

      const anomalies = stripeService.detectAnomalies(events);
      
      expect(anomalies).toHaveLength(0);
    });
  });

  describe('Security Monitoring', () => {
    it('should generate unique incident IDs', async () => {
      const incident1 = await securityService.reportIncident({
        type: 'stripe_security_incident',
        severity: 'low',
        title: 'Test Incident 1',
        details: 'Test details',
        source: 'test',
        actions_taken: ['logged'],
      });

      const incident2 = await securityService.reportIncident({
        type: 'stripe_security_incident',
        severity: 'low',
        title: 'Test Incident 2',
        details: 'Test details',
        source: 'test',
        actions_taken: ['logged'],
      });

      expect(incident1).not.toBe(incident2);
      expect(incident1).toMatch(/^INC-[A-Z0-9]+-[A-Z0-9]+$/);
      expect(incident2).toMatch(/^INC-[A-Z0-9]+-[A-Z0-9]+$/);
    });

    it('should track incident statistics correctly', async () => {
      await securityService.reportIncident({
        type: 'stripe_security_incident',
        severity: 'high',
        title: 'High Severity Test',
        details: 'Test details',
        source: 'test',
        actions_taken: ['logged'],
      });

      await securityService.reportIncident({
        type: 'authentication_failure',
        severity: 'low',
        title: 'Low Severity Test',
        details: 'Test details',
        source: 'test',
        actions_taken: ['logged'],
      });

      const stats = securityService.getIncidentStatistics();

      expect(stats.total).toBe(2);
      expect(stats.by_severity.high).toBe(1);
      expect(stats.by_severity.low).toBe(1);
      expect(stats.by_type['stripe_security_incident']).toBe(1);
      expect(stats.by_type['authentication_failure']).toBe(1);
    });

    it('should retrieve recent incidents', async () => {
      const incidentId = await securityService.reportIncident({
        type: 'stripe_security_incident',
        severity: 'medium',
        title: 'Recent Test',
        details: 'Test details',
        source: 'test',
        actions_taken: ['logged'],
      });

      const recentIncidents = securityService.getRecentIncidents(10);

      expect(recentIncidents).toHaveLength(1);
      expect(recentIncidents[0].id).toBe(incidentId);
      expect(recentIncidents[0].title).toBe('Recent Test');
    });
  });

  describe('Error Handling', () => {
    it('should handle Stripe API errors gracefully', async () => {
      // Mock Stripe to throw an error
      jest.spyOn(stripeService['stripe'].paymentIntents, 'create').mockRejectedValue(
        new Error('Your card was declined.')
      );

      await expect(
        stripeService.createPaymentIntent({
          amount: 1000,
          currency: 'usd',
          customerId: 'cus_test',
          customerEmail: '<EMAIL>',
        })
      ).rejects.toThrow('Your card was declined.');
    });

    it('should log security alerts for errors', async () => {
      const alertSpy = jest.spyOn(stripeService, 'sendSecurityAlert').mockResolvedValue();

      jest.spyOn(stripeService['stripe'].paymentIntents, 'create').mockRejectedValue(
        new Error('API Error')
      );

      try {
        await stripeService.createPaymentIntent({
          amount: 1000,
          currency: 'usd',
          customerId: 'cus_test',
          customerEmail: '<EMAIL>',
        });
      } catch (error) {
        // Expected to throw
      }

      expect(alertSpy).toHaveBeenCalledWith({
        severity: 'medium',
        details: 'Payment intent creation failed: API Error',
        actions: ['Logged error', 'Customer notified'],
        customerId: 'cus_test',
      });

      alertSpy.mockRestore();
    });
  });

  describe('Input Validation', () => {
    it('should validate payment intent parameters', async () => {
      const invalidParams = [
        { amount: 0, currency: 'usd', customerId: 'cus_test', customerEmail: '<EMAIL>' },
        { amount: 1000, currency: '', customerId: 'cus_test', customerEmail: '<EMAIL>' },
        { amount: 1000, currency: 'usd', customerId: '', customerEmail: '<EMAIL>' },
        { amount: 1000, currency: 'usd', customerId: 'cus_test', customerEmail: 'invalid-email' },
      ];

      for (const params of invalidParams) {
        await expect(
          stripeService.createPaymentIntent(params as any)
        ).rejects.toThrow();
      }
    });

    it('should validate subscription parameters', async () => {
      const invalidParams = [
        { customerId: '', priceId: 'price_test', customerEmail: '<EMAIL>' },
        { customerId: 'cus_test', priceId: '', customerEmail: '<EMAIL>' },
        { customerId: 'cus_test', priceId: 'price_test', customerEmail: 'invalid-email' },
      ];

      for (const params of invalidParams) {
        await expect(
          stripeService.createSubscription(params as any)
        ).rejects.toThrow();
      }
    });
  });

  // Helper functions
  function generateValidSignature(payload: string, timestamp: number, secret: string): string {
    const signature = crypto
      .createHmac('sha256', secret)
      .update(`${timestamp}.${payload}`)
      .digest('hex');
    
    return `t=${timestamp},v1=${signature}`;
  }

  function createMockEvent(type: string, objectData: any = {}): Stripe.Event {
    return {
      id: `evt_${Math.random().toString(36).substring(7)}`,
      object: 'event',
      type: type as any,
      data: {
        object: objectData,
      },
      created: Math.floor(Date.now() / 1000),
      livemode: false,
      pending_webhooks: 0,
      request: null,
    } as Stripe.Event;
  }
});

describe('Integration Tests', () => {
  describe('End-to-End Webhook Processing', () => {
    it('should process a complete webhook flow', async () => {
      // This would test the entire flow from webhook receipt to processing
      // In a real implementation, this would use test fixtures and mock external services
    });
  });

  describe('Rate Limiting Integration', () => {
    it('should handle concurrent requests properly', async () => {
      // Test concurrent requests hitting rate limits
      // This would involve multiple async operations
    });
  });

  describe('Security Alert Integration', () => {
    it('should send alerts to configured channels', async () => {
      // Test alert delivery to various channels
      // This would mock external webhook endpoints
    });
  });
});

describe('Performance Tests', () => {
  describe('Webhook Processing Performance', () => {
    it('should process webhooks within acceptable time limits', async () => {
      const startTime = Date.now();
      
      // Process a webhook
      const payload = JSON.stringify({ type: 'payment_intent.succeeded' });
      const timestamp = Math.floor(Date.now() / 1000);
      const secret = 'whsec_test_secret';
      const signature = crypto
        .createHmac('sha256', secret)
        .update(`${timestamp}.${payload}`)
        .digest('hex');

      // This would test actual webhook processing time
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      expect(processingTime).toBeLessThan(1000); // Should process within 1 second
    });
  });

  describe('Rate Limiting Performance', () => {
    it('should handle rate limiting checks efficiently', () => {
      const startTime = Date.now();
      
      // Perform multiple rate limit checks
      for (let i = 0; i < 1000; i++) {
        stripeService.checkRateLimit(`cus_${i}`, 'test');
      }
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      expect(processingTime).toBeLessThan(100); // Should be very fast
    });
  });
});