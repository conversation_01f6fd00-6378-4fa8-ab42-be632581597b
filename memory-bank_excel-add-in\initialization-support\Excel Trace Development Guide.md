# QuantBoost Excel Trace Add-in  
### Enhanced Development Guide v3.0 (Aligned with Shared Licensing SDK & Best Practices)  
_Last updated: April 8, 2025_

---

## Overview

Excel Trace aims to deliver a premium VSTO-based experience for instantly tracing cell dependents with a customizable shortcut key and rich UI panel. It incorporates unified SaaS subscription licensing via the QuantBoost Licensing SDK.

---

## Table of Contents

- [1. Key Architectural Notes](#1-key-architectural-notes)  
- [2. Development Phases](#2-development-phases)  
   - [Phase 1: Solution Setup](#phase-1-solution-setup)  
   - [Phase 2: Dependency Tracing Features](#phase-2-dependency-tracing-features)  
   - [Phase 3: Task Pane UI](#phase-3-task-pane-ui)  
   - [Phase 4: Licensing Integration & UX](#phase-4-licensing-integration--ux)  
   - [Phase 5: Finalization, Testing, Deployment](#phase-5-finalization-testing-deployment)  
- [3. Incorporated Enhancements](#3-incorporated-enhancements)  
- [4. Next Steps](#4-next-steps)  

---

## 1. Key Architectural Notes

- Reliance on **shared QuantBoost Licensing SDK** for all licensing calls, caching, grace logic.
- All license validation is **async, cached, resilient** with opt-in UI prompts.
- Licensing SDK controls feature gating, grace period handling and renewal/upgrade prompts.
- Trace logic uses **async calls** with background processing to keep UI responsive.
- UI design with **non-blocking toasts, proactive status messaging**, and deep links to subscription portal.
- Plan for **opt-in anonymous usage telemetry** & error logs.
- Clean plugin **startup** — licensing calls begin async and do not delay load.
- Graceful fallback behavior on API/network/service errors.
- Supports easy future upgrades: license feature tiers, telemetry, enterprise seat controls.

---

## 2. Development Phases

---

### Phase 1: Solution Setup

- [ ] VSTO add-in project
- [x] **Shared Licensing SDK ready**
- [ ] Add SDK reference
- [ ] Ribbon scaffold

---

### Phase 2: Dependency Tracing Features

- [ ] Implement `DependencyTracer` service:  
  - Efficiently call `cell.Dependents` COM APIs **safely with try/catch**.  
  - Build **DependentInfo** class with sheet, cell, formula snippet etc.  
  - Multi-sheet and multi-area support.  
  - **Async background task** off UI thread with cancellation support.  
  - Gracefully handle no selection/applicable cases.  

- [ ] Hotkey integration:  
  - Register default `Ctrl+Shift+[` on startup, only if **license valid or grace allowed**.

