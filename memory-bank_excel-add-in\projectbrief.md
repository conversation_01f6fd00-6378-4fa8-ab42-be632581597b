---
title: Project Brief for QuantBoost Excel Add-in
project_name: QuantBoost_Excel
projects:
  - excel-add-in
status: bootstrapping
purpose: Provides a high-level overview of the QuantBoost Excel Add-in, its planned features, and its role within the QuantBoost suite.
source_analysis: Initial setup based on development documents and project plan for a new add-in.
last_updated: 2025-05-13T17:00:00Z
tags: ["excel-add-in", "Core", "Brief"]
---

## Project Pitch

The QuantBoost Excel add-in is a key component of the QuantBoost suite, designed to bring powerful productivity and analysis tools directly into Microsoft Excel. It complements the PowerPoint add-in by offering specialized features for spreadsheet management, auditing, and optimization. Like the rest of the suite, it will be governed by the QuantBoost SaaS subscription model, aiming to provide high-value features at a competitive price point.

## Planned Features

The initial version of the QuantBoost Excel add-in will focus on delivering the following core features:

*   **CleanExcel (Name Scrubber):** Enables users to identify and manage hidden, broken, error, and external defined names within Excel workbooks. This feature aims to improve workbook health, security, and reduce file bloat.
*   **Excel Trace:** Provides an efficient way to trace cell precedents and dependents, offering a richer and more interactive experience than native Excel tools, including a customizable shortcut key.
*   **Sheet Size Analyzer:** Helps users understand the file size contribution of individual sheets and elements within a workbook (e.g., images, charts, PivotTables) using OpenXML analysis where possible, and COM-based heuristics as a fallback.

These features are designed to address common pain points for heavy Excel users, enhancing their efficiency and control over their workbooks.
