'use client'; // Required for onClick handlers

import { useState } from 'react';
import Link from 'next/link';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Badge
} from "@/components/ui";
import Image from 'next/image'; // For header logo
import { useRouter } from 'next/navigation';

// Billing frequency type
type BillingFrequency = 'annual' | 'quarterly';

// Simplified product structure - Individual and Team plans use same price IDs
const planTypes = [
  {
    id: 'individual',
    name: 'Individual',
    description: 'Perfect for individual users',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
    pricing: {
      annual: { price: 120, period: 'per year', stripePriceId: 'price_1RC3HTE6FvhUKV1bE9D6zf6e' },
      quarterly: { price: 45, period: 'per quarter', stripePriceId: 'price_1RCQeBE6FvhUKV1bUN94Oihf' }
    }
  },
  {
    id: 'team',
    name: 'Team',
    description: 'For teams and organizations',
    features: ['All Modules', 'License Management', 'Priority Support', 'Volume Discounts'],
    pricing: {
      annual: { price: 120, period: 'per user/year', stripePriceId: 'price_1RC3HTE6FvhUKV1bE9D6zf6e' },
      quarterly: { price: 45, period: 'per user/quarter', stripePriceId: 'price_1RCQeBE6FvhUKV1bUN94Oihf' }
    }
  }
];

export default function PricingPage() {
  const [isLoading, setIsLoading] = useState<string | null>(null); // Store the loading priceId
  const [billingFrequency, setBillingFrequency] = useState<BillingFrequency>('annual');
  const [teamQuantity, setTeamQuantity] = useState(5); // Default team size
  const router = useRouter();

  const handleBuyNow = async (priceId: string, planId: string) => {
    setIsLoading(priceId);
    try {
      // For team plans, include quantity in the URL
      const url = planId === 'team'
        ? `/checkout/${priceId}?quantity=${teamQuantity}`
        : `/checkout/${priceId}`;
      router.push(url);
    } catch (error) {
      console.error('Error navigating to checkout:', error);
      // Handle error - show message to user?
    } finally {
      setIsLoading(null);
    }
  };

  // Calculate savings for annual billing
  const calculateAnnualSavings = (annualPrice: number, quarterlyPrice: number) => {
    const quarterlyYearly = quarterlyPrice * 4;
    const savings = quarterlyYearly - annualPrice;
    const percentage = Math.round((savings / quarterlyYearly) * 100);
    return { savings, percentage };
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
       {/* Header Section - Added */}
      <header className="px-4 lg:px-6 h-14 flex items-center border-b sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <Link href="/" className="flex items-center justify-center" prefetch={false}>
          <Image src="/QuantBoost_LogoOnly_v0.png" alt="QuantBoost Logo" width={32} height={32} />
          <span className="sr-only">QuantBoost</span>
        </Link>
        <nav className="ml-auto flex gap-4 sm:gap-6 items-center">
          <Link href="/pricing" className="text-sm font-medium hover:underline underline-offset-4 text-primary" prefetch={false}>
            Pricing
          </Link>
          <Link href="/features" className="text-sm font-medium hover:underline underline-offset-4" prefetch={false}>
            Features
          </Link>
          <Link href="/training" className="text-sm font-medium hover:underline underline-offset-4" prefetch={false}>
            Training
          </Link>
          <Link href="/help" className="text-sm font-medium hover:underline underline-offset-4" prefetch={false}>
            Help
          </Link>
          <Link
            href="/auth/login"
            className="text-sm font-medium hover:underline underline-offset-4"
            prefetch={false}
          >
            Login
          </Link>
          <Button asChild>
            <Link href="/start-trial">Start Free Trial</Link>
          </Button>
        </nav>
      </header>

      {/* Pricing Section */}
      <main className="flex-1 py-12 md:py-24 lg:py-32">
        <div className="container mx-auto px-4 md:px-6 max-w-7xl">
          {/* Header Content - Centered */}
          <div className="text-center space-y-4 mb-12 max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Choose Your Plan</h1>
            <p className="max-w-[700px] mx-auto text-muted-foreground md:text-xl">
              Simple, transparent pricing. Unlock powerful features to boost your productivity.
            </p>
          </div>

          {/* Billing Frequency Toggle - Centered */}
          <div className="flex justify-center items-center mb-12">
            <div className="bg-muted p-1 rounded-lg inline-flex shadow-sm">
              <button
                onClick={() => setBillingFrequency('quarterly')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                  billingFrequency === 'quarterly'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                Quarterly
              </button>
              <button
                onClick={() => setBillingFrequency('annual')}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-all relative ${
                  billingFrequency === 'annual'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                Annual
                <Badge className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1.5 py-0.5">
                  33% OFF
                </Badge>
              </button>
            </div>
          </div>

          {/* Pricing Cards Container - Centered with Constraints */}
          <div className="flex justify-center">
            <div className="w-full max-w-5xl">
              {/* Simplified Plan Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8 px-4 sm:px-6 lg:px-8">
            {planTypes.map((plan) => {
              const currentPricing = plan.pricing[billingFrequency];
              const savings = billingFrequency === 'annual'
                ? calculateAnnualSavings(plan.pricing.annual.price, plan.pricing.quarterly.price)
                : null;

              return (
                <Card
                  key={plan.id}
                  className={`group flex flex-col relative cursor-pointer transition-all duration-300 hover:border-black hover:shadow-lg hover:scale-[1.02] ${plan.id === 'team' ? 'border-primary' : ''}`}
                  onClick={() => handleBuyNow(currentPricing.stripePriceId, plan.id)}
                >
                  {plan.id === 'team' && (
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
                      Most Popular
                    </Badge>
                  )}
                  <CardHeader>
                    <CardTitle className="text-2xl">{plan.name}</CardTitle>
                    <CardDescription>{plan.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="flex-grow space-y-6">
                    <div className="space-y-2">
                      {plan.id === 'team' ? (
                        <>
                          <div className="text-4xl font-bold">
                            ${(currentPricing.price * teamQuantity).toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            ${currentPricing.price} × {teamQuantity} users {currentPricing.period.replace('per user/', '')}
                          </div>
                          {billingFrequency === 'annual' && savings && (
                            <div className="text-sm text-green-600 font-medium">
                              Save ${(savings.savings * teamQuantity).toLocaleString()}/year ({savings.percentage}% off)
                            </div>
                          )}
                        </>
                      ) : (
                        <>
                          <div className="text-4xl font-bold">
                            ${currentPricing.price}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {currentPricing.period}
                          </div>
                          {billingFrequency === 'annual' && savings && (
                            <div className="text-sm text-green-600 font-medium">
                              Save ${savings.savings}/year ({savings.percentage}% off)
                            </div>
                          )}
                        </>
                      )}
                    </div>

                    {/* Team Quantity Selector */}
                    {plan.id === 'team' && (
                      <div className="space-y-3">
                        <label className="text-sm font-medium">Team Size</label>
                        <div className="flex items-center gap-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setTeamQuantity(Math.max(1, teamQuantity - 1));
                            }}
                            className="w-8 h-8 rounded-full border border-border flex items-center justify-center hover:bg-muted transition-colors"
                            disabled={teamQuantity <= 1}
                          >
                            -
                          </button>
                          <span className="w-12 text-center font-medium">{teamQuantity}</span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setTeamQuantity(teamQuantity + 1);
                            }}
                            className="w-8 h-8 rounded-full border border-border flex items-center justify-center hover:bg-muted transition-colors"
                          >
                            +
                          </button>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Need more than 20 users? <a href="/help" className="text-primary hover:underline" onClick={(e) => e.stopPropagation()}>Contact sales</a>
                        </div>
                      </div>
                    )}

                    <ul className="space-y-3 text-sm">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-3">
                          <CheckIcon className="h-4 w-4 text-primary flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                  <CardFooter>
                    <Button
                      className="w-full transition-all duration-300 group-hover:bg-black group-hover:text-white group-hover:border-black"
                      variant={plan.id === 'team' ? 'default' : 'outline'}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleBuyNow(currentPricing.stripePriceId, plan.id);
                      }}
                      disabled={isLoading === currentPricing.stripePriceId}
                    >
                      {isLoading === currentPricing.stripePriceId ? 'Processing...' : 'Get Started'}
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
              </div>
            </div>
          </div>

          {/* Custom Solution Section - Centered */}
          <div className="text-center mt-16 space-y-4 max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold tracking-tighter">Need a Custom Solution?</h2>
            <p className="max-w-[600px] mx-auto text-muted-foreground">
              Contact us for enterprise licensing, volume discounts, or custom requirements.
            </p>
            <Button variant="outline" asChild>
              <Link href="/help">Contact Sales</Link>
            </Button>
          </div>
        </div>
      </main>

      {/* Footer Section - Added */}
      <footer className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t">
        <p className="text-xs text-muted-foreground">&copy; 2025 QuantBoost. All rights reserved.</p>
        <nav className="sm:ml-auto flex gap-4 sm:gap-6">
          <Link href="#" className="text-xs hover:underline underline-offset-4" prefetch={false}>
            Terms of Service
          </Link>
          <Link href="#" className="text-xs hover:underline underline-offset-4" prefetch={false}>
            Privacy
          </Link>
        </nav>
      </footer>
    </div>
  );
}

// Helper CheckIcon component (assuming it's not globally available)
function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 6 9 17l-5-5" />
    </svg>
  );
}
