---
title: System Patterns for QuantBoost.Licensing SDK
purpose: Describes the architecture, components, data flow, and design patterns of the QuantBoost Licensing SDK.
projects: ["QuantBoost.Licensing"]
source_analysis: "Codebase analysis of QuantBoost.Licensing/LicensingSDK.cs"
status: bootstrapped-incomplete
last_updated: 2025-05-13T16:15:00Z
tags: ["licensing", "sdk", "architecture", "components", "dataflow", "csharp"]
---

## Architecture and System Patterns

### High-Level Architecture
The `QuantBoost.Licensing` project provides a client-side SDK for .NET applications to manage software licensing. It encapsulates:
*   Communication with a remote licensing server.
*   Local caching of license details.
*   License status management and event notifications.

### Main Components and Responsibilities

*   **`IQuantBoostLicensingManager` (Interface):**
    *   Defines the public contract for the licensing manager, ensuring a consistent API for consumers.
    *   Specifies properties for accessing current license details and status, and methods for validation, magic link requests, and cache management.

*   **`QuantBoostLicensingManager` (Class):**
    *   The core implementation of `IQuantBoostLicensingManager`.
    *   **Responsibilities:**
        *   Initializing with a `productId` and `apiBaseUrl`.
        *   Managing an `HttpClient` instance for API communication.
        *   Performing license validation by sending requests to the backend API (`/v1/licenses/validate`).
        *   Requesting magic links (`/v1/auth/magic-link`).
        *   Loading license details from a local cache file (`%LocalAppData%\QuantBoost\[ProductID]\license.cache`) on startup.
        *   Saving validated license details to the local cache.
        *   Generating a unique device ID using SHA256 hash of machine name and product ID.
        *   Maintaining the `CurrentLicense` (of type `LicenseDetails`) and `CurrentStatus` (of type `LicenseStatus`).
        *   Firing the `LicenseStatusChanged` event when license information changes.
        *   Implementing `IDisposable` to clean up the `HttpClient`.

*   **`LicenseDetails` (Class):**
    *   A data class representing the comprehensive details of a license.
    *   Properties include `LicenseKey`, `ProductId`, `LastKnownStatus`, `IsValid`, `LicenseTier`, `TrialDaysLeft`, `ActivationId`, `ExpiryDateUtc`, `Email`, `Features` (a dictionary for feature flags), `UpgradeUrl`, `ManageUrl`, etc.
    *   Includes a `GetFeatureFlag` helper method to easily check boolean feature flags.

*   **`LicenseStatus` (Enum):**
    *   Defines the various possible states of a license, such as `Unknown`, `Active`, `TrialActive`, `GracePeriod`, `Expired`, `InvalidKey`, `NotAssigned`, `ActivationBlocked`, `NetworkError`.

*   **Internal API Helper Classes:**
    *   `ApiResponse<T>`: Generic wrapper for successful API responses.
    *   `ApiLicenseData`: Represents the `data` object within a successful license validation API response.
    *   `ApiErrorResponse`: Wrapper for API error responses.
    *   `ApiErrorDetails`: Represents the `error` object within an API error response.

### Key Data Flow Patterns

*   **License Validation Flow:**
    1.  Client calls `ValidateLicenseAsync(licenseKey)` or `ValidateUsingStoredKeyAsync()`.
    2.  If using stored key and cache is fresh, returns cached status. Otherwise, proceeds.
    3.  `QuantBoostLicensingManager` constructs a JSON request body containing `licenseKey`, `productId`, and `deviceId`.
    4.  An HTTP POST request is made to the `/v1/licenses/validate` API endpoint.
    5.  The API response (JSON) is received.
    6.  If successful (HTTP 200), the response is deserialized into `ApiResponse<ApiLicenseData>`. `LicenseDetails` are updated/created with data from `ApiLicenseData`.
    7.  If an API error occurs (e.g., HTTP 404, 403), the error response is parsed, and `LicenseDetails` are updated to reflect the error status (e.g., `InvalidKey`, `NotAssigned`).
    8.  Network errors result in `LicenseStatus.NetworkError`.
    9.  The updated `LicenseDetails` (or error state) are stored in `_currentLicense`.
    10. The `_currentLicense` is saved to the local file cache (`license.cache`).
    11. The `LicenseStatusChanged` event is fired with the new `LicenseDetails`.

*   **Caching Mechanism:**
    *   On instantiation, `LoadCachedLicense()` attempts to read and deserialize `license.cache`.
    *   After successful validation or when a validation attempt results in a definitive state, `SaveCachedLicense()` serializes `CurrentLicense` to `license.cache`.
    *   A `CACHE_VALIDITY_MINUTES` constant (10 minutes) determines if a cached license is considered "fresh" enough to return its status without re-validating.
    *   `ClearCachedLicense()` deletes the cache file and nullifies the in-memory `CurrentLicense`.

*   **Magic Link Request Flow:**
    1.  Client calls `RequestMagicLinkAsync(email)`.
    2.  Email format is validated.
    3.  A JSON request body with the `email` is sent via HTTP POST to `/v1/auth/magic-link`.
    4.  Returns `true` if the API call is successful (HTTP 2xx), `false` otherwise.

### Design Patterns
*   **Interface Segregation Principle:** `IQuantBoostLicensingManager` defines a clear contract.
*   **Singleton-like access to HttpClient:** One instance per `QuantBoostLicensingManager`.
*   **Strategy Pattern (Implicit):** The handling of different HTTP status codes in `ValidateLicenseAsync` to determine `LicenseStatus` acts like a strategy.
*   **Observer Pattern:** The `LicenseStatusChanged` event allows multiple consumers to react to license state changes.
*   **Caching:** Local file-based caching strategy for license details to reduce API calls and allow offline checks (within freshness limits).
*   **Facade (Implicit):** The `QuantBoostLicensingManager` acts as a facade, simplifying interactions with the complex licensing backend and caching logic for the client application.
