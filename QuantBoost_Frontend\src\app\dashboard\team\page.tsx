"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';


import { useEffect, useState } from 'react';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { apiClient, Subscription, License, isApiSuccess, handleApiError } from '@/lib/api';
import { useToast } from '@/components/ui/toast';

interface SubscriptionWithLicenses extends Subscription {
  licenses: License[];
}

export default function TeamManagementPage() {
  const [loading, setLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithLicenses[]>([]);
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionWithLicenses | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [assignEmail, setAssignEmail] = useState('');
  const [selectedLicenseId, setSelectedLicenseId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const { addToast } = useToast();
  const supabase = useSupabaseClient();

  useEffect(() => {
    fetchTeamData();
  }, []);

  const fetchTeamData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('User not authenticated');
        return;
      }

      // Fetch subscriptions from Azure API
      const subscriptionsResponse = await apiClient.getUserSubscriptions();

      if (!isApiSuccess(subscriptionsResponse)) {
        const errorMsg = handleApiError(subscriptionsResponse, 'Failed to fetch subscriptions');
        setError(errorMsg);
        return;
      }

      const userSubscriptions = subscriptionsResponse.data;
      
      // Fetch licenses for each subscription
      const subscriptionsWithLicenses: SubscriptionWithLicenses[] = [];
      for (const subscription of userSubscriptions) {
        const licensesResponse = await apiClient.getTeamLicenses(subscription.id);
        const licenses = isApiSuccess(licensesResponse) ? licensesResponse.data : [];
        subscriptionsWithLicenses.push({
          ...subscription,
          licenses
        });
      }

      setSubscriptions(subscriptionsWithLicenses);
      
      // Set the first team subscription (quantity > 1) as selected
      const teamSubscription = subscriptionsWithLicenses.find(sub => 
        sub.quantity > 1 && (sub.status === 'active' || sub.status === 'trialing')
      );
      if (teamSubscription) {
        setSelectedSubscription(teamSubscription);
      }

    } catch (error) {
      console.error('Error fetching team data:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleInviteTeamMember = async () => {
    if (!selectedSubscription || !inviteEmail.trim()) return;

    setActionLoading('invite');
    setError(null);
    setSuccess(null);

    try {
      const response = await apiClient.inviteTeamMember(selectedSubscription.id, {
        target_email: inviteEmail.trim()
      });

      if (isApiSuccess(response)) {
        addToast({
          type: 'success',
          title: 'Invitation Sent',
          description: `Invitation sent to ${inviteEmail}`
        });
        setInviteEmail('');
        await fetchTeamData(); // Refresh data
      } else {
        const errorMsg = handleApiError(response, 'Failed to send invitation');
        addToast({
          type: 'error',
          title: 'Invitation Failed',
          description: errorMsg || 'Failed to send invitation'
        });
        setError(errorMsg);
      }
    } catch (error) {
      console.error('Error inviting team member:', error);
      setError('Failed to send invitation');
    } finally {
      setActionLoading(null);
    }
  };

  const handleAssignLicense = async () => {
    if (!selectedSubscription || !selectedLicenseId || !assignEmail.trim()) return;

    setActionLoading('assign');
    setError(null);
    setSuccess(null);

    try {
      const response = await apiClient.assignTeamLicense(selectedSubscription.id, {
        license_id_to_assign: selectedLicenseId,
        target_user_email: assignEmail.trim()
      });

      if (isApiSuccess(response)) {
        addToast({
          type: 'success',
          title: 'License Assigned',
          description: `License assigned to ${assignEmail}`
        });
        setAssignEmail('');
        setSelectedLicenseId('');
        await fetchTeamData(); // Refresh data
      } else {
        const errorMsg = handleApiError(response, 'Failed to assign license');
        addToast({
          type: 'error',
          title: 'Assignment Failed',
          description: errorMsg || 'Failed to assign license'
        });
        setError(errorMsg);
      }
    } catch (error) {
      console.error('Error assigning license:', error);
      setError('Failed to assign license');
    } finally {
      setActionLoading(null);
    }
  };

  const handleUnassignLicense = async (licenseId: string) => {
    if (!selectedSubscription) return;

    setActionLoading(`unassign-${licenseId}`);
    setError(null);
    setSuccess(null);

    try {
      const response = await apiClient.unassignTeamLicense(selectedSubscription.id, licenseId);

      if (isApiSuccess(response)) {
        addToast({
          type: 'success',
          title: 'License Unassigned',
          description: 'License unassigned successfully'
        });
        await fetchTeamData(); // Refresh data
      } else {
        const errorMsg = handleApiError(response, 'Failed to unassign license');
        addToast({
          type: 'error',
          title: 'Unassignment Failed',
          description: errorMsg || 'Failed to unassign license'
        });
        setError(errorMsg);
      }
    } catch (error) {
      console.error('Error unassigning license:', error);
      setError('Failed to unassign license');
    } finally {
      setActionLoading(null);
    }
  };

  const getAvailableLicenses = () => {
    if (!selectedSubscription) return [];
    return selectedSubscription.licenses.filter(license => 
      license.status === 'unassigned' || (!license.user_id && !license.email)
    );
  };

  const getAssignedLicenses = () => {
    if (!selectedSubscription) return [];
    return selectedSubscription.licenses.filter(license => 
      license.status === 'assigned' || license.status === 'active'
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Team Management</h3>
          <p className="text-sm text-muted-foreground">Loading team data...</p>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {[...Array(2)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const teamSubscriptions = subscriptions.filter(sub => sub.quantity > 1);

  if (teamSubscriptions.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Team Management</h3>
          <p className="text-sm text-muted-foreground">
            Invite and manage your team members.
          </p>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>No Team Subscriptions</CardTitle>
            <CardDescription>
              You don't have any team subscriptions that support multiple users.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Team management is only available for subscriptions with multiple seats.
              Please upgrade your subscription to access team features.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Team Management</h3>
        <p className="text-sm text-muted-foreground">
          Invite and manage your team members.
        </p>
      </div>

      {/* Subscription Selection */}
      {teamSubscriptions.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Select Team Subscription</CardTitle>
            <CardDescription>
              Choose which team subscription to manage.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {teamSubscriptions.map((subscription) => (
                <div 
                  key={subscription.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedSubscription?.id === subscription.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedSubscription(subscription)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{subscription.plan_id}</p>
                      <p className="text-sm text-muted-foreground">
                        {subscription.quantity} seats • {subscription.licenses.length} licenses
                      </p>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      subscription.status === 'active' 
                        ? 'bg-green-100 text-green-800'
                        : subscription.status === 'trialing'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {subscription.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error/Success Messages */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-800">{error}</p>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <p className="text-green-800">{success}</p>
          </CardContent>
        </Card>
      )}

      {selectedSubscription && (
        <>
          {/* Invite New Team Member */}
          <Card>
            <CardHeader>
              <CardTitle>Invite Team Member</CardTitle>
              <CardDescription>
                Send an invitation to a new team member. They will receive an email with activation instructions.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <Input
                    type="email"
                    placeholder="Enter email address"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    className="flex-1"
                  />
                  <Button 
                    onClick={handleInviteTeamMember}
                    disabled={!inviteEmail.trim() || actionLoading === 'invite'}
                  >
                    {actionLoading === 'invite' ? 'Sending...' : 'Send Invite'}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Available seats: {selectedSubscription.quantity - getAssignedLicenses().length} of {selectedSubscription.quantity}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Assign Existing License */}
          {getAvailableLicenses().length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Assign Existing License</CardTitle>
                <CardDescription>
                  Assign an existing unassigned license to a user.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    <select
                      value={selectedLicenseId}
                      onChange={(e) => setSelectedLicenseId(e.target.value)}
                      className="px-3 py-2 border rounded-md"
                    >
                      <option value="">Select license</option>
                      {getAvailableLicenses().map((license) => (
                        <option key={license.id} value={license.id}>
                          License {license.id.slice(-8)} ({license.status})
                        </option>
                      ))}
                    </select>
                    <Input
                      type="email"
                      placeholder="Enter email address"
                      value={assignEmail}
                      onChange={(e) => setAssignEmail(e.target.value)}
                    />
                    <Button 
                      onClick={handleAssignLicense}
                      disabled={!selectedLicenseId || !assignEmail.trim() || actionLoading === 'assign'}
                    >
                      {actionLoading === 'assign' ? 'Assigning...' : 'Assign License'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Team Members Table */}
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
              <CardDescription>
                Current team members and their license status.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {getAssignedLicenses().length === 0 ? (
                <p className="text-sm text-muted-foreground">No team members assigned yet.</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Assigned Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getAssignedLicenses().map((license) => (
                      <TableRow key={license.id}>
                        <TableCell>{license.email || 'N/A'}</TableCell>
                        <TableCell>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            license.status === 'active' 
                              ? 'bg-green-100 text-green-800'
                              : license.status === 'assigned'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {license.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          {license.assigned_at 
                            ? new Date(license.assigned_at).toLocaleDateString()
                            : 'N/A'
                          }
                        </TableCell>
                        <TableCell>
                          <Button 
                            variant="destructive" 
                            size="sm" 
                            onClick={() => handleUnassignLicense(license.id)}
                            disabled={actionLoading === `unassign-${license.id}`}
                          >
                            {actionLoading === `unassign-${license.id}` ? 'Removing...' : 'Remove'}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
