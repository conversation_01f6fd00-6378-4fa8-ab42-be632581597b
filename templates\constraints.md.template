# constraints.md - Project Rules and Prohibitions (Multi-Project Workspace)

**Purpose:** Defines absolute rules, mandatory practices, and critical safety constraints that MUST be followed. Rules are tagged `[Global]` or with the specific subproject identifier (e.g., `[Frontend]`, `[Backend]`, `[VSTO]`).

**Instructions for Dane:**
*   When adding or checking constraints, clearly indicate their scope using prefixes: `[Global]`, `[Frontend]`, `[Backend]`, `[VSTO]`, etc.
*   If a constraint applies to multiple, but not all, list them e.g., `[Backend, VSTO]`.

---

## 1. Technical Constraints (MUST NOT / MUST)

<!-- #TechConstraints -->
*   `[Global]` **MUST NOT** commit secrets, API keys, or sensitive credentials directly into the codebase. Use environment variables loaded from `.env` files (which MUST be in `.gitignore`).
*   `[Global]` **MUST NOT** bypass linting or formatting checks enforced by pre-commit hooks or CI (if configured per project). Address violations before proceeding.
*   `[VSTO]` **MUST** target **.NET Framework 4.8.1**. DO NOT use APIs/features from later .NET versions.
*   `[VSTO]` **MUST** use **C# 7.3**. DO NOT use C# 8.0+ features.
*   `[VSTO]` **MUST** use **DocumentFormat.OpenXml SDK v2.18.0**. Strictly PROHIBITED: Using APIs from OpenXML SDK v3.x or `System.IO.Packaging` directly.
*   `[Backend]` **MUST** ensure all new database schema changes are accompanied by a migration file (using Prisma) and follow the documented migration process in `backend/README.md` or `techContext.md`.
*   `[Backend]` **MUST NOT** perform destructive database operations directly without an approved migration script.
*   `[Backend]` **MUST** use `[e.g., bcrypt]` for password hashing.
*   [Add other technical rules, clearly scoping them.]

---

## 2. Process & Workflow Constraints

<!-- #ProcessConstraints -->
*   `[Global]` **MUST** follow the documented Git branching strategy: [Describe or link].
*   `[Global]` **MUST NOT** push directly to the `main` or `develop` branches. All changes must go through Pull Requests (PRs).
*   `[Global]` **MUST** ensure PRs pass all relevant automated checks before requesting review.
*   `[Global]` **MUST** address all reviewer comments before merging a PR.
*   `[Global]` **MUST** update relevant Memory Bank files (`progress.md`, `issues.md`, `plan.md`, etc.) upon completing a task or resolving an issue.
*   [Add other mandatory process steps, scoping them.]

---

## 3. Security Constraints

<!-- #SecurityConstraints -->
*   `[Backend]` **MUST** validate and sanitize all user input received via APIs before processing or storing.
*   `[Frontend]` **MUST** sanitize data received from APIs before rendering to prevent XSS.
*   `[Global]` **MUST NOT** expose sensitive data in logs or error messages across any subproject.
*   `[Backend]` **MUST** implement proper authorization checks for all API endpoints requiring authentication.
*   `[Global]` **MUST** keep dependencies updated across all subprojects to patch known security vulnerabilities.
*   [Add other critical security rules, scoping them.]

---

## 4. Code Style & Quality

<!-- #StyleConstraints -->
*   `[Frontend]` **MUST** adhere to style enforced by ESLint/Prettier (see `frontend/` configs).
*   `[Backend]` **MUST** adhere to style enforced by ESLint/Prettier (see `backend/` configs).
*   `[VSTO]` **MUST** adhere to style enforced by .NET Analyzers (see `.csproj`).
*   `[Global]` **MUST** aim for clear, readable, and maintainable code.
*   `[Global]` **MUST NOT** leave commented-out code blocks in final committed code.
*   `[Global]` **MUST** strive for components/functions with single responsibilities.
*   `[VSTO]` **CRITICAL:** QuantBoostRibbon constructor **MUST BE MINIMAL**. DO NOT access `Globals.ThisAddIn` etc.
*   `[VSTO]` **DO NOT** leak COM objects; ensure they are released.
*   `[Global]` **DO NOT** perform long-running/blocking operations on the UI thread (relevant for Frontend/VSTO).
*   `[Global]` **DO NOT** ignore exceptions; handle or log them appropriately using relevant error handling services.
*   `[Global]` **DO NOT** update UI elements directly from background threads (use appropriate helpers like `AsyncHelper`).

---