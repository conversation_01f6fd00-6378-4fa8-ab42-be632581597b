<?xml version="1.0" encoding="UTF-8"?>
<customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui" onLoad="OnLoad">
  <ribbon>
    <tabs>
      <tab id="tabQuantBoost" label="QuantBoost">

        <group id="groupAnalyze" label="Analyze">
          <button id="btnAnalyze"
                  label="Size Analyzer"
                  getImage="GetAnalyzeIcon"
                  size="large"
                  onAction="OnAnalyzeClick"/>
        </group>

        <group id="groupExcelLink" label="Excel Link">
          <button id="btnInsertExcelContent"
                  label="Insert from Excel"
                  getImage="GetInsertExcelContentIcon"
                  size="large"
                  onAction="OnInsertExcelContentClick"/>
          <button id="btnRefreshSelectedLink"
                  label="Refresh Selected"
                  getImage="GetRefreshSelectedLinkIcon"
                  size="large"
                  onAction="OnRefreshSelectedLinkClick"/>
          <button id="btnRefreshAllLinks"
                  label="Refresh All"
                  getImage="GetRefreshAllLinksIcon"
                  size="large"
                  onAction="OnRefreshAllLinksClick"/>
          <button id="btnLinkManager"
                  label="Link Manager"
                  getImage="GetLinkManagerIcon"
                  size="large"
                  onAction="OnLinkManagerClick"/>
        </group>

        <group id="groupAccount" label="Account">
          <button id="btnManageAccount"
                  getImage="GetManageAccountIcon"
                  size="large"
                  getLabel="GetManageAccountLabel"
                  onAction="OnManageAccountClick"
                  getVisible="GetManageAccountVisible" />
          <button id="btnLogout"
                  label="Logout"
                  getImage="GetLogoutIcon"
                  size="large"
                  onAction="OnLogoutClick"
                  getVisible="GetLogoutButtonVisible" />
        </group>

      </tab>
    </tabs>
  </ribbon>
</customUI>
