const express = require('express');
const router = express.Router();
const { supabase } = require('../supabaseClient'); // Adjust path as necessary
const { sendSuccess, sendError } = require('../utils/responseHelpers'); // Adjust path as necessary
const { isValidEmail } = require('../utils/validationHelpers'); // Adjust path as necessary
// const { logLicenseEvent } = require('../utils/eventLogger'); // Uncomment if needed
const path = require('path'); // Added for magic-link-relay

// AA.2: Add POST /v1/auth/magic-link
router.post('/magic-link', async (req, res) => {
    const { email, options } = req.body;

    // --- Step 1: Basic validation for the email ---
    if (!email) {
        return sendError(res, 'Email is required.', 400);
    }
    if (!isValidEmail(email)) {
        return sendError(res, 'Invalid email format.', 400);
    }

    // --- Step 2: Securely validate the client's redirect URL ---
    const clientRedirectTo = options?.redirectTo;
    
    if (!clientRedirectTo) {
        return sendError(res, 'redirectTo option is required.', 400);
    }

    let clientUrl;
    try {
        clientUrl = new URL(clientRedirectTo);
    } catch (error) {
        return sendError(res, 'Invalid redirectTo URL format.', 400);
    }

    // This is the key security step: only allow redirects to trusted origins.
    const allowedOrigins = [
        'http://localhost' // Allows any port on localhost for local development
    ];

    // The .replace() part removes the port number (e.g., :61083) for the check
    if (!allowedOrigins.includes(clientUrl.origin.replace(/:\d+$/, ''))) {
        return sendError(res, 'Untrusted redirect origin.', 400);
    }

    // --- Step 3: Generate magic link using admin client (for existing users) ---
    try {
        const { data, error } = await supabase.auth.admin.generateLink({
            type: 'magiclink',
            email: email,
            options: {
                // Use the validated URL from the client directly.
                redirectTo: clientRedirectTo,
            },
        });

        if (error) {
            console.error('Error generating magic link:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) ||
                                (error.message && error.message.toLowerCase().includes('json'));

            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to generate magic link.', error.status || 500);
        }

        // Note: admin.generateLink doesn't actually send the email, it just generates the link
        // For production, you'd need to send the email manually or use a different approach
        // For now, return the link for testing purposes
        return sendSuccess(res, { 
            message: `Magic link generated for ${email}. Please check your email.`,
            // Remove loginUrl from response in production for security
            loginUrl: data.properties?.action_link 
        }, 200);

    } catch (err) {
        console.error('Unexpected error in /magic-link route:', err);
        return sendError(res, 'An unexpected error occurred.', 500);
    }
});

// AA.3: Add POST /v1/auth/refresh-token
router.post('/refresh-token', async (req, res) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
        return sendError(res, 'Refresh token is required.', 400);
    }

    try {
        const { data, error } = await supabase.auth.refreshSession({ refresh_token: refreshToken });
        
        // ADD THIS LOGGING
        console.log('Supabase refreshSession raw response:', {
            hasData: !!data,
            hasSession: !!data?.session,
            accessTokenLength: data?.session?.access_token?.length,
            refreshTokenLength: data?.session?.refresh_token?.length,
            refreshTokenValue: data?.session?.refresh_token,
            fullSession: JSON.stringify(data?.session, null, 2)
        });

        if (error) {
            console.error('Error refreshing token:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) || (error.message && error.message.toLowerCase().includes('json'));
            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to refresh token.', error.status || 401);
        }

        if (data.session) {
            // Log before sending response
            console.log('Sending refresh response with:', {
                accessTokenLength: data.session.access_token?.length,
                refreshTokenLength: data.session.refresh_token?.length,
                refreshTokenFirst20: data.session.refresh_token?.substring(0, 20)
            });
            
            return sendSuccess(res, {
                accessToken: data.session.access_token,
                refreshToken: data.session.refresh_token,
                expiresIn: data.session.expires_in,
                user: data.user
            }, 200);
        } else {
            return sendError(res, 'Failed to refresh session, no session data returned.', 500);
        }

    } catch (err) {
        console.error('Unexpected error in /refresh-token route:', err);
        return sendError(res, 'An unexpected error occurred while refreshing token.', 500);
    }
});

// AA.4: Add POST /v1/auth/verify-otp
router.post('/verify-otp', async (req, res) => {
    const { email, token } = req.body;

    if (!email || !token) {
        return sendError(res, 'Email and OTP token are required.', 400);
    }
    if (!isValidEmail(email)) {
        return sendError(res, 'Invalid email format.', 400);
    }

    try {
        const { data, error } = await supabase.auth.verifyOtp({ 
            email: email, 
            token: token, 
            type: 'magiclink'
        });

        if (error) {
            console.error('Error verifying OTP:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) || (error.message && error.message.toLowerCase().includes('json'));
            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to verify OTP.', error.status || 401);
        }

        if (data.session) {
            return sendSuccess(res, {
                accessToken: data.session.access_token,
                refreshToken: data.session.refresh_token,
                expiresIn: data.session.expires_in,
                user: data.user
            }, 200);
        } else {
            return sendError(res, 'OTP verified, but failed to establish a session.', 401);
        }

    } catch (err) {
        console.error('Unexpected error in /verify-otp route:', err);
        return sendError(res, 'An unexpected error occurred while verifying OTP.', 500);
    }
});

// AA.5: Add POST /v1/auth/logout
router.post('/logout', async (req, res) => {
    const authHeader = req.headers.authorization;
    let accessToken = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
        accessToken = authHeader.split(' ')[1];
    }

    if (!accessToken) {
        return sendError(res, 'Access token is required in Authorization header to logout.', 401);
    }

    try {
        const { error } = await supabase.auth.signOut(accessToken); 

        if (error) {
            console.error('Error signing out:', error);
            const isJsonError = (error.originalError instanceof SyntaxError) || (error.message && error.message.toLowerCase().includes('json'));
            if (isJsonError) {
                return sendError(res, 'Temporary connectivity issue. Please try again shortly.', 503);
            }
            return sendError(res, error.message || 'Failed to logout.', error.status || 500);
        }

        return sendSuccess(res, { message: 'Successfully logged out.' }, 200);

    } catch (err) {
        console.error('Unexpected error in /logout route:', err);
        return sendError(res, 'An unexpected error occurred during logout.', 500);
    }
});

// AA.6: Add GET /v1/auth/magic-link-relay (serving HTML)
router.get('/magic-link-relay', (req, res) => {
    const relayPath = path.join(__dirname, '..', 'magic-link-relay.html');
    
    res.sendFile(relayPath, (err) => {
        if (err) {
            console.error("Error sending magic-link-relay.html:", err);
            sendError(res, "Relay page not found or error sending file.", err.status || 500);
        } else {
            console.log("Magic-link-relay.html sent successfully from auth.routes.js.");
        }
    });
});

// AA.7: Add GET /v1/auth/callback - Handle Supabase magic link callbacks and redirect appropriately
router.get('/callback', async (req, res) => {
    const { payment, email, payment_intent, access_token, refresh_token, error, error_description } = req.query;
    
    console.log('Auth callback received:', { payment, email, payment_intent, hasAccessToken: !!access_token, error });
    
    // Handle authentication errors
    if (error) {
        console.error('Authentication error in callback:', error, error_description);
        
        if (payment === 'success') {
            // Payment context - redirect to frontend with error
            const frontendUrl = process.env.FRONTEND_URL || 'https://app-quantboost-frontend-staging.azurewebsites.net';
            return res.redirect(`${frontendUrl}/auth/payment-success?error=${encodeURIComponent(error_description || error)}`);
        } else {
            // VSTO context - serve error page or redirect to VSTO error handler
            return res.status(400).send(`
                <html>
                    <head><title>Authentication Error</title></head>
                    <body>
                        <h2>Authentication Failed</h2>
                        <p>Error: ${error_description || error}</p>
                        <p>Please try again or contact support.</p>
                    </body>
                </html>
            `);
        }
    }
    
    // Check if this is a payment context
    if (payment === 'success' && email && payment_intent) {
        // Payment success - redirect to frontend dashboard with automatic authentication
        const frontendUrl = process.env.FRONTEND_URL || 'https://app-quantboost-frontend-staging.azurewebsites.net';
        
        // If we have tokens and it's auto_login, include them in the redirect for immediate authentication
        if (access_token && refresh_token && req.query.auto_login === 'true') {
            // Create a secure way to pass tokens to the frontend for immediate login
            const redirectUrl = `${frontendUrl}/auth/auto-login?payment=success&email=${encodeURIComponent(email)}&payment_intent=${payment_intent}&access_token=${access_token}&refresh_token=${refresh_token}`;
            console.log('Redirecting payment success with auto-login to frontend:', redirectUrl);
            return res.redirect(redirectUrl);
        } else {
            // Fallback to regular dashboard redirect (existing behavior)
            const redirectUrl = `${frontendUrl}/dashboard?payment=success&email=${encodeURIComponent(email)}&payment_intent=${payment_intent}`;
            console.log('Redirecting payment success to frontend:', redirectUrl);
            return res.redirect(redirectUrl);
        }
    }
    
    // VSTO or other context - handle accordingly
    if (access_token && refresh_token) {
        // For VSTO, we can serve a page that passes tokens back to the add-in
        // or redirect to a VSTO-specific handler
        console.log('Handling VSTO authentication callback');
        
        // Serve a page that can communicate tokens back to VSTO add-in
        return res.send(`
            <html>
                <head>
                    <title>Authentication Successful</title>
                    <meta charset="utf-8">
                </head>
                <body>
                    <h2>Authentication Successful</h2>
                    <p>You have been successfully authenticated. This window will close automatically.</p>
                    <script>
                        // Store tokens for VSTO add-in to retrieve
                        if (window.opener || window.parent) {
                            // Communicate with parent window (VSTO WebBrowser control)
                            const authData = {
                                access_token: "${access_token}",
                                refresh_token: "${refresh_token}",
                                success: true
                            };
                            
                            // Try multiple methods to communicate with VSTO
                            try {
                                // Method 1: postMessage to parent
                                if (window.parent) {
                                    window.parent.postMessage(authData, '*');
                                }
                                
                                // Method 2: Close window after delay
                                setTimeout(() => {
                                    window.close();
                                }, 2000);
                            } catch (e) {
                                console.log('Communication with parent window failed:', e);
                            }
                        }
                        
                        // Also store in localStorage as fallback
                        localStorage.setItem('quantboost_auth', JSON.stringify({
                            access_token: "${access_token}",
                            refresh_token: "${refresh_token}",
                            timestamp: Date.now()
                        }));
                    </script>
                </body>
            </html>
        `);
    }
    
    // Default case - no specific context identified
    console.log('Auth callback with no specific context, serving generic success page');
    return res.send(`
        <html>
            <head><title>Authentication Complete</title></head>
            <body>
                <h2>Authentication Complete</h2>
                <p>Please return to the application.</p>
            </body>
        </html>
    `);
});

module.exports = router;