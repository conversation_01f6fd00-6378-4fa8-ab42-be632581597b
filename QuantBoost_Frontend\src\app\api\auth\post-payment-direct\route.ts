import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(req: NextRequest) {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );
  
  try {
    const { email, paymentIntentId } = await req.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Check if user exists in auth.users
    const { data: { users }, error: listError } = await supabase.auth.admin.listUsers();
    if (listError) {
      console.error('Error listing users:', listError);
      return NextResponse.json({ error: 'Authentication service error' }, { status: 500 });
    }

    let existingUser = users.find(user => user.email === email);
    
    // If user doesn't exist, create them (this handles cases where webhook failed)
    if (!existingUser) {
      console.log(`User ${email} not found, creating new user account for payment success flow`);
      
      try {
        const { data: newUserData, error: createError } = await supabase.auth.admin.createUser({
          email: email,
          email_confirm: true,
          user_metadata: {
            source: 'post_payment_creation',
            payment_intent_id: paymentIntentId,
            created_at: new Date().toISOString()
          }
        });

        if (createError) {
          console.error('Error creating user account:', createError);
          return NextResponse.json({ 
            error: 'Failed to create user account',
            details: createError.message 
          }, { status: 500 });
        }

        existingUser = newUserData.user;
        console.log(`✅ Successfully created user account for ${email}`);
        
      } catch (userCreateError) {
        console.error('Failed to create user:', userCreateError);
        return NextResponse.json({ 
          error: 'User account creation failed',
          details: userCreateError instanceof Error ? userCreateError.message : 'Unknown error'
        }, { status: 500 });
      }
    }

    if (!existingUser) {
      return NextResponse.json({ error: 'User account creation failed' }, { status: 500 });
    }

    // Generate an access token directly for the user
    const { data: tokenData, error: tokenError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: email,
      options: {
        // This will be handled by our direct auth endpoint, not a redirect
        redirectTo: `${process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000'}/dashboard?payment=success`
      }
    });

    if (tokenError || !tokenData.properties) {
      console.error('Error generating auth tokens:', tokenError);
      // Fallback to magic link approach
      const { data: linkData, error: linkError } = await supabase.auth.admin.generateLink({
        type: 'magiclink',
        email: email,
        options: {
          redirectTo: `/v1/auth/callback?payment=success&email=${encodeURIComponent(email)}&payment_intent=${paymentIntentId}&auto_login=true`
        }
      });

      if (linkError) {
        return NextResponse.json({ error: 'Could not generate login link' }, { status: 500 });
      }

      return NextResponse.json({ 
        success: true, 
        loginUrl: linkData.properties?.action_link,
        message: 'Login link generated successfully (fallback)',
        useFallback: true
      });
    }

    // Extract access and refresh tokens from the magic link URL
    const actionLink = tokenData.properties.action_link;
    const urlParams = new URL(actionLink);
    const hashParams = new URLSearchParams(urlParams.hash.substring(1));
    
    const accessToken = hashParams.get('access_token');
    const refreshToken = hashParams.get('refresh_token');
    const expiresIn = hashParams.get('expires_in');

    if (!accessToken || !refreshToken) {
      console.error('Could not extract tokens from magic link');
      // Fallback to regular magic link
      return NextResponse.json({ 
        success: true, 
        loginUrl: actionLink,
        message: 'Login link generated successfully (fallback)',
        useFallback: true
      });
    }

    // Return session tokens for immediate authentication
    console.log('✅ Direct tokens extracted for payment completion');
    
    return NextResponse.json({ 
      success: true, 
      session: {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: expiresIn ? parseInt(expiresIn) : 3600,
        user: existingUser
      },
      message: 'Direct authentication successful',
      directAuth: true,
      debugInfo: {
        email,
        paymentIntentId,
        userId: existingUser.id,
        context: 'payment-success-direct'
      }
    });

  } catch (error) {
    console.error('Error in direct post-payment login:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
