---
title: Technical Context for QuantBoost_API
purpose: "Documents the technologies, libraries, and environment requirements for the QuantBoost_API project."
projects: ["QuantBoost_API"]
source_code_paths_analyzed:
  - "c:\VS projects\QuantBoost\QuantBoost_API\index.js"
  - "c:\VS projects\QuantBoost\QuantBoost_API\package.json"
status: "bootstrapped"
last_updated: "2025-05-13T12:05:00Z"
tags: ["api", "technical", "context"]
---

# Technical Context: QuantBoost_API

## 1. Primary Technologies

*   **Runtime:** Node.js (Version not explicitly specified, but ES6+ syntax like `async/await`, `const/let` is used).
*   **Framework:** Express.js (Version 5.1.0 based on `package.json`, though `index.js` shows `express()`).
*   **Language:** JavaScript (ES6+).
*   **Database:** Supabase (PostgreSQL), interacted with via `@supabase/supabase-js` client library (v2.49.4).

## 2. Key Libraries and Dependencies

*   **`@supabase/supabase-js` (v2.49.4):** Client library for interacting with Supabase.
*   **`express` (v5.1.0):** Web application framework.
*   **`cors` (v2.8.5):** Middleware for enabling Cross-Origin Resource Sharing.
*   **`dotenv` (v16.5.0):** For loading environment variables from a `.env` file.
*   **`jsonwebtoken` (v9.0.2):** For handling JSON Web Tokens, intended for securing admin/internal endpoints.
*   **`crypto` (built-in Node.js module):** Used for hashing (SHA256) device IDs.
*   **`supertest` (v7.1.0) (devDependencies):** For HTTP assertion testing.

## 3. Database Interactions

*   All database interactions are performed using the `supabase` client instance.
*   Targets tables like `licenses`, `license_activations`, `profiles`, and `license_events`.
*   Uses Supabase client methods for queries (e.g., `.from().select().eq().single()`).
*   Error handling for Supabase calls includes checking the `error` object and distinguishing `PGRST116` (not found) errors.
*   Adheres to lowercase table and column names (e.g., `license_key`, `product_id`, `expiry_date`).

## 4. External API Calls

*   Primarily interacts with Supabase as its backend service. No other external third-party APIs are explicitly called in `index.js` apart from Supabase services (Auth, Database).

## 5. Build Tools

*   No explicit build tools (like Webpack, Babel, or Parcel) are mentioned in `package.json` or `index.js` for the backend itself. The `main` script is `index.js` and `type` is `commonjs`.

## 6. Environment Requirements & Setup

*   **`.env` file:** Required for local development to store environment variables.
*   **Critical Environment Variables:**
    *   `SUPABASE_URL`: The URL of the Supabase project.
    *   `SUPABASE_KEY`: The Supabase Service Role Key (must be the service role key for backend operations).
    *   `JWT_SECRET`: Secret key for JWT signing and verification.
    *   `PORT` (Optional, defaults to 3000): Port for the Express server to listen on.
    *   `EMAIL_REDIRECT_URL` (Optional but recommended for magic link auth): URL to redirect users to after magic link authentication.
    *   `NODE_ENV`: Set to `production` in production environments to hide detailed error messages and potentially enable other optimizations.
*   **Startup:** The server is started by running `node index.js`.
*   **Dependencies Installation:** `npm install` based on `package.json`.

## 7. Security Considerations from Code

*   **Environment Variables:** Loads sensitive keys from `.env`. The `TODO LIST.txt` emphasizes using hosting platform environment variables in production.
*   **Rate Limiting:** Implemented for the `/v1/licenses/validate` endpoint.
*   **JWT Authentication:** Middleware (`authenticateJWT`) is present but not globally applied; intended for specific protected routes.
*   **Response Helpers (`sendSuccess`, `sendError`):** Used for consistent API responses, with conditional inclusion of error details based on `NODE_ENV`.
*   **Device ID Hashing:** Device IDs are hashed using SHA256.
*   **Input Validation:** Basic checks for presence and type of `licenseKey`, `productId`, `deviceId`, and email format. `TODO LIST.txt` highlights the need for more robust validation.
