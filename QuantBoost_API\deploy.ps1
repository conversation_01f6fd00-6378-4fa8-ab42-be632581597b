# QuantBoost API - Quick Deployment Script for Windows
# Run this script in PowerShell as Administrator

param(
    [switch]$InstallPrerequisites,
    [switch]$DeployInfrastructure,
    [switch]$BuildAndDeploy,
    [switch]$All
)

Write-Host "🚀 QuantBoost API Deployment Script" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to install Azure CLI
function Install-AzureCLI {
    Write-Host "📦 Installing Azure CLI..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri https://aka.ms/installazurecliwindows -OutFile .\AzureCLI.msi
        Start-Process msiexec.exe -Wait -ArgumentList '/I AzureCLI.msi /quiet'
        Remove-Item .\AzureCLI.msi
        Write-Host "✅ Azure CLI installed successfully!" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to install Azure CLI: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Function to install Terraform
function Install-Terraform {
    Write-Host "📦 Installing Terraform..." -ForegroundColor Yellow
    try {
        # Check if Chocolatey is installed
        if (Get-Command choco -ErrorAction SilentlyContinue) {
            choco install terraform -y
        }
        else {
            Write-Host "⚠️  Chocolatey not found. Please install Terraform manually from https://www.terraform.io/downloads" -ForegroundColor Yellow
            Write-Host "   Or install Chocolatey first: https://chocolatey.org/install" -ForegroundColor Yellow
        }
        Write-Host "✅ Terraform installation initiated!" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to install Terraform: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to verify prerequisites
function Test-Prerequisites {
    Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow
    
    $azExists = Get-Command az -ErrorAction SilentlyContinue
    $terraformExists = Get-Command terraform -ErrorAction SilentlyContinue
    $dockerExists = Get-Command docker -ErrorAction SilentlyContinue
    
    if (-not $azExists) {
        Write-Host "❌ Azure CLI not found" -ForegroundColor Red
        return $false
    }
    else {
        Write-Host "✅ Azure CLI found" -ForegroundColor Green
    }
    
    if (-not $terraformExists) {
        Write-Host "❌ Terraform not found" -ForegroundColor Red
        return $false
    }
    else {
        Write-Host "✅ Terraform found" -ForegroundColor Green
    }
    
    if (-not $dockerExists) {
        Write-Host "⚠️  Docker not found (optional for infrastructure deployment)" -ForegroundColor Yellow
    }
    else {
        Write-Host "✅ Docker found" -ForegroundColor Green
    }
    
    return $true
}

# Function to deploy infrastructure
function Deploy-Infrastructure {
    Write-Host "🏗️  Deploying Azure infrastructure..." -ForegroundColor Yellow
    
    # Check if terraform.tfvars exists
    if (-not (Test-Path "infrastructure\terraform.tfvars")) {
        Write-Host "❌ terraform.tfvars not found!" -ForegroundColor Red
        Write-Host "   Please copy infrastructure\terraform.tfvars.example to infrastructure\terraform.tfvars" -ForegroundColor Yellow
        Write-Host "   and fill in your Supabase credentials." -ForegroundColor Yellow
        return $false
    }
    
    try {
        Set-Location infrastructure
        
        Write-Host "🔧 Initializing Terraform..." -ForegroundColor Yellow
        terraform init
        
        Write-Host "📋 Planning deployment..." -ForegroundColor Yellow
        terraform plan
        
        Write-Host "🚀 Applying infrastructure..." -ForegroundColor Yellow
        terraform apply -auto-approve
        
        Write-Host "✅ Infrastructure deployed successfully!" -ForegroundColor Green
        
        # Get outputs
        Write-Host "📄 Infrastructure Details:" -ForegroundColor Cyan
        $acrName = terraform output -raw container_registry_name
        $appFqdn = terraform output -raw container_app_fqdn
        
        Write-Host "   Container Registry: $acrName" -ForegroundColor White
        Write-Host "   App URL: https://$appFqdn" -ForegroundColor White
        
        Set-Location ..
        return $true
    }
    catch {
        Write-Host "❌ Infrastructure deployment failed: $($_.Exception.Message)" -ForegroundColor Red
        Set-Location ..
        return $false
    }
}

# Function to build and deploy container
function Build-AndDeploy {
    Write-Host "🐳 Building and deploying container..." -ForegroundColor Yellow
    
    try {
        Set-Location infrastructure
        $acrName = terraform output -raw container_registry_name
        Set-Location ..
        
        Write-Host "🔐 Logging into Container Registry..." -ForegroundColor Yellow
        az acr login --name $acrName
        
        Write-Host "🔨 Building Docker image..." -ForegroundColor Yellow
        docker build -t "$acrName.azurecr.io/quantboost-api:latest" .
        
        Write-Host "⬆️  Pushing to registry..." -ForegroundColor Yellow
        docker push "$acrName.azurecr.io/quantboost-api:latest"
        
        Write-Host "🚀 Updating Container App..." -ForegroundColor Yellow
        az containerapp update `
            --name ca-quantboost-api `
            --resource-group rg-quantboost-api-prod `
            --image "$acrName.azurecr.io/quantboost-api:latest"
        
        Write-Host "✅ Container deployed successfully!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Container deployment failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution logic
if (-not (Test-Administrator)) {
    Write-Host "❌ This script requires administrator privileges. Please run as administrator." -ForegroundColor Red
    exit 1
}

if ($InstallPrerequisites -or $All) {
    Install-AzureCLI
    Install-Terraform
    
    Write-Host "🔄 Please restart PowerShell and login to Azure:" -ForegroundColor Yellow
    Write-Host "   az login" -ForegroundColor White
    Write-Host "   az account show" -ForegroundColor White
}

if ($DeployInfrastructure -or $All) {
    if (-not (Test-Prerequisites)) {
        Write-Host "❌ Prerequisites not met. Run with -InstallPrerequisites first." -ForegroundColor Red
        exit 1
    }
    
    if (-not (Deploy-Infrastructure)) {
        exit 1
    }
}

if ($BuildAndDeploy -or $All) {
    if (-not (Test-Prerequisites)) {
        Write-Host "❌ Prerequisites not met." -ForegroundColor Red
        exit 1
    }
    
    Build-AndDeploy
}

if (-not ($InstallPrerequisites -or $DeployInfrastructure -or $BuildAndDeploy -or $All)) {
    Write-Host "📖 Usage Examples:" -ForegroundColor Cyan
    Write-Host "   .\deploy.ps1 -InstallPrerequisites" -ForegroundColor White
    Write-Host "   .\deploy.ps1 -DeployInfrastructure" -ForegroundColor White
    Write-Host "   .\deploy.ps1 -BuildAndDeploy" -ForegroundColor White
    Write-Host "   .\deploy.ps1 -All" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 Manual Steps:" -ForegroundColor Cyan
    Write-Host "   1. Run -InstallPrerequisites" -ForegroundColor White
    Write-Host "   2. Restart PowerShell and run: az login" -ForegroundColor White
    Write-Host "   3. Copy terraform.tfvars.example to terraform.tfvars" -ForegroundColor White
    Write-Host "   4. Fill in your Supabase credentials" -ForegroundColor White
    Write-Host "   5. Run -DeployInfrastructure" -ForegroundColor White
    Write-Host "   6. Run -BuildAndDeploy" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 QuantBoost API deployment process complete!" -ForegroundColor Green