# Subscriber Dashboard & Team Management PRP

## Feature Description

Build a comprehensive subscriber dashboard to replace the current Stripe billing portal redirect with a full custom dashboard that includes subscription management and team member license assignment via email invitations.

## Context & Current State Analysis

### Backend API Architecture (Azure-hosted)
- **Location**: `C:\VS projects\QuantBoost\QuantBoost_API`
- **Hosting**: Azure with Express.js API
- **Authentication**: JWT-based via Supabase Auth
- **Key Existing Endpoints**:
  - [`/v1/me/subscriptions`](C:\VS projects\QuantBoost\QuantBoost_API\routes\userSubscriptions.routes.js): User subscription management
  - [`/v1/me/subscriptions/:subscriptionId/team-licenses`](C:\VS projects\QuantBoost\QuantBoost_API\routes\teamAdmin.routes.js): Team license management
  - [`/v1/me/licenses`](C:\VS projects\QuantBoost\QuantBoost_API\routes\userLicenses.routes.js): Individual license management
  - [`/v1/auth/magic-link`](C:\VS projects\QuantBoost\QuantBoost_API\routes\auth.routes.js): Magic link authentication

### Frontend Architecture (Next.js)
- **Location**: `C:\VS projects\QuantBoost\QuantBoost_Frontend`
- **Framework**: Next.js 15.2.4 with App Router
- **Authentication**: Supabase client-side with magic links
- **Current Issues**:
  - Dashboard redirects to Stripe billing portal
  - Incomplete subscription management UI
  - Missing team invitation email system
  - Database schema misalignment in queries

### Database Schema (Supabase)
- **Individual License Assignment Model**: Each subscription creates multiple license records per seat quantity
- **Key Tables**: [`profiles`](memory-bank_api/supabase_tables.md:80), [`subscriptions`](memory-bank_api/supabase_tables.md:104), [`licenses`](memory-bank_api/supabase_tables.md:41), [`license_activations`](memory-bank_api/supabase_tables.md:19)
- **License Assignment**: Via email field in licenses table, converted to user_id when user logs in

## Technical Requirements

### Architecture Decision: Hybrid API Approach
- **Frontend API Routes**: Handle Stripe billing operations and frontend-specific logic
- **Backend API**: Handle license management, team operations, and authentication
- **Integration**: Frontend calls both Next.js API routes and Azure API as needed

### Data Flow Architecture

```mermaid
graph TB
    subgraph "User Journey"
        A[User Logs In] --> B[Dashboard]
        B --> C{Subscription Type?}
        C -->|Individual| D[Individual Dashboard]
        C -->|Team| E[Team Management]
        E --> F[Invite Team Members]
        E --> G[Assign Licenses]
        F --> H[Send Email Invitation]
        G --> I[License Assignment]
    end
    
    subgraph "Frontend (Next.js)"
        J[Dashboard Components]
        K[API Routes /api/*]
        L[Supabase Client]
    end
    
    subgraph "Backend (Azure API)"
        M[Team Management API]
        N[License Management]
        O[User Authentication]
    end
    
    subgraph "External Services"
        P[Stripe API]
        Q[Email Service]
        R[(Supabase DB)]
    end
    
    B --> J
    J --> K
    J --> M
    K --> P
    H --> Q
    M --> R
    L --> R
```

### Component Architecture

```mermaid
graph TB
    subgraph "Dashboard Layout"
        A[DashboardLayout] --> B[Navigation]
        A --> C[MainContent]
    end
    
    subgraph "Dashboard Pages"
        C --> D[SubscriptionOverview]
        C --> E[TeamManagement] 
        C --> F[BillingHistory]
        C --> G[LicenseManagement]
    end
    
    subgraph "Team Management Components"
        E --> H[TeamMemberList]
        E --> I[InviteTeamMember]
        E --> J[LicenseAssignment]
        E --> K[TeamMemberActions]
    end
    
    subgraph "API Integration"
        L[Frontend API Routes]
        M[Azure Backend API]
        N[Stripe Integration]
    end
    
    D --> L
    E --> M
    F --> N
```

## Implementation Plan

### Phase 1: Dashboard Foundation & Backend Integration

#### Task 1.1: Update Dashboard Entry Point
**Files**: [`src/app/dashboard/page.tsx`](C:\VS projects\QuantBoost\QuantBoost_Frontend\src\app\dashboard\page.tsx)
- Remove Stripe redirect
- Create comprehensive dashboard layout with navigation
- Implement authentication checks and user context

#### Task 1.2: Fix Subscription Management Integration
**Files**: [`src/app/dashboard/subscription/page.tsx`](C:\VS projects\QuantBoost\QuantBoost_Frontend\src\app\dashboard\subscription\page.tsx)
- Fix database queries (remove non-existent table references)
- Integrate with Azure API [`/v1/me/subscriptions`](C:\VS projects\QuantBoost\QuantBoost_API\routes\userSubscriptions.routes.js)
- Add proper error handling and loading states

#### Task 1.3: Create API Integration Layer
**Files**: `src/lib/api.ts` (new)
- Create typed API client for Azure backend
- Implement authentication token management
- Add request/response interceptors for error handling

**API Integration Pattern**:
```typescript
// Frontend calls Azure API for license operations
const response = await fetch(`${process.env.AZURE_API_URL}/v1/me/subscriptions/${subscriptionId}/team-licenses`, {
  headers: {
    'Authorization': `Bearer ${supabaseAccessToken}`,
    'Content-Type': 'application/json'
  }
});
```

### Phase 2: Team License Management

#### Task 2.1: Team License Display
**Files**: `src/components/team/TeamLicenseList.tsx` (new)
- Fetch team licenses via Azure API [`/v1/me/subscriptions/:subscriptionId/team-licenses`](C:\VS projects\QuantBoost\QuantBoost_API\routes\teamAdmin.routes.js:55)
- Display license status, assigned users, and actions
- Implement real-time updates using Supabase subscriptions

#### Task 2.2: License Assignment Interface
**Files**: `src/components/team/LicenseAssignment.tsx` (new)
- Use Azure API [`/v1/me/subscriptions/:subscriptionId/team-licenses/assign`](C:\VS projects\QuantBoost\QuantBoost_API\routes\teamAdmin.routes.js:87)
- Implement license assignment to existing users
- Add validation for email format and license availability

#### Task 2.3: Team Member Invitation System
**Files**: `src/components/team/TeamInvitation.tsx` (new)
- Use Azure API [`/v1/me/subscriptions/:subscriptionId/team-licenses/invite`](C:\VS projects\QuantBoost\QuantBoost_API\routes\teamAdmin.routes.js:176)
- Handle invitation workflow with email validation
- Display invitation status and management

### Phase 3: Email Integration & User Experience

#### Task 3.1: Email Service Integration
**Backend Enhancement**: Extend Azure API invitation endpoint
- Integrate with email service (Resend/SendGrid)
- Create invitation email templates with magic links
- Add email delivery tracking and error handling

**Email Template Structure**:
```html
Subject: You've been invited to QuantBoost Team License

Hi there!

You've been invited to join a QuantBoost team license. Click the link below to activate your license:

[Activate License] -> Magic link via /v1/auth/magic-link with redirectTo=dashboard/license/activate?token=xyz

This invitation will expire in 7 days.
```

#### Task 3.2: License Activation Flow
**Files**: `src/app/dashboard/license/activate/page.tsx` (new)
- Handle license activation from email links
- Integrate with existing magic link authentication
- Update license status from 'assigned' to 'active'

#### Task 3.3: Real-time Dashboard Updates
**Files**: Update existing dashboard components
- Implement Supabase real-time subscriptions for license changes
- Add optimistic UI updates for team management actions
- Create toast notifications for user feedback

### Phase 4: Billing & Subscription Management

#### Task 4.1: Enhanced Billing Integration
**Files**: Update [`src/app/api/billing/create-portal-session/route.ts`](C:\VS projects\QuantBoost\QuantBoost_Frontend\src\app\api\billing\create-portal-session\route.ts)
- Keep existing Stripe portal integration for subscription changes
- Add custom billing overview with invoice history
- Integrate cancellation and upgrade flows

#### Task 4.2: Subscription Status Management
**Files**: `src/components/subscription/SubscriptionStatus.tsx` (new)
- Display subscription details, next billing date, plan information
- Show license utilization (used/available seats)
- Add upgrade/downgrade recommendations

#### Task 4.3: Invoice Management
**Files**: Enhanced [`src/app/api/billing/invoices/route.ts`](C:\VS projects\QuantBoost\QuantBoost_Frontend\src\app\api\billing\invoices\route.ts)
- Improve invoice fetching and display
- Add download and print functionality
- Integrate with accounting/export features

### Phase 5: Security & Polish

#### Task 5.1: Role-Based Access Control
**Implementation**: Leverage existing Azure API authentication
- Implement team admin vs member permissions
- Add subscription ownership validation
- Secure all sensitive operations

#### Task 5.2: Error Handling & Validation
**Files**: `src/lib/validation.ts` (new), error boundaries
- Comprehensive form validation using Zod
- Global error boundary for API failures
- User-friendly error messages and recovery options

#### Task 5.3: Performance Optimization
**Implementation**: 
- Implement React Suspense for loading states
- Add skeleton screens for dashboard components
- Optimize API calls with caching and pagination

## Key Integration Points

### Azure API Integration
- **Authentication**: Use Supabase access tokens for Azure API calls
- **Team Management**: Leverage existing [`teamAdmin.routes.js`](C:\VS projects\QuantBoost\QuantBoost_API\routes\teamAdmin.routes.js) endpoints
- **License Operations**: Use [`userLicenses.routes.js`](C:\VS projects\QuantBoost\QuantBoost_API\routes\userLicenses.routes.js) for individual license management
- **Subscription Data**: Integrate with [`userSubscriptions.routes.js`](C:\VS projects\QuantBoost\QuantBoost_API\routes\userSubscriptions.routes.js)

### Frontend API Routes (Keep These)
- **Stripe Operations**: [`/api/checkout/create-session`](C:\VS projects\QuantBoost\QuantBoost_Frontend\src\app\api\checkout\create-session\route.ts), [`/api/billing/*`](C:\VS projects\QuantBoost\QuantBoost_Frontend\src\app\api\billing\)
- **Webhook Handling**: [`/api/webhooks/stripe`](C:\VS projects\QuantBoost\QuantBoost_Frontend\src\app\api\webhooks\stripe\route.ts) for license provisioning
- **Frontend-Specific**: Any UI-specific operations that don't need backend persistence

### Database Schema Alignment
- **License Assignment Model**: Individual licenses assigned via email field
- **Team Management**: Use existing subscription → licenses relationship
- **Status Tracking**: Leverage license status field ('unassigned', 'assigned', 'active')

## Implementation Tasks in Priority Order

1. **Update Dashboard Entry Point** - Remove Stripe redirect, add navigation
2. **Fix Subscription Page** - Correct database queries, integrate Azure API
3. **Create API Integration Layer** - Typed client for Azure backend
4. **Build Team License Display** - Show current team licenses and status
5. **Implement License Assignment** - Assign licenses to existing users
6. **Add Team Invitation System** - Invite new users via email
7. **Integrate Email Service** - Send invitation emails with magic links
8. **Create License Activation Flow** - Handle email link activation
9. **Add Real-time Updates** - Supabase subscriptions for live updates
10. **Polish UI/UX** - Error handling, loading states, notifications

## Validation Gates

### Frontend Build & Type Safety
```bash
# Build validation
npm run build && npm run lint

# Type checking
npx tsc --noEmit

# Component testing
npm run test
```

### API Integration Testing
```bash
# Test Azure API connectivity
curl -H "Authorization: Bearer $SUPABASE_TOKEN" \
  $AZURE_API_URL/v1/me/subscriptions

# Test team license operations
curl -X POST -H "Authorization: Bearer $SUPABASE_TOKEN" \
  -d '{"target_email":"<EMAIL>"}' \
  $AZURE_API_URL/v1/me/subscriptions/$SUB_ID/team-licenses/invite
```

### Database Validation
```sql
-- Verify license assignment model
SELECT s.id, s.quantity, COUNT(l.id) as license_count 
FROM subscriptions s 
LEFT JOIN licenses l ON s.id = l.subscription_id 
WHERE s.user_id = $USER_ID;

-- Check license status distribution
SELECT status, COUNT(*) 
FROM licenses 
WHERE subscription_id = $SUBSCRIPTION_ID 
GROUP BY status;
```

### End-to-End Testing
1. **Purchase Flow**: User purchases team subscription → licenses created via webhook
2. **Team Management**: Admin logs in → dashboard loads → can see team licenses
3. **Invitation Flow**: Admin invites user → email sent → user activates → license assigned
4. **VSTO Integration**: User validates license in add-in → license activation recorded

## Risk Mitigation

### Technical Risks
1. **API Authentication**: Use existing Supabase JWT validation in Azure API
2. **Email Deliverability**: Implement proper SPF/DKIM, use reputable service
3. **Database Consistency**: Leverage existing webhook system for license provisioning
4. **Performance**: Implement proper caching and pagination for team data

### User Experience Risks
1. **Complex UI**: Implement progressive disclosure for team management features
2. **Email Confusion**: Clear invitation emails with prominent activation buttons
3. **License Conflicts**: Proper validation before assignment operations
4. **Real-time Sync**: Fallback to manual refresh if real-time updates fail

## Success Metrics

- **User Adoption**: Team admins successfully invite and manage team members
- **License Utilization**: Higher percentage of purchased licenses activated
- **User Satisfaction**: Reduced support tickets for team management
- **Technical Performance**: Dashboard loads under 2 seconds, API responses under 500ms
- **Email Deliverability**: >95% delivery rate for invitation emails

## Documentation Requirements

### API Documentation
- Update Azure API documentation with team management endpoints
- Document authentication flow between frontend and backend
- Create integration examples for common team operations

### User Documentation  
- Team admin guide for inviting and managing users
- End-user guide for accepting invitations and activating licenses
- Troubleshooting guide for common team management issues

## Confidence Score: 9/10

This plan leverages the existing solid Azure API foundation with comprehensive team management endpoints, integrates seamlessly with the current Stripe webhook system, and builds upon the established Supabase authentication. The hybrid approach allows keeping the robust backend API while enhancing the frontend experience.

The individual license assignment model is already implemented in the backend API, and the database schema supports the required functionality. The main work involves building the frontend components and integrating email delivery for invitations.