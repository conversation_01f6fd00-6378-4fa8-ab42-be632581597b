# Quant Boost Sheet Size Analyzer – Development Guide (v3.0)  
_Enhanced for Shared Licensing SDK, async UX, and advanced analysis_  
_Last updated: March 30, 2025_

---

## Overview

The Quant Boost Sheet Size Analyzer is a VSTO Excel add-in module that helps users quickly understand, estimate, and optimize the file size contribution of individual sheets, objects, and workbook elements. This version integrates advanced heuristics, async UX, and deep integration with the unified Quant Boost Licensing SDK.

---

## Table of Contents
- [1. Architecture Principles](#1-architecture-principles)  
- [2. Development Plan](#2-development-plan)  
    - [Phase 1: Solution Setup & SDK Integration](#phase-1-solution-setup--sdk-integration)  
    - [Phase 2: Core Analysis Engine](#phase-2-core-analysis-engine)  
    - [Phase 3: User Interface Design](#phase-3-user-interface-design)  
    - [Phase 4: Licensing & Feature Tiers](#phase-4-licensing--feature-tiers)  
    - [Phase 5: Testing, Deployment, Telemetry](#phase-5-testing-deployment-telemetry)  
- [3. Incorporated Enhancements](#3-incorporated-enhancements)  
- [4. Next Steps](#4-next-steps)  

---

## 1. Architecture Principles

- Use **unified async Quant Boost Licensing SDK**, handling caching, grace period & validation.  
- **Async-first UX:** never block Excel UI, offload analysis.  
- Dual-path analysis:  
  - **OpenXML** (if workbook saved) for precision and speed  
  - **COM interop fallback** for unsaved/protected files  
- Expose **clear “Estimate only”** labels if fallback used.  
- Granular breakdown: per sheet, by image, formulas, charts, pivots, names.  
- Modular service/controller/UI layering, future-proofed.  
- Consistent UI style, banners, upgrade prompts, integration with license states.  
- **Graceful error handling**, toast notifications, and telemetry hooks.  

---

## 2. Development Plan

---

### Phase 1: Solution Setup & SDK Integration

- [ ] Create new Excel VSTO add-in project  
- [ ] **Add reference to QuantBoost.Licensing.dll**  
- [ ] Setup folders for `Services`, `Controllers`, `UI`  
- [ ] Build process: WiX installer, signed builds  
- [ ] Initial async **license validation call** with product id `"sheetanalyzer"`  
- [ ] Subscribe to licensing events to toggle feature enablement in ribbon & UI at runtime  

---

### Phase 2: Core Analysis Engine

- [ ] Analysis runs **async on background task**  
- [ ] Entry point:  
  ```csharp
  Task<WorkbookSizeResult> AnalyzeWorkbookAsync(Excel.Workbook wb, IProgress<string> progress)  
  ```  
- [ ] Hybrid approach:  
  - Use **OpenXML SDK** if: the workbook is saved and accessible on disk  
  - Else, **fallback to COM interop heuristics**  
- [ ] For fallback/interop: gather, per sheet, estimates of:  
  - Approximate used range size (cell count x bytes per cell heuristic)  
  - Total shape & image bytes (`Shapes`, `ChartObjects`)  
  - PivotCaches linked size heuristics  
  - Defined Names complexity (# and length)  
  - Count of volatile formulas as a calc load proxy  
- [ ] For OpenXML:  
  - Read worksheet parts sizes  
  - Embedded media & XML part sizes  
  - Shared strings, styles separately  
  - Total workbook zip size for normalization  
- [ ] **Aggregate & normalize** to per-sheet size percentages.  
- [ ] Export intermediate **SheetSizeInfo**:

```csharp
public class SheetSizeInfo {
    public string SheetName {get;set;}
    public long EstimatedBytes {get;set;}
    public int UsedCellCount {get;set;}
    public int ImageCount {get;set;}
    public int ChartCount {get;set;}
    public int PivotCount {get;set;}
    public int NamedRangeCount {get;set;}
    public int VolatileFormulaCount {get;set;}
    public bool AnalysisIsEstimate {get;set;}
}
```

- [ ] Capture analysis timestamp, saved path, fallback mode flag.  
- [ ] Release COM objects promptly, catch & mark protected sheet errors.  

---

### Phase 3: User Interface Design

- [ ] Ribbon button "Analyze Workbook Size"  
- [ ] Main **Task Pane UI** features:  
  - Progress bar + cancel during analysis  
  - Table/grid by sheet showing size estimate, object counts  
  - Chart/graph (optional future) per sheet contribution  
  - Filter by size descending, filter out tiny sheets  
  - Banner with:  
    - Analysis timestamp  
    - Total Workbook Size (vs sum)  
    - Label if **estimate-only fallback was used**  
  - Licensing status badge: Active, Trial, Grace left, Expired  
  - Upgrade CTA button and manage license link  
- [ ] Toast notifications on errors (API unreachable, protected file, etc.)  
- [ ] Consistent UI styling aligning company plugins.

---

### Phase 4: Licensing & Feature Tiers

- [ ] _At load_, async **SDK `ValidateUsingStoredKeyAsync()`**.  
- [ ] Subscribe to **`LicenseStatusChanged` event** from SDK:  
  - **Disable ribbon button/freeze UI** when expired/no license  
  - Show "Trial" days left and encourage upgrade  
  - Grace period countdown shown in banner  
- [ ] Gating features based on license tier/status:  
  - **Detail breakdowns** by object type  
- [ ] Use unified SDK prompts for activation  
- [ ] Report telemetry on license states anonymously (opt-in)  
- [ ] Don’t block Excel startup even if offline/license check fails — rely on SDK cache/grace  

---

### Phase 5: Testing, Deployment, Telemetry

- [ ] Unit/integration tests:  
  - Hybrid analysis choosing logic  
  - All license states: trial, expired, grace, active  
  - Various workbook types: small, large, protected  
- [ ] Validate license gating + upgrade UX flows  
- [ ] Profile async responsiveness during large analysis  
- [ ] MSI signed installer with update manifest/version info  
- [ ] Hooks for future opt-in telemetry:  
  - Analysis run count/duration  
  - License state during use  
  - Non-PII errors  
- [ ] Documentation:  
  - Feature explainer in UI pane  
  - Licensing FAQ  
  - Upgrade prompts & links

---

## 3. Incorporated Enhancements

- **Dual analysis paths (OpenXML + COM fallback)** for highest resilience and accuracy possible.  
- **Async everywhere:** analysis, licensing checks, UI updates.  
- **Granular heuristics** to include counts of images, charts, pivots, volatile formulas.  
- Explicit **UI disclosure** when results are fallback/estimates.  
- **Timestamp + refresh metadata** to warn if data is stale.  
- **Rich licensing integration**:  
  - Enablement by SDK events  
  - Trial/grace countdowns  
  - Upgrade buttons everywhere  
  - Licensing state doesn’t block plugin init  
- COM cleanup & error handling planned robustly.  
- Telemetry hooks for opt-in error/usage analytics.  
- Modular for future features (batch scan, historic trend track).  

---

## 4. Next Steps

1. Scaffold project, license SDK, ribbon (+ events).  
2. Build async licensing init with UI update toggling.  
3. Implement dual-path analysis service async, with heuristics.  
4. Build UI pane for results/refresh + error notice.  
5. Wire in SDK events for feature gating, upgrade prompts.  
6. Add telemetry/logging hooks.  
7. Sign/Fix MSI build, internal user testing.  
8. Finalize help UX, docs, and pre-launch checklist.  

---

**End of Quant Boost Sheet Size Analyzer — Enhanced Development Guide**

---