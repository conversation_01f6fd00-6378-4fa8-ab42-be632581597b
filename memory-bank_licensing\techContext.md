---
title: Technical Context for QuantBoost.Licensing SDK
purpose: Details the technology stack, dependencies, and environment for the QuantBoost Licensing SDK.
projects: ["QuantBoost.Licensing"]
source_analysis: "Codebase analysis of QuantBoost.Licensing/LicensingSDK.cs"
status: bootstrapped-incomplete
last_updated: 2025-05-13T16:15:00Z
tags: ["licensing", "sdk", "technical", "dependencies", "csharp"]
---

## Technology Stack and Dependencies

### Core Technologies
*   **Primary Language:** C# 7.3
*   **Target Framework:** .NET Framework 4.8.1

### Key Libraries and Dependencies
*   **`Newtonsoft.Json`:** Used for JSON serialization and deserialization of API requests and responses, and for caching license details.
*   **`System.Net.Http`:** Utilized for making HTTP requests to the backend licensing API.
*   **`System.Security.Cryptography`:** Employed for generating a device ID using SHA256 hashing.
*   **`System.Net.Mail`:** Used for basic email address format validation (`IsValidEmailFormat` utility method).

### External API Calls
The SDK is designed to communicate with a backend licensing API. Key interactions include:
*   **License Validation:** Sends POST requests to an endpoint like `/v1/licenses/validate` with `licenseKey`, `productId`, and `deviceId`.
*   **Magic Link Request:** Sends POST requests to an endpoint like `/v1/auth/magic-link` with `email`.
*   The base URL for these API calls is configurable during the `QuantBoostLicensingManager` instantiation.

### Build and Environment
*   **Build Tool:** MSBuild (inferred from the `.csproj` project file structure).
*   **Target Environment:** Requires .NET Framework 4.8.1 runtime.
*   **Development Environment:** Visual Studio or any C# compatible IDE.

### Database Interactions
*   No direct database interactions. License details are cached locally as a JSON file in the user's `LocalAppData` directory (`%LocalAppData%\QuantBoost\[ProductID]\license.cache`).
