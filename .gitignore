# Environment files - CRITICAL for API key security
.env
.env.*
.env.local
.env.development
.env.test
.env.production
.env.staging

# API key and secrets files
secrets.txt
strings-to-filter.txt
*.key
*.pem
*.p12
*.pfx
*config.json
*secrets.json

# Azure and cloud configuration files
.azure/
azure.json
appsettings.Development.json
appsettings.Local.json

# Database connection files
database.json
db.json

# Terraform sensitive files
.terraform/
*.tfstate
*.tfstate.backup
.tfvars
terraform.tfvars
terraform.tfvars.json

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build and executable files
*.exe
*.dll
*.so
*.dylib
/build
/dist
/out

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependencies
node_modules/
packages/

# Backup files
*.backup
*.bak
*.tmp

# Git cleanup files
.git-rewrite/

# Project specific
Azure_QuantBoost.md
QuantBoost_API/.env
.github/