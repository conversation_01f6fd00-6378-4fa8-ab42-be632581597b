import { NextRequest, NextResponse } from 'next/server';
import Stripe from 'stripe';
import { ENV, validateStripeConfig, getEnvironmentDebugInfo } from '@/lib/env';

// Valid price IDs for QuantBoost subscriptions - Individual and Team use same price IDs
const VALID_PRICE_IDS = {
  'price_1RC3HTE6FvhUKV1bE9D6zf6e': { // Annual (Individual or Team based on quantity)
    name: 'Annual Plan',
    description: 'Full access, billed annually.',
    productId: 'prod_S6Fn893jGxRhKk'
  },
  'price_1RCQeBE6FvhUKV1bUN94Oihf': { // Quarterly (Individual or Team based on quantity)
    name: 'Quarterly Plan',
    description: 'Full access, billed quarterly.',
    productId: 'prod_S6Fn893jGxRhKk'
  }
};

export async function POST(req: NextRequest) {
  // Debug environment variables using our new env module
  const debugInfo = getEnvironmentDebugInfo();
  console.log('Environment debug info:', debugInfo);

  // Validate Stripe configuration
  const stripeValidation = validateStripeConfig();
  if (!stripeValidation.isValid) {
    console.error('Stripe configuration validation failed:', stripeValidation);
    return NextResponse.json({
      error: 'Payment service configuration error',
      details: 'Missing Stripe configuration',
      debug: {
        missing: stripeValidation.missing,
        environmentInfo: debugInfo
      }
    }, { status: 500 });
  }

  // Create Stripe client using our env module
  const stripe = new Stripe(ENV.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-03-31.basil',
  });

  try {
    const {
      priceId,
      userId,
      email,
      quantity = 1,
      customerInfo
    } = await req.json();

    if (!priceId) {
      return NextResponse.json({ error: 'Missing priceId' }, { status: 400 });
    }

    // Email is required for our Supabase Magic Link authentication flow
    // This ensures proper user account creation and subscription management
    if (!email) {
      return NextResponse.json({
        error: 'Email is required for account creation and authentication'
      }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      console.error(`❌ Invalid email format: ${email}`);
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    // Reject placeholder guest emails
    if (email === '<EMAIL>') {
      console.error(`❌ Rejected guest email in payment intent creation`);
      return NextResponse.json({
        error: 'Please provide a valid email address for account creation.'
      }, { status: 400 });
    }

    const validatedEmail = email;
    console.log(`✅ Creating payment intent with email: ${email}`);

    console.log(`✅ Creating payment intent for email: ${email}`);

    const priceDetails = VALID_PRICE_IDS[priceId as keyof typeof VALID_PRICE_IDS];
    if (!priceDetails) {
      return NextResponse.json({ error: 'Invalid priceId' }, { status: 400 });
    }

    // Prepare customer data for Stripe
    const customerData: {
      email: string;
      name?: string;
      phone?: string;
      address?: {
        line1: string;
        line2?: string;
        city: string;
        state?: string;
        postal_code?: string;
        country: string;
      };
    } = {
      email,
    };

    // Add customer name if provided
    if (customerInfo?.firstName && customerInfo?.lastName) {
      customerData.name = `${customerInfo.firstName} ${customerInfo.lastName}`;
    }

    // Add phone if provided
    if (customerInfo?.phone) {
      customerData.phone = customerInfo.phone;
    }

    // Add address if provided
    if (customerInfo?.addressLine1 && customerInfo?.city && customerInfo?.country) {
      customerData.address = {
        line1: customerInfo.addressLine1,
        line2: customerInfo.addressLine2 || undefined,
        city: customerInfo.city,
        state: customerInfo.state || undefined,
        postal_code: customerInfo.postalCode || undefined,
        country: customerInfo.country,
      };
    }

    // Create or retrieve customer
    let customer;
    try {
      if (validatedEmail) {
        console.log(`🔍 Looking for existing Stripe customer with email: ${validatedEmail}`);

        // Try to find existing customer by email
        const existingCustomers = await stripe.customers.list({
          email: validatedEmail,
          limit: 1,
        });

        if (existingCustomers.data.length > 0) {
          customer = existingCustomers.data[0];
          console.log(`✅ Found existing Stripe customer: ${customer.id}`);

          // Update customer with new information if provided
          if (Object.keys(customerData).length > 1) { // More than just email
            customer = await stripe.customers.update(customer.id, customerData);
            console.log(`✅ Updated existing customer ${customer.id} with new data`);
          }
        } else {
          // Create new customer
          console.log(`🆕 Creating new Stripe customer for email: ${validatedEmail}`);
          customer = await stripe.customers.create(customerData);
          console.log(`✅ Created new Stripe customer: ${customer.id}`);
        }

        // Verify the customer has the correct email
        if (customer.email !== validatedEmail) {
          console.error(`❌ Customer email mismatch! Expected: ${validatedEmail}, Got: ${customer.email}`);
        }
      } else {
        // Initial setup: create customer without email for Stripe Elements initialization
        // Email will be added when user fills out the form (following Stripe best practices)
        console.log(`🔄 Creating customer for initial Stripe Elements setup (email pending)`);
        customer = await stripe.customers.create({
          metadata: {
            isInitialSetup: 'true',
            createdAt: new Date().toISOString(),
            pendingEmailCapture: 'true', // Will be updated with email from form
          }
        });
        console.log(`✅ Created customer for initial setup: ${customer.id}`);
      }
    } catch (customerError) {
      console.error('❌ Customer creation/update failed:', customerError);
      customer = null;
    }

    // Ensure we have a customer for subscription
    if (!customer) {
      return NextResponse.json({
        error: 'Customer creation failed - required for subscriptions'
      }, { status: 500 });
    }

    // Create subscription with incomplete payment behavior
    console.log(`🔄 Creating subscription for customer ${customer.id} with email ${validatedEmail || 'guest'}`);
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      items: [{
        price: priceId,
        quantity: quantity,
      }],
      payment_behavior: 'default_incomplete',
      payment_settings: {
        save_default_payment_method: 'on_subscription',
        payment_method_types: ['card', 'link'] // Enable Link for subscriptions
      },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        priceId,
        userId: userId || '',
        email: validatedEmail || 'guest',
        quantity: quantity.toString(),
        productName: priceDetails.name,
        ...(customerInfo?.firstName && { firstName: customerInfo.firstName }),
        ...(customerInfo?.lastName && { lastName: customerInfo.lastName }),
        ...(customerInfo?.phone && { phone: customerInfo.phone }),
        ...(customerInfo?.country && { country: customerInfo.country }),
      },
    });

    console.log(`✅ Created subscription ${subscription.id} for customer ${customer.id}`);
    console.log(`📧 Subscription metadata email: ${subscription.metadata.email}`);

    // Handle the invoice which can be a string ID or expanded object
    let invoice: Stripe.Invoice;
    if (typeof subscription.latest_invoice === 'string') {
      // If it's just an ID, we need to fetch the full invoice
      invoice = await stripe.invoices.retrieve(subscription.latest_invoice, {
        expand: ['payment_intent']
      });
    } else {
      invoice = subscription.latest_invoice as Stripe.Invoice;
    }

    // Safely access payment_intent with proper error handling
    let paymentIntent: Stripe.PaymentIntent;

    if (!invoice) {
      console.error('No invoice found for subscription:', subscription.id);
      return NextResponse.json({
        error: 'No invoice found for subscription',
        details: 'Subscription was created but no invoice was generated'
      }, { status: 500 });
    }

    // Check if payment_intent exists on the invoice
    const paymentIntentId = (invoice as any).payment_intent;

    if (!paymentIntentId) {
      console.log('No payment intent found on invoice, creating one manually');
      console.log('Invoice details:', {
        id: invoice.id,
        status: invoice.status,
        amount_due: invoice.amount_due,
        attempted: (invoice as any).attempted
      });

      // For subscriptions with payment_behavior: 'default_incomplete',
      // we need to create a payment intent manually
      paymentIntent = await stripe.paymentIntents.create({
        amount: invoice.amount_due,
        currency: invoice.currency,
        customer: customer.id,
        metadata: {
          subscription_id: subscription.id,
          invoice_id: invoice.id || '',
          priceId,
          email: validatedEmail || 'guest',
          productName: priceDetails.name || '',
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      console.log('Created payment intent:', paymentIntent.id);
    } else {
      // Get the payment intent (it could be a string ID or expanded object)
      if (typeof paymentIntentId === 'string') {
        paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      } else {
        paymentIntent = paymentIntentId as Stripe.PaymentIntent;
      }
    }

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      subscriptionId: subscription.id,
      paymentIntentId: paymentIntent.id,
      customerId: customer.id,
      productName: priceDetails.name,
      description: priceDetails.description,
    });
  } catch (error) {
    console.error('Error creating subscription:', error);

    // Log environment variable status for debugging
    console.error('Environment check:', {
      hasStripeSecret: !!process.env.STRIPE_SECRET_KEY,
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.SUPABASE_SERVICE_KEY
    });

    return NextResponse.json({
      error: 'Failed to create subscription',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}