# GitHub Actions workflow for deploying QuantBoost infrastructure
# Place this file in .github/workflows/ directory

name: Deploy QuantBoost Infrastructure

on:
  push:
    branches: [main]
    paths: 
      - 'QuantBoost_API/infrastructure/**'
      - 'QuantBoost_Frontend/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - prod

env:
  ARM_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
  ARM_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
  ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
  ARM_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}

jobs:
  terraform:
    name: Deploy Infrastructure
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    defaults:
      run:
        working-directory: QuantBoost_API/infrastructure
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.9.8
        terraform_wrapper: false
    
    - name: Terraform Init
      run: terraform init
    
    - name: Terraform Plan
      run: |
        terraform plan \
          -var="supabase_url=${{ secrets.SUPABASE_URL }}" \
          -var="supabase_anon_key=${{ secrets.SUPABASE_ANON_KEY }}" \
          -var="supabase_service_role_key=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" \
          -var="stripe_publishable_key=${{ secrets.STRIPE_PUBLISHABLE_KEY }}" \
          -var="stripe_secret_key=${{ secrets.STRIPE_SECRET_KEY }}" \
          -var="stripe_webhook_signing_secret=${{ secrets.STRIPE_WEBHOOK_SECRET }}" \
          -var="jwt_secret=${{ secrets.JWT_SECRET }}" \
          -var="environment=${{ github.event.inputs.environment || 'staging' }}" \
          -out=tfplan
    
    - name: Terraform Apply
      if: github.ref == 'refs/heads/main'
      run: terraform apply -auto-approve tfplan
    
    - name: Get Terraform Outputs
      id: terraform-outputs
      run: |
        echo "static_web_app_name=$(terraform output -raw static_web_app_default_hostname)" >> $GITHUB_OUTPUT
        echo "api_key=$(terraform output -raw static_web_app_api_key)" >> $GITHUB_OUTPUT
        echo "container_app_fqdn=$(terraform output -raw container_app_fqdn)" >> $GITHUB_OUTPUT

  deploy-frontend:
    name: Deploy Frontend
    needs: terraform
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: QuantBoost_Frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: QuantBoost_Frontend
      run: npm ci
    
    - name: Build Frontend
      working-directory: QuantBoost_Frontend
      run: npm run build
    
    - name: Deploy to Azure Static Web Apps
      uses: Azure/static-web-apps-deploy@v1
      with:
        azure_static_web_apps_api_token: ${{ needs.terraform.outputs.api_key }}
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        action: "upload"
        app_location: "QuantBoost_Frontend"
        api_location: "QuantBoost_Frontend/src/app/api"
        output_location: "out"
        skip_app_build: true # We already built it
