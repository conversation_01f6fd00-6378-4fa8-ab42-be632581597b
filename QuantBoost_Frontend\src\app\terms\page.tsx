import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@/components/ui';
import Link from 'next/link';
import Image from 'next/image';

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="px-4 lg:px-6 h-14 flex items-center border-b">
        <Link href="/" className="flex items-center justify-center">
          <Image src="/QuantBoost_LogoOnly_v0.png" alt="QuantBoost Logo" width={32} height={32} />
          <span className="ml-2 text-lg font-semibold">QuantBoost</span>
        </Link>
        <nav className="ml-auto">
          <Button variant="ghost" asChild>
            <Link href="/pricing">← Back to Pricing</Link>
          </Button>
        </nav>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl">Terms of Service</CardTitle>
            <p className="text-muted-foreground">Last updated: {new Date().toLocaleDateString()}</p>
          </CardHeader>
          <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
            <h2>1. Acceptance of Terms</h2>
            <p>
              By accessing and using QuantBoost services, you accept and agree to be bound by the terms 
              and provision of this agreement.
            </p>

            <h2>2. Service Description</h2>
            <p>
              QuantBoost provides Excel and PowerPoint add-in tools designed to enhance productivity 
              and analysis capabilities for business professionals.
            </p>

            <h2>3. Subscription and Payment</h2>
            <p>
              Our services are provided on a subscription basis. Payment is required in advance for 
              the selected subscription period. All fees are non-refundable except as required by law 
              or as specifically permitted in our refund policy.
            </p>

            <h2>4. User Responsibilities</h2>
            <p>
              You are responsible for maintaining the confidentiality of your account credentials and 
              for all activities that occur under your account.
            </p>

            <h2>5. Intellectual Property</h2>
            <p>
              All content, features, and functionality of QuantBoost services are owned by QuantBoost 
              and are protected by international copyright, trademark, and other intellectual property laws.
            </p>

            <h2>6. Privacy</h2>
            <p>
              Your privacy is important to us. Please review our Privacy Policy, which also governs 
              your use of the services.
            </p>

            <h2>7. Limitation of Liability</h2>
            <p>
              QuantBoost shall not be liable for any indirect, incidental, special, consequential, 
              or punitive damages resulting from your use of the services.
            </p>

            <h2>8. Termination</h2>
            <p>
              We may terminate or suspend your account and access to the services at our sole discretion, 
              without prior notice, for conduct that we believe violates these Terms of Service.
            </p>

            <h2>9. Changes to Terms</h2>
            <p>
              We reserve the right to modify these terms at any time. We will notify users of any 
              material changes via email or through the service.
            </p>

            <h2>10. Contact Information</h2>
            <p>
              If you have any questions about these Terms of Service, please contact us at 
              <EMAIL>.
            </p>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
