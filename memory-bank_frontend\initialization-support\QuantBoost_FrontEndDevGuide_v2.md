# Development Plan: QuantBoost Licensing & Activation Flow

**Goal:** Implement a system where a user can purchase a multi-seat subscription via Stripe (guest checkout allowed), receive licenses, log in via magic link, assign licenses via a web dashboard, and end-users can validate those licenses in a PowerPoint Add-in via an SDK.

**Assumptions:**

*   Stripe account, API keys, and configured products/prices exist.
*   Supabase project is set up.
*   Basic frontend web application structure exists for purchase initiation and magic link callback.
*   PowerPoint Add-in project structure exists.
*   Database schemas match the finalized versions (v5 SQL script).

---

## Phase 1: Backend Foundation & Stripe Integration

### Task 1.1: Database Schema Finalization & Migrations

*   **Importance:** Critical (Operational/Maintainability)
*   **Subtask 1.1.1:** Verify `licenses` table schema (ensure `seat_limit` removed, `expiry_date` present, `subscription_id` present & nullable, `email` nullable, `user_id` nullable).
*   **Subtask 1.1.2:** Verify `subscriptions` table schema (ensure `user_id` NOT NULL & FK to profiles, `plan_id` removed, `quantity` INTEGER added & NOT NULL DEFAULT 1, necessary Stripe IDs present).
*   **Subtask 1.1.3:** Verify `profiles` table schema (ensure `email` NOT NULL & UNIQUE constraint, `id` PK, `stripe_customer_id` present).
*   **Subtask 1.1.4:** Verify `license_activations` table schema (ensure `license_id` NOT NULL & FK to licenses).
*   **Subtask 1.1.5:** **Set up database migrations** (using Supabase CLI migrations or `node-pg-migrate`). Create initial migration(s) reflecting the finalized schema. Apply migrations to your local/dev database.
*   **`index.js` Impact:** No direct code change, but subsequent logic relies on this schema.

### Task 1.2: Backend Stripe Configuration & Helpers

*   **Importance:** High (Core Functionality)
*   **Subtask 1.2.1:** Install Stripe Node.js library: `npm install stripe` (or `yarn add stripe`).
*   **Subtask 1.2.2:** Add Stripe Secret Key and Webhook Signing Secret to `.env` and configure secure environment variable handling for production.
*   **Subtask 1.2.3:** Initialize Stripe client in `index.js`.
    ```javascript
    // index.js (near supabase client init)
    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    ```
*   **`index.js` Impact:** Adds Stripe initialization.

### Task 1.3: Implement Stripe Checkout Session Endpoint

*   **Importance:** High (Core Functionality)
*   **Subtask 1.3.1:** Create a new **public** route in `index.js`: `POST /v1/payments/create-checkout-session`.
*   **Subtask 1.3.2:** Add input validation (require `priceId`, `quantity`; validate types).
*   **Subtask 1.3.3:** Implement logic to call `stripe.checkout.sessions.create`.
    *   Set `mode: 'subscription'`.
    *   Set `line_items: [{ price: priceId, quantity: quantity }]`.
    *   Set `customer_creation: 'always'`.
    *   Include necessary `metadata` (e.g., `productId`, `planIdentifier`, `quantity`).
    *   Set `success_url` and `cancel_url` (potentially from env vars).
*   **Subtask 1.3.4:** Return the `sessionId` to the frontend.
*   **`index.js` Impact:** Adds a new route and associated logic.

### Task 1.4: Implement Stripe Webhook Handler Endpoint

*   **Importance:** Critical (Core Functionality / Data Sync)
*   **Subtask 1.4.1:** Create a new route: `POST /v1/webhooks/stripe`. Apply `express.raw({type: 'application/json'})` middleware *before* `express.json()` ONLY for this route.
    ```javascript
    // index.js - BEFORE app.use(express.json());
    app.post('/v1/webhooks/stripe', express.raw({type: 'application/json'}), handleStripeWebhook);
    // index.js - AFTER raw middleware for webhook
    app.use(express.json()); // For other routes
    // ...
    async function handleStripeWebhook(req, res) { /* ... logic ... */ }
    ```
*   **Subtask 1.4.2:** **Implement Stripe signature verification** using `stripe.webhooks.constructEvent(req.body, req.headers['stripe-signature'], stripeWebhookSecret)`. Return 400 if verification fails.
*   **Subtask 1.4.3:** Create a `switch` statement based on `event.type`.
*   **Subtask 1.4.4:** Implement handler for `checkout.session.completed`:
    *   Extract customer email, customer ID, metadata (quantity, product).
    *   **Find or create user in `profiles` table**, store/update `stripe_customer_id`.
    *   **Create `subscriptions` record**, store `stripe_subscription_id`, `user_id`, `quantity`, `status` (e.g., 'pending').
    *   **(Optional but Recommended): Automatically trigger magic link** by calling `supabase.auth.signInWithOtp` internally for the customer's email.
*   **Subtask 1.4.5:** Implement handler for `invoice.paid`:
    *   Find associated `subscriptions` record.
    *   Update `subscriptions` status to `'active'`, update period dates.
    *   **Create the correct number of `licenses` rows** based on `subscriptions.quantity`. Link via `subscription_id`, set `product_id`, `status='inactive'`, `expiry_date=sub.current_period_end`, `license_tier`.
*   **Subtask 1.4.6:** Implement handlers for `invoice.payment_failed`, `customer.subscription.updated`, `customer.subscription.deleted` to update `subscriptions` and linked `licenses` statuses/expiry dates appropriately.
*   **Subtask 1.4.7:** Ensure webhook **always returns a `200 OK` response to Stripe quickly** (log internal errors). Use background jobs if processing is long.
*   **`index.js` Impact:** Adds new webhook route, raw body parser, significant event handling logic, database interactions.

---

## Phase 2: Authentication & Core Validation Logic

### Task 2.1: Review/Refine Magic Link Endpoint

*   **Importance:** High (Core Functionality)
*   **Subtask 2.1.1:** Verify `/v1/auth/magic-link` endpoint in `index.js` correctly uses `supabase.auth.signInWithOtp`.
*   **Subtask 2.1.2:** Ensure `EMAIL_REDIRECT_URL` environment variable is configured (in `.env` & production) and used in `signInWithOtp` options, matching Supabase Auth settings.
*   **`index.js` Impact:** Configuration check, minor code verification.

### Task 2.2: Review/Refine Activation Check Logic (`checkOrRecordActivation`)

*   **Importance:** High (Core Functionality)
*   **Subtask 2.2.1:** Verify the `checkOrRecordActivation` function in `index.js` implements the latest logic:
    *   Checks `license.email` exists and is valid.
    *   Checks `license_activations` for existing active record based ONLY on `license_id`.
    *   If exists, update timestamp/machine_id, return `allowed: true`.
    *   If not exists, INSERT new activation, return `allowed: true` (or false on insert error).
*   **`index.js` Impact:** Confirm latest logic is present and correct.

### Task 2.3: Review/Refine License Validation Endpoint (`/v1/licenses/validate`)

*   **Importance:** High (Core Functionality)
*   **Subtask 2.3.1:** Verify handler calls the correct `checkOrRecordActivation`.
*   **Subtask 2.3.2:** Verify status determination logic uses `license.status` and `license.expiry_date` / trial dates correctly, considering the data updated by webhooks.
*   **Subtask 2.3.3:** Verify the structure and content of the success response data matches C# SDK expectations (`status`, `isValid`, `features`, `email`, etc.).
*   **`index.js` Impact:** Ensure consistency with SDK and latest logic; relies on webhook data population.

---

## Phase 3: Frontend Web Application (Core User Flows)

### Task 3.1: Implement Basic Purchase UI

*   **Importance:** Medium (Needed for Testing E2E Purchase)
*   **Subtask 3.1.1:** Create UI element(s) to select plan (Price ID) and quantity.
*   **Subtask 3.1.2:** Implement frontend JavaScript to call backend `/v1/payments/create-checkout-session`.
*   **Subtask 3.1.3:** Implement `stripe.js` logic to redirect to Stripe Checkout using the received Session ID.

### Task 3.2: Implement Payment Status Pages

*   **Importance:** Medium (User Experience)
*   **Subtask 3.2.1:** Create `/payment-success` page. Display success message & instruct user to check email for magic link.
*   **Subtask 3.2.2:** Create `/payment-cancel` page. Display cancellation message.

### Task 3.3: Implement Magic Link Callback Handling

*   **Importance:** High (Core Functionality)
*   **Subtask 3.3.1:** Configure `supabase-js` client in frontend (URL, Anon Key).
*   **Subtask 3.3.2:** Implement `supabase.auth.onAuthStateChange` listener early in app lifecycle.
*   **Subtask 3.3.3:** Create the callback page (e.g., `/auth/callback`) specified in Supabase/env vars. The `onAuthStateChange` listener should handle session creation and redirecting the user (e.g., to a dashboard).

### Task 3.4: Implement License Assignment Dashboard (Basic)

*   **Importance:** High (Core B2B Functionality)
*   **Subtask 3.4.1:** Create a basic dashboard page accessible after login.
*   **Subtask 3.4.2:** Implement API endpoint (e.g., `GET /v1/licenses/my-subscriptions`) requiring authentication (`authenticateJWT` or session check) to fetch subscriptions and associated licenses for the logged-in user (`req.user.id`).
*   **Subtask 3.4.3:** Display fetched licenses on the dashboard, showing status and assigned email (if any).
*   **Subtask 3.4.4:** Implement UI for assigning an email to an inactive license.
*   **Subtask 3.4.5:** Implement API endpoint (e.g., `POST /v1/licenses/:licenseId/assign`) requiring authentication & authorization (check if user owns the subscription linked to the license). This endpoint updates the `licenses` row with the provided `email`, `user_id` (optional), and sets `status` to `'active'`.

---

## Phase 4: Client SDK Integration (PowerPoint Add-in)

### Task 4.1: Integrate Licensing SDK

*   **Importance:** High (Primary Goal)
*   **Subtask 4.1.1:** Add the updated `LicensingSDK.cs` to the Add-in project.
*   **Subtask 4.1.2:** Instantiate `QuantBoostLicensingManager` on Add-in load/startup.
*   **Subtask 4.1.3:** Call `ValidateUsingStoredKeyAsync()` on startup.
*   **Subtask 4.1.4:** Implement UI logic based on `CurrentStatus` and `IsValid`:
    *   Enable/disable ribbon buttons/features.
    *   Display license status/tier/email.
    *   Show "Activate" button/prompt if status is Unknown/InvalidKey/NotAssigned/Expired.
*   **Subtask 4.1.5:** Implement "Activate" button logic to call `RequestMagicLinkAsync()`, providing instructions to the user.
*   **Subtask 4.1.6:** Implement a "Refresh Status" button/mechanism to call `RefreshLicenseStatusAsync()`.
*   **Subtask 4.1.7:** Use `IsFeatureEnabled()` calls to gate specific Add-in actions.

---

## Phase 5: Hardening & Deployment Preparation

### Task 5.1: Secure Sensitive Operations

*   **Importance:** Critical (Security)
*   **Subtask 5.1.1:** Review **ALL** backend API endpoints. Apply `authenticateJWT` (or Supabase session checks) to routes requiring login (e.g., `/my-subscriptions`, `/assign`).
*   **Subtask 5.1.2:** Ensure webhook handler rigorously verifies Stripe signature before processing.
*   **Subtask 5.1.3:** Implement authorization checks (e.g., does the logged-in user own the subscription they are trying to manage licenses for?).

### Task 5.2: Set `NODE_ENV=production`

*   **Importance:** Critical (Security/Performance)
*   **Subtask 5.2.1:** Ensure deployment environment sets `NODE_ENV=production`.

### Task 5.3: Enhance Input Validation

*   **Importance:** High (Security/Robustness)
*   **Subtask 5.3.1:** Add schema validation (`Joi`, `zod`, etc.) to all API endpoints in `index.js` (validate body, params, query strings).

### Task 5.4: Implement Structured Logging

*   **Importance:** High (Operational/Debugging)
*   **Subtask 5.4.1:** Replace `console.log/warn/error` in `index.js` with Winston/Pino.
*   **Subtask 5.4.2:** Configure logger for structured output (JSON) and appropriate log levels.
*   **Subtask 5.4.3:** Set up log shipping to a monitoring service for production.

### Task 5.5: Testing (Unit, Webhook, E2E)

*   **Importance:** High (Reliability)
*   **Subtask 5.5.1:** Write unit tests for complex backend functions (status logic, webhook parsing).
*   **Subtask 5.5.2:** Use Stripe CLI (`stripe listen --forward-to localhost:3000/v1/webhooks/stripe`) to test webhook handlers locally.
*   **Subtask 5.5.3:** Perform end-to-end testing simulating the full user journey (purchase -> magic link -> login -> assign license -> activate in Add-in).

### Task 5.6: Set up Production Hosting & Deployment

*   **Importance:** Critical (Deployment)
*   **Subtask 5.6.1:** Set up hosting environment (Vercel, Render, AWS etc.).
*   **Subtask 5.6.2:** **Configure ALL required environment variables securely** via hosting platform secrets management (`SUPABASE_URL`, `SUPABASE_KEY`, `JWT_SECRET`, `EMAIL_REDIRECT_URL`, `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`).
*   **Subtask 5.6.3:** Set up CI/CD pipeline for automated testing and deployment.

---

## Phase 6: Polish & Monitor

### Task 6.1: Review/Refine Feature Set / Tier Logic

*   **Importance:** Medium (Correct Functionality)
*   **Subtask 6.1.1:** Ensure API returns correct `features` based on `license_tier` (requires tier to be set correctly on license, possibly derived from `subscriptions.plan_id` during webhook processing).
*   **Subtask 6.1.2:** Ensure SDK's `IsFeatureEnabled` correctly interprets the `features` data.

### Task 6.2: API Documentation (Swagger/OpenAPI)

*   **Importance:** Medium (Maintainability/Collaboration)
*   **Subtask 6.2.1:** Add OpenAPI spec generation to `index.js` (e.g., using `swagger-jsdoc`, `swagger-ui-express`).

### Task 6.3: Monitoring & Alerting

*   **Importance:** High (Operational)
*   **Subtask 6.3.1:** Set up uptime monitoring, error rate tracking, and performance monitoring for the deployed API.
*   **Subtask 6.3.2:** Configure alerts for critical errors (e.g., webhook failures, high 5xx error rate).

---