# Copilot Instructions: QuantBoost Frontend (Next.js App)

**Project:** QuantBoost Frontend & License Management Portal
**Goal:** Guide AI code generation/suggestions to adhere strictly to the specified stack, best practices for Next.js (App Router), TypeScript, Supabase, Stripe, and maintainability for a production-ready web application.

---

## 1. Core Technical Stack & Constraints (MANDATORY)

*   **Framework:** **MUST** use **Next.js v13+ with the App Router**. Utilize Server Components, Client Components (`'use client'`), Route Handlers (for API endpoints), and Server Actions where appropriate. Avoid the Pages Router unless explicitly instructed for specific legacy reasons.
*   **Language:** **MUST** use **TypeScript**. Enforce strong typing wherever possible. Use interfaces or types for props, API responses, state, etc. Avoid `any` unless absolutely necessary and justified.
*   **UI Library:** **MUST** use **Shadcn/ui** components. Leverage its composability based on Radix UI and Tailwind CSS.
*   **Styling:** **MUST** use **Tailwind CSS** for styling. Follow utility-first principles. Avoid custom CSS files unless absolutely necessary for complex, non-utility styles.
*   **Hosting:** Code should be compatible with **Vercel** deployment (Serverless functions, Edge functions where appropriate).

---

## 2. Key Architectural Components & Patterns

*   **Routing:** Use the **App Router** file-based conventions (`app/page.tsx`, `app/dashboard/layout.tsx`, `app/api/route.ts`, etc.). Utilize Layouts, Pages, Loading UI, and Error UI components.
*   **Component Types:** Clearly distinguish between **Server Components** (default, for data fetching, accessing backend resources directly) and **Client Components** (`'use 'client'`, for interactivity, hooks like `useState`, `useEffect`, event handlers). Minimize client-side JavaScript footprint where possible.
*   **State Management:**
    *   **Global State (Auth, User Profile):** Use **Zustand** or **React Context API** for simple global state. Create appropriate providers and hooks (e.g., `useAuth`).
    *   **Server State (Data Fetching):** **MUST** use **SWR** or **React Query** for fetching data from Supabase (or internal API routes) within Client Components, handling caching, revalidation, and mutations. For Server Components, fetch data directly (e.g., using Supabase JS client).
*   **Forms:** **MUST** use **React Hook Form** for all forms (Login/Signup [using email], Profile Update, etc.). Integrate with Shadcn/ui components. Implement client-side validation. Server-side validation via Server Actions or API Routes where applicable.
*   **Authentication:**
    *   **MUST** use **Supabase Auth** with **Passwordless Magic Link via Email** as the primary sign-in/sign-up method. Implement the `signInWithOtp` flow (using the `email` option).
    *   Provide a clear UI for users to enter their email address to receive the magic link.
    *   Handle the callback URL/route where Supabase redirects the user after clicking the magic link to complete the sign-in process and establish the session.
    *   Implement sign-out functionality using Supabase Auth.
    *   Include necessary email verification flows if Supabase configuration requires it after initial magic link sign-up.
    *   Use the Supabase JS client (`@supabase/auth-helpers-nextjs` or `@supabase/ssr` for App Router) for managing sessions, authentication state, and interacting with Supabase Auth on both client and server.
    *   Leverage **Row Level Security (RLS)** in Supabase for data protection based on authenticated user ID.
*   **Database:**
    *   Interact with **Supabase Database (Postgres)** using the Supabase JS client.
    *   Database access from the client-side should primarily be for data fetching via SWR/React Query (respecting RLS).
    *   Mutations (inserts, updates, deletes) should ideally occur via **Server Actions** or secure **API Routes (Route Handlers)** to ensure proper authorization and validation based on the **authenticated user session**.
*   **Payments & Subscriptions:**
    *   **MUST** use **Stripe Checkout** for initiating subscriptions/payments. Embed or redirect to the hosted checkout page. Associate Stripe Customer ID with the Supabase User ID.
    *   **MUST** use **Stripe Customer Portal** for self-service subscription management. Provide a link/button that redirects the user to their portal session (generated server-side, associated with the authenticated user).
    *   **Webhook Handling:** Implement webhook endpoints using **Next.js Route Handlers** (`app/api/webhooks/stripe/route.ts`). Secure the webhook endpoint using Stripe signatures. Process events like `checkout.session.completed`, `customer.subscription.updated`, `invoice.payment_succeeded` etc., to update the Supabase DB (`subscriptions`, `licenses` tables, linking to the appropriate Supabase User ID). **Webhook handlers MUST be idempotent.**
*   **License Validation API:**
    *   Implement the validation endpoint (e.g., `/api/license/validate`) as a **Next.js Route Handler**.
    *   This handler **MUST** securely verify the license key against Supabase data (checking `licenses` and `license_activations` tables, user entitlement, subscription status, activation limits).
    *   This endpoint will be called by the VSTO add-ins. Ensure it returns clear success/failure responses.
*   **API Routes (Route Handlers):** Use for secure server-side logic needed by the frontend (e.g., creating Stripe Checkout sessions, creating Customer Portal sessions, handling webhooks, potentially proxying some Supabase calls if complex logic/joins are needed server-side). **Ensure routes requiring authentication verify the user's session.**
*   **Server Actions:** Use for form submissions and data mutations directly from Server Components or Client Components where appropriate. **Ensure actions requiring authentication verify the user's session.** Ensure proper validation and error handling within Server Actions.

---

## 3. Coding Standards & Best Practices

*   **TypeScript:** Use strict mode. Define clear types/interfaces for data structures (API responses, DB rows, props). Utilize utility types (`Partial`, `Omit`, etc.) where beneficial.
*   **React:** Follow standard React best practices (rules of hooks, key props for lists, composition). Prefer functional components with hooks.
*   **Next.js:** Leverage Next.js features like `next/image` for optimization, `next/link` for client-side navigation, dynamic imports (`next/dynamic`) for code splitting. Understand Server/Client component boundaries.
*   **Environment Variables:** Use `.env.local` (and `.env.development`/`.env.production`) for secrets (Supabase keys, Stripe keys). Access them using `process.env.VARIABLE_NAME`. Prefix variables exposed to the browser with `NEXT_PUBLIC_`.
*   **Error Handling:** Implement robust error handling for API calls, data fetching, form submissions, and webhook processing. Provide user-friendly feedback (e.g., using Shadcn/ui Alerts or Toasts via libraries like `react-hot-toast` or `sonner`). Log errors server-side (Vercel logs or dedicated service).
*   **Security:**
    *   Validate all user input (client-side and server-side).
    *   Secure API Routes/Server Actions (check authentication/authorization based on Supabase session).
    *   Secure webhooks using signature verification.
    *   Do not expose sensitive keys or logic to the client-side bundle.
    *   Rely on Supabase RLS for data access control.
*   **Performance:** Optimize image loading, leverage Next.js caching, code-split using dynamic imports, minimize Client Component bundle size.
*   **Accessibility (a11y):** Use semantic HTML. Ensure Shadcn/ui components are used correctly regarding accessibility properties. Test for keyboard navigation and screen reader compatibility.
*   **Code Structure:** Organize code logically (e.g., `app/`, `components/`, `lib/` for utilities/Supabase client, `hooks/`, `types/`).

---

## 4. Forbidden Practices

*   **DO NOT** implement traditional password-based authentication flows unless specifically requested as a secondary option. **Prioritize passwordless magic links.**
*   **DO NOT** use the Next.js Pages Router (unless explicitly required).
*   **DO NOT** use JavaScript; use TypeScript exclusively.
*   **DO NOT** use CSS Modules or Styled Components; use Tailwind CSS.
*   **DO NOT** use Redux (unless complexity *truly* demands it later; prefer Zustand/Context first).
*   **DO NOT** expose secret keys (Stripe secret, Supabase service key) directly in client-side code. Use environment variables and access secrets only server-side (Server Components, API Routes, Server Actions).
*   **DO NOT** perform sensitive database mutations directly from the client-side without server-side validation/authorization (checking the user session) via API Routes or Server Actions. Rely on RLS for reads.
*   **DO NOT** process payments directly on the frontend; use Stripe Checkout.
*   **DO NOT** trust webhook payloads without verifying the Stripe signature.

---

