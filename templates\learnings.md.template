# learnings.md - Project Nuances, Preferences, and Rationale (Multi-Project Workspace)

**Purpose:** To retain critical context, user preferences, historical decision rationale, and discovered 'gotchas' or effective patterns that might be lost due to memory resets. This file supplements formal documentation in other Memory Bank files.

**Instructions for Dane:**
*   **Consult:** Review this file during context loading (Principle 1), especially when tasks involve areas known to be tricky, require specific user preferences, or touch upon complex historical decisions. Cross-reference with `constraints.md`, `systemPatterns.md`, and `techContext.md` before making significant design or implementation choices (Act Mode Step 3.5).
*   **Update:** Document new learnings proactively when:
    *   A user expresses a specific preference or coding style choice (e.g., "Prefer functional components").
    *   A non-obvious technical decision is made (document the *rationale* here if it's not fully captured in `progress.md` or `systemPatterns.md`).
    *   A tricky implementation detail, edge case, or common mistake ('gotcha') is discovered and resolved (e.g., "Remember to manually release COM objects in VSTO Add-in").
    *   An effective pattern (or anti-pattern to avoid) specific to this project is identified.
    *   After completing a task, consider if any insights gained should be added here.
*   **Scope:** Clearly associate learnings with the relevant `[Subproject]` or mark as `[Global]`.
*   **Structure:** Use the sections below. Add new learnings chronologically within the relevant section or update existing entries if appropriate.

---

## 1. User Preferences & Workflow

<!-- #UserPreferences -->
*   `[Scope]` **Preference:** [Date] - [Description of preference, e.g., User prefers descriptive variable names even if slightly longer.]
*   `[Frontend]` **Preference:** YYYY-MM-DD - User prefers functional components with Hooks over class components for all new React development.
*   `[Global]` **Workflow:** YYYY-MM-DD - User wants explicit confirmation prompts before running any potentially destructive commands (`requires_approval: true` in relevant tools).

---

## 2. Project Nuances & 'Gotchas'

<!-- #Gotchas -->
*   `[Scope]` **Nuance/Gotcha:** [Date] - [Description of the tricky detail, edge case, or common pitfall.]
    *   **Context/Location:** [Relevant file(s) or component(s)]
    *   **Resolution/Best Practice:** [How to handle it correctly]
*   `[VSTO]` **Gotcha:** YYYY-MM-DD - Forgetting to explicitly release COM objects using `Marshal.ReleaseComObject` can lead to memory leaks in PowerPoint.
    *   **Context/Location:** Any code interacting with the PowerPoint Object Model.
    *   **Resolution/Best Practice:** Always release COM objects in `finally` blocks or using `using` statements where applicable.
*   `[Backend]` **Nuance:** YYYY-MM-DD - The external API X returns inconsistent date formats; always parse dates defensively using `date-fns` tolerant parsing.
    *   **Context/Location:** `backend/src/services/externalApiService.ts`
    *   **Resolution/Best Practice:** Use `try-catch` and multiple parsing attempts if needed.

---

## 3. Historical Rationale (Non-Obvious Decisions)

<!-- #Rationale -->
*   `[Scope]` **Decision:** [Date] - [Brief description of the decision, cross-reference `progress.md` if logged there.]
    *   **Rationale:** [Detailed explanation of *why* this choice was made, especially if counter-intuitive or alternatives were rejected. Why was Approach A rejected for Approach B?]
*   `[Backend]` **Decision:** YYYY-MM-DD - Chose Message Queue system Y over system Z.
    *   **Rationale:** Although Z offered slightly higher throughput in benchmarks, Y was selected due to significantly better documentation, existing team familiarity, and lower operational overhead observed in prior projects, aligning with the 'maintainability' goal in `projectbrief.md`. The performance difference was deemed acceptable for expected load. (See `progress.md` entry [Date]).

---

## 4. Effective Patterns / Anti-Patterns (Project-Specific)

<!-- #ProjectPatterns -->
*   `[Scope]` **Pattern/Anti-Pattern:** [Date] - [Description of the pattern.]
    *   **Type:** [Effective Pattern | Anti-Pattern (Avoid)]
    *   **Context:** [Where it applies]
    *   **Rationale:** [Why it's effective or problematic in *this* project]
*   `[Frontend]` **Effective Pattern:** YYYY-MM-DD - Using custom hook `useSpecificApiData(endpoint)` for fetching data.
    *   **Type:** Effective Pattern
    *   **Context:** All components needing data from the backend API.
    *   **Rationale:** Encapsulates loading/error state management, caching logic (via react-query), and request logic, reducing boilerplate in components. See `frontend/src/hooks/useSpecificApiData.ts`.
*   `[Global]` **Anti-Pattern:** YYYY-MM-DD - Passing large configuration objects deep down the component tree (prop drilling).
    *   **Type:** Anti-Pattern (Avoid)
    *   **Context:** Component composition.
    *   **Rationale:** Makes components less reusable and harder to test. Prefer using React Context or state management libraries for globally needed config/state.

---
