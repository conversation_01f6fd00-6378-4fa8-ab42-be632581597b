terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "azurerm" {
  features {}
}

data "azurerm_client_config" "current" {}

# Random string for unique resource naming
resource "random_string" "suffix" {
  length  = 6
  special = false
  upper   = false
}

resource "azurerm_resource_group" "staging" {
  name     = "rg-quantboost-frontend-staging"
  location = "Central US"
}

# Key Vault for secrets
resource "azurerm_key_vault" "staging" {
  name                = "kv-qb-staging-${random_string.suffix.result}"
  location            = azurerm_resource_group.staging.location
  resource_group_name = azurerm_resource_group.staging.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"

  access_policy {
    tenant_id = data.azurerm_client_config.current.tenant_id
    object_id = data.azurerm_client_config.current.object_id
    secret_permissions = [ "Get", "List", "Set", "Delete", "Purge" ]
  }

  # Access policy for App Service managed identity
  access_policy {
    tenant_id = data.azurerm_client_config.current.tenant_id
    object_id = azurerm_linux_web_app.frontend.identity[0].principal_id
    secret_permissions = [ "Get", "List" ]
  }
}

# Container Registry
resource "azurerm_container_registry" "staging" {
  name                = "acrquantbooststaging${random_string.suffix.result}"
  resource_group_name = azurerm_resource_group.staging.name
  location            = azurerm_resource_group.staging.location
  sku                 = "Basic"
  admin_enabled       = true
}

# Application Insights
resource "azurerm_log_analytics_workspace" "staging" {
  name                = "law-quantboost-staging"
  location            = azurerm_resource_group.staging.location
  resource_group_name = azurerm_resource_group.staging.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

resource "azurerm_application_insights" "staging" {
  name                = "ai-quantboost-staging"
  location            = azurerm_resource_group.staging.location
  resource_group_name = azurerm_resource_group.staging.name
  workspace_id        = azurerm_log_analytics_workspace.staging.id
  application_type    = "Node.JS"
}

# Container Apps Environment
resource "azurerm_container_app_environment" "staging" {
  name                       = "cae-quantboost-staging"
  location                   = azurerm_resource_group.staging.location
  resource_group_name        = azurerm_resource_group.staging.name
  log_analytics_workspace_id = azurerm_log_analytics_workspace.staging.id
}

# App Service Plan for Frontend (S1 Standard tier - uses Standard A Family vCPUs quota)
resource "azurerm_service_plan" "frontend" {
  name                = "asp-quantboost-frontend-staging"
  resource_group_name = azurerm_resource_group.staging.name
  location            = "Central US"  # Try Central US - typically lower demand than West Coast
  os_type             = "Linux"
  sku_name            = "B1"         # Basic A1 tier - try Central US region

  tags = {
    Environment = "staging"
    Component   = "frontend"
  }
}

# Temporary: Static Web Apps (commented out - replaced by App Service)
# resource "azurerm_static_web_app" "frontend" {
#   name                = "swa-quantboost-frontend-staging"
#   resource_group_name = azurerm_resource_group.staging.name
#   location            = "East US 2" # Static Web Apps have limited regions
#   sku_tier            = "Free"
#   sku_size            = "Free"
#
#   app_settings = {
#     "NEXT_PUBLIC_SUPABASE_URL"           = var.supabase_url
#     "SUPABASE_SERVICE_KEY"               = var.supabase_service_role_key
#     "STRIPE_PUBLISHABLE_KEY"             = var.stripe_publishable_key
#     "STRIPE_SECRET_KEY"                  = var.stripe_secret_key
#     "STRIPE_WEBHOOK_SECRET"              = var.stripe_webhook_signing_secret
#     "NEXT_PUBLIC_BASE_URL"               = "https://dev.quantboost.ai"
#     "NEXT_PUBLIC_AZURE_API_URL"          = "https://ca-quantboost-api-staging.placeholder.azurecontainerapps.io"
#     "APPLICATIONINSIGHTS_CONNECTION_STRING" = azurerm_application_insights.staging.connection_string
#   }
#
#   tags = {
#     Environment = "staging"
#     Component   = "frontend"
#   }
# }

# App Service for Frontend (Next.js)
resource "azurerm_linux_web_app" "frontend" {
  name                = "app-quantboost-frontend-staging"
  resource_group_name = azurerm_resource_group.staging.name
  location            = "Central US"  # Match App Service Plan location
  service_plan_id     = azurerm_service_plan.frontend.id

  site_config {
    always_on = true

    application_stack {
      node_version = "18-lts"
    }
  }

  app_settings = {
    # Node.js Configuration
    "NODE_ENV"                               = "production"
    "WEBSITE_NODE_DEFAULT_VERSION"           = "18-lts"
    "SCM_DO_BUILD_DURING_DEPLOYMENT"        = "true"

    # Stripe Configuration
    "STRIPE_SECRET_KEY"                      = var.stripe_secret_key
    "STRIPE_WEBHOOK_SECRET"                  = var.stripe_webhook_signing_secret
    "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"     = var.stripe_publishable_key

    # Supabase Configuration
    "NEXT_PUBLIC_SUPABASE_URL"               = var.supabase_url
    "NEXT_PUBLIC_SUPABASE_ANON_KEY"          = var.supabase_anon_key
    "SUPABASE_SERVICE_KEY"                   = var.supabase_service_role_key

    # Application Configuration
    "NEXT_PUBLIC_BASE_URL"                   = "https://app-quantboost-frontend-staging.azurewebsites.net"
    "NEXT_PUBLIC_AZURE_API_URL"              = "https://ca-quantboost-api-staging.placeholder.azurecontainerapps.io"

    # Monitoring
    "APPLICATIONINSIGHTS_CONNECTION_STRING"  = azurerm_application_insights.staging.connection_string
    "APPINSIGHTS_INSTRUMENTATIONKEY"         = azurerm_application_insights.staging.instrumentation_key
  }

  # Enable system-assigned managed identity for Key Vault access
  identity {
    type = "SystemAssigned"
  }

  # Configure custom domain support
  https_only = true

  tags = {
    Environment = "staging"
    Component   = "frontend"
  }
}

# Key Vault Secrets
resource "azurerm_key_vault_secret" "supabase_url" {
  name         = "supabase-url"
  value        = var.supabase_url
  key_vault_id = azurerm_key_vault.staging.id
}

resource "azurerm_key_vault_secret" "supabase_key" {
  name         = "supabase-anon-key"
  value        = var.supabase_anon_key
  key_vault_id = azurerm_key_vault.staging.id
}

resource "azurerm_key_vault_secret" "supabase_service_key" {
  name         = "supabase-service-role-key"
  value        = var.supabase_service_role_key
  key_vault_id = azurerm_key_vault.staging.id
}

resource "azurerm_key_vault_secret" "jwt_secret" {
  name         = "jwt-secret"
  value        = var.jwt_secret
  key_vault_id = azurerm_key_vault.staging.id
}

resource "azurerm_key_vault_secret" "stripe_secret_key" {
  name         = "stripe-secret-key"
  value        = var.stripe_secret_key
  key_vault_id = azurerm_key_vault.staging.id
}

resource "azurerm_key_vault_secret" "stripe_webhook_secret" {
  name         = "stripe-webhook-signing-secret"
  value        = var.stripe_webhook_signing_secret
  key_vault_id = azurerm_key_vault.staging.id
}

resource "azurerm_key_vault_secret" "stripe_publishable_key" {
  name         = "stripe-publishable-key"
  value        = var.stripe_publishable_key
  key_vault_id = azurerm_key_vault.staging.id
}

// Container App Resource - COMMENTED OUT (requires Docker image to be built first)
// Uncomment after building and pushing the quantboost-api Docker image to the registry
# resource "azurerm_container_app" "api" {
#   name                         = "ca-quantboost-api-staging"
#   container_app_environment_id = azurerm_container_app_environment.staging.id
#   resource_group_name          = azurerm_resource_group.staging.name
#   revision_mode                = "Single"
#
#   identity {
#     type = "SystemAssigned"
#   }
#
#   registry {
#     server               = azurerm_container_registry.staging.login_server
#     username             = azurerm_container_registry.staging.admin_username
#     password_secret_name = "acr-password"
#   }
#
#   secret {
#     name  = "acr-password"
#     value = azurerm_container_registry.staging.admin_password
#   }
#   secret {
#     name  = "supabase-url"
#     value = var.supabase_url
#   }
#   secret {
#     name  = "supabase-anon-key"
#     value = var.supabase_anon_key
#   }
#   secret {
#     name  = "supabase-service-role-key"
#     value = var.supabase_service_role_key
#   }
#   secret {
#     name  = "jwt-secret"
#     value = var.jwt_secret
#   }
#   secret {
#     name  = "stripe-secret-key"
#     value = var.stripe_secret_key
#   }
#   secret {
#     name  = "stripe-webhook-signing-secret"
#     value = var.stripe_webhook_signing_secret
#   }
#
#   template {
#     container {
#       name   = "quantboost-api"
#       image  = "${azurerm_container_registry.staging.login_server}/quantboost-api:latest"
#       cpu    = 0.5
#       memory = "1Gi"
#
#       env {
#         name  = "NODE_ENV"
#         value = "staging"
#       }
#       env {
#         name  = "PORT"
#         value = "3000"
#       }
#       env {
#         name        = "SUPABASE_URL"
#         secret_name = "supabase-url"
#       }
#       env {
#         name        = "SUPABASE_ANON_KEY"
#         secret_name = "supabase-anon-key"
#       }
#       env {
#         name        = "SUPABASE_SERVICE_ROLE_KEY"
#         secret_name = "supabase-service-role-key"
#       }
#       env {
#         name        = "JWT_SECRET"
#         secret_name = "jwt-secret"
#       }
#       env {
#         name  = "APPLICATIONINSIGHTS_CONNECTION_STRING"
#         value = azurerm_application_insights.staging.connection_string
#       }
#       env {
#         name        = "STRIPE_SECRET_KEY"
#         secret_name = "stripe-secret-key"
#       }
#       env {
#         name        = "STRIPE_WEBHOOK_SIGNING_SECRET"
#         secret_name = "stripe-webhook-signing-secret"
#       }
#     }
#
#     min_replicas = 1
#     max_replicas = 3
#
#     http_scale_rule {
#       name                = "http-concurrent-requests"
#       concurrent_requests = 50
#     }
#   }
#
#   ingress {
#     external_enabled = true
#     target_port      = 3000
#
#     traffic_weight {
#       percentage      = 100
#       latest_revision = true
#     }
#   }
# }

################################################################################
# SECTION 2: INSTALLER DISTRIBUTION INFRASTRUCTURE
# Contains resources for hosting the VSTO installer file (.exe) globally.
################################################################################

# 2.1. Resource Group for distribution assets
resource "azurerm_resource_group" "distribution" {
  name     = "rg-quantboost-distribution-staging"
  location = var.distribution_location
  tags = {
    environment = "staging"
    purpose     = "Distribution"
  }
}

# 2.2. Storage Account to host the QuantBoost.exe installer
resource "azurerm_storage_account" "distribution" {
  name                     = "stqboostdiststg${random_string.suffix.result}"
  resource_group_name      = azurerm_resource_group.distribution.name
  location                 = azurerm_resource_group.distribution.location
  account_tier             = "Standard"
  account_replication_type = "LRS" # Use LRS for staging to save costs
}

# 2.3. Storage Container for the installer files
resource "azurerm_storage_container" "releases" {
  name                  = "releases"
  storage_account_id    = azurerm_storage_account.distribution.id
  container_access_type = "blob"
}

# 2.4. CDN Profile for global content delivery
resource "azurerm_cdn_profile" "main" {
  name                = "cdn-quantboost-staging"
  resource_group_name = azurerm_resource_group.distribution.name
  location            = azurerm_resource_group.distribution.location
  sku                 = "Standard_Microsoft"
}

# 2.5. CDN Endpoint linked to the storage account
resource "azurerm_cdn_endpoint" "main" {
  name                = "quantboost-downloads-staging-${random_string.suffix.result}"
  profile_name        = azurerm_cdn_profile.main.name
  resource_group_name = azurerm_resource_group.distribution.name
  location            = azurerm_resource_group.distribution.location

  origin {
    name      = "storage-origin"
    host_name = azurerm_storage_account.distribution.primary_blob_host
  }
}

################################################################################
# SECTION 3: AZURE CODE SIGNING INFRASTRUCTURE (STAGING)
# Contains resources for the managed code signing service.
################################################################################

# 3.1. Azure Trusted Signing Account (formerly Azure Code Signing)
# Note: Requires Azure provider ~> 4.0 and Microsoft.CodeSigning resource provider registration
# Geographic restriction: Only available in USA/Canada with 3+ years business history
resource "azurerm_trusted_signing_account" "main" {
  name                = "tsa-qb-stg-${random_string.suffix.result}"
  resource_group_name = azurerm_resource_group.distribution.name
  location            = azurerm_resource_group.distribution.location
  sku_name            = "Basic"

  tags = {
    Environment = "staging"
    Purpose     = "VSTO installer signing"
  }
}

# 3.2. Certificate Profile for signing staging installers
# Note: Certificate profile resource may not be available in current Terraform provider
# This will need to be created manually via Azure portal after account creation
# resource "azurerm_trusted_signing_certificate_profile" "staging_trust" {
#   name                         = "cp-staging-publictrust"
#   trusted_signing_account_name = azurerm_trusted_signing_account.main.name
#   resource_group_name          = azurerm_resource_group.distribution.name
#   profile_type                 = "PublicTrust"
#
#   # Identity validation ID must be obtained after manual validation process
#   # This will need to be updated after identity validation is completed
#   identity_validation_id = "********-0000-0000-0000-********0000"  # Placeholder - update after validation
#
#   tags = {
#     Environment = "staging"
#     Purpose     = "VSTO installer signing"
#   }
#
#   depends_on = [azurerm_trusted_signing_account.main]
# }

# Outputs
output "container_registry_name" {
  description = "Container Registry name"
  value       = azurerm_container_registry.staging.name
}

output "container_registry_server" {
  description = "Container Registry server URL"
  value       = azurerm_container_registry.staging.login_server
}

# Container App output - commented out until Container App is deployed via GitHub Actions
# output "container_app_fqdn" {
#   description = "Container App FQDN"
#   value       = azurerm_container_app.api.latest_revision_fqdn
# }

output "resource_group_name" {
  description = "Resource Group name"
  value       = azurerm_resource_group.staging.name
}

output "key_vault_name" {
  description = "Key Vault name"
  value       = azurerm_key_vault.staging.name
}

output "app_service_default_hostname" {
  description = "App Service default hostname"
  value       = azurerm_linux_web_app.frontend.default_hostname
}

output "app_service_name" {
  description = "App Service name"
  value       = azurerm_linux_web_app.frontend.name
}

output "app_service_plan_name" {
  description = "App Service Plan name"
  value       = azurerm_service_plan.frontend.name
}

output "app_service_principal_id" {
  description = "App Service managed identity principal ID"
  value       = azurerm_linux_web_app.frontend.identity[0].principal_id
}

# --- Outputs for Distribution & Signing Infrastructure ---
output "distribution_resource_group_name" {
  description = "The name of the Distribution Resource Group"
  value       = azurerm_resource_group.distribution.name
}

output "distribution_storage_account_name" {
  description = "Storage account name for installer uploads"
  value       = azurerm_storage_account.distribution.name
}

output "distribution_cdn_url_default" {
  description = "The default Azure CDN URL for the staging installer"
  value       = "https://${azurerm_cdn_endpoint.main.fqdn}/releases/QuantBoost-staging.exe"
}

output "distribution_cdn_url_custom" {
  description = "The custom domain URL for staging downloads"
  value       = "https://staging-download.quantboost.ai/QuantBoost-staging.exe"
}

output "upload_instructions" {
  description = "Instructions for uploading the installer file"
  value       = "Upload QuantBoost-staging.exe to Storage Account '${azurerm_storage_account.distribution.name}' into the container '${azurerm_storage_container.releases.name}'"
}

output "trusted_signing_account_name" {
  description = "The name of the Azure Trusted Signing account for CI/CD pipeline"
  value       = azurerm_trusted_signing_account.main.name
}

# Certificate profile output - commented out until resource is available in Terraform
# output "trusted_signing_certificate_profile_name" {
#   description = "The name of the certificate profile for signing the staging installer"
#   value       = azurerm_trusted_signing_certificate_profile.staging_trust.name
# }

output "dns_instructions" {
  description = "CNAME record required for the custom staging domain"
  value       = "Create a CNAME record for 'staging-download.quantboost.ai' pointing to '${azurerm_cdn_endpoint.main.fqdn}'"
}
