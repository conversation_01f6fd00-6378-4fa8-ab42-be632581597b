"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';

function CallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');
  const supabase = useSupabaseClient();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Handle magic link authentication using Supabase's built-in session handling
        // This will automatically process any auth tokens in the URL
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Session error:', error);
          setStatus('error');
          setMessage('Authentication failed. Please try logging in again.');
          return;
        }

        if (data.session) {
          setStatus('success');
          setMessage('Authentication successful! Redirecting to dashboard...');

          // Clean up the URL
          window.history.replaceState({}, document.title, window.location.pathname);

          setTimeout(() => {
            router.push('/dashboard');
          }, 1500);
        } else {
          // No session found, redirect to login
          setStatus('error');
          setMessage('No authentication session found. Redirecting to login...');

          setTimeout(() => {
            router.push('/auth/login');
          }, 2000);
        }
      } catch (error) {
        console.error('Unexpected auth callback error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try logging in again.');
      }
    };

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state change:', event, session);

      if (event === 'SIGNED_IN' && session) {
        setStatus('success');
        setMessage('Authentication successful! Redirecting to dashboard...');

        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);
      } else if (event === 'SIGNED_OUT') {
        setStatus('error');
        setMessage('Authentication failed. Please try logging in again.');
      }
    });

    // Initial callback handling
    handleAuthCallback();

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth, router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-lg p-8 text-center space-y-6">
        {/* Loading State */}
        {status === 'loading' && (
          <>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
            <h1 className="text-xl font-semibold text-gray-900">Authenticating...</h1>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {/* Success State */}
        {status === 'success' && (
          <>
            <div className="flex justify-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-green-800">Success!</h1>
            <p className="text-gray-600">{message}</p>
          </>
        )}

        {/* Error State */}
        {status === 'error' && (
          <>
            <div className="flex justify-center">
              <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-red-800">Authentication Failed</h1>
            <p className="text-gray-600">{message}</p>
            <button
              onClick={() => router.push('/auth/login')}
              className="w-full py-3 bg-gray-800 hover:bg-gray-900 text-white font-medium rounded-lg transition-colors"
            >
              Try Again
            </button>
          </>
        )}
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full mx-4 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing Authentication</h2>
          <p className="text-gray-600">Please wait while we complete your login...</p>
        </div>
      </div>
    }>
      <CallbackContent />
    </Suspense>
  );
}
