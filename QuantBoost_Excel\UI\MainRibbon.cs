// --- START OF MainRibbon.cs ---

#region Using Directives

using System;
using System.IO;
using System.Reflection;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Linq;
using Office = Microsoft.Office.Core;
using Excel = Microsoft.Office.Interop.Excel;
using Microsoft.Office.Tools;

// Project specific namespaces
using QuantBoost_Licensing;
using QuantBoost_Shared.Security;
using QuantBoost_Shared.UI;
using QuantBoost_Shared.Utilities;
// UI namespace is current, so no need for 'using QuantBoost_Excel.UI;' here

#endregion

namespace QuantBoost_Excel.UI
{
    [ComVisible(true)]
    public class MainRibbon : Office.IRibbonExtensibility
    {
        #region Fields
        private Office.IRibbonUI _ribbonUI;
        private CustomTaskPane _analyzeTaskPane;
        private QuantBoost_Excel.Features.SizeAnalyzer.UI.WpfHostControl _analyzePaneControl;
        #endregion

        #region Constructor
        public MainRibbon()
        {
            ErrorHandlingService.LogException(null, "MainRibbon Constructor executed.");
        }
        #endregion

        #region IRibbonExtensibility Implementation
        public string GetCustomUI(string ribbonID)
        {
            if (ribbonID != "Microsoft.Excel.Workbook") return null;
            string resourceName = "QuantBoost_Excel.UI.MainRibbon.xml";
            ErrorHandlingService.LogException(null, $"GetCustomUI: Attempting to load Ribbon XML '{resourceName}'.");
            System.Diagnostics.Debug.WriteLine($"[DEBUG] GetCustomUI: Attempting to load Ribbon XML: '{resourceName}'");
            Stream stream = null;
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    string errorMessage = $"Ribbon XML resource not found: '{resourceName}'. Check Build Action (Embedded Resource) and name.";
                    System.Diagnostics.Debug.WriteLine($"[ERROR] GetCustomUI: {errorMessage}");
                    var exToLog = new FileNotFoundException(errorMessage, resourceName);
                    ErrorHandlingService.LogException(exToLog, "GetCustomUI - Resource Stream Null");
                    throw exToLog;
                }
                using (StreamReader reader = new StreamReader(stream))
                {
                    stream = null; // Handed off to reader
                    string xmlContent = reader.ReadToEnd();
                    System.Diagnostics.Debug.WriteLine($"[SUCCESS] GetCustomUI: Loaded Ribbon XML: {resourceName}");
                    ErrorHandlingService.LogException(null, $"Successfully loaded Ribbon XML: {resourceName}");
                    return xmlContent;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[EXCEPTION] GetCustomUI: {ex.ToString()}");
                bool isOurFileNotFound = ex is FileNotFoundException && ex.Message.Contains("Ribbon XML resource not found");
                if (!isOurFileNotFound) ErrorHandlingService.HandleException(ex, $"Unexpected Fatal Error Loading Ribbon XML: {resourceName}");
                return null;
            }
            finally { stream?.Dispose(); }
        }

        public void OnLoad(Office.IRibbonUI ribbonUI)
        {
            ErrorHandlingService.LogException(null, "MainRibbon OnLoad executed.");
            _ribbonUI = ribbonUI;
            Task.Delay(500).ContinueWith(t => // Use t for task parameter
            {
                QuantBoost_Shared.Utilities.AsyncHelper.RunOnUIThread(() =>
                {
                    try
                    {
                        ErrorHandlingService.LogException(null, "Triggering initial license UI update from OnLoad delay.");
                        var licenseManager = Globals.ThisAddIn?._licensingManager;
                        LicenseDetails currentLicenseSDK = licenseManager?.CurrentLicense;
                        LicenseDetailsClient clientDetails = Globals.ThisAddIn.MapToClientDetails(currentLicenseSDK);
                        UpdateLicenseUI(clientDetails);
                    }
                    catch (Exception ex) { ErrorHandlingService.LogException(ex, "Error during delayed initial license UI update."); }
                });
            }, TaskScheduler.Default);
        }
        #endregion

        #region Excel Trace
        /// <summary>
        /// Returns the custom Excel Trace logo image for the ribbon button.
        /// Uses Microsoft M365 guidelines with multiple icon sizes for optimal display.
        /// </summary>
        /// <param name="control">The ribbon control requesting the image.</param>
        /// <returns>A Bitmap object containing the Excel Trace logo at the optimal size.</returns>
        public Bitmap GetExcelTraceImage(Office.IRibbonControl control)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();

                // For large ribbon buttons, use the largest available size (96x96) for maximum impact
                // This follows Microsoft's M365 guidelines for high-DPI displays and large buttons
                string resourceName = "QuantBoost_Excel.Resources.trace_96x96.png";

                using (var stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream != null)
                    {
                        // Return the 96x96 image directly - no scaling needed
                        // Office will handle any necessary scaling for different display contexts
                        return new Bitmap(stream);
                    }
                    else
                    {
                        ErrorHandlingService.LogException(null, $"GetExcelTraceImage: Could not load {resourceName} from embedded resources.");

                        // Fallback to 64x64 if 96x96 fails
                        using (var fallbackStream = assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.trace_64x64.png"))
                        {
                            if (fallbackStream != null)
                            {
                                ErrorHandlingService.LogException(null, "GetExcelTraceImage: Using 64x64 fallback image.");
                                return new Bitmap(fallbackStream);
                            }
                        }

                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error loading Excel Trace logo image.");
                return null;
            }
        }

        /// <summary>
        /// Returns the custom Size Analyzer image for the ribbon button.
        /// Uses Microsoft M365 guidelines with multiple icon sizes for optimal display.
        /// </summary>
        /// <param name="control">The ribbon control requesting the image.</param>
        /// <returns>A Bitmap object containing the Size Analyzer icon at the optimal size.</returns>
        public Bitmap GetSizeAnalyzerImage(Office.IRibbonControl control)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();

                // For large ribbon buttons, use the largest available size (96x96) for maximum impact
                // This follows Microsoft's M365 guidelines for high-DPI displays and large buttons
                string resourceName = "QuantBoost_Excel.Resources.excel_size_96x96.PNG";

                using (var stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream != null)
                    {
                        // Return the 96x96 image directly - no scaling needed
                        // Office will handle any necessary scaling for different display contexts
                        return new Bitmap(stream);
                    }
                    else
                    {
                        ErrorHandlingService.LogException(null, $"GetSizeAnalyzerImage: Could not load {resourceName} from embedded resources.");

                        // Fallback to 80x80 if 96x96 fails
                        using (var fallbackStream = assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.excel_size_80x80.PNG"))
                        {
                            if (fallbackStream != null)
                            {
                                ErrorHandlingService.LogException(null, "GetSizeAnalyzerImage: Using 80x80 fallback image.");
                                return new Bitmap(fallbackStream);
                            }
                        }

                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error loading Size Analyzer icon image.");
                return null;
            }
        }



        /// <summary>
        /// Returns the custom login image for the ribbon button.
        /// Uses Microsoft M365 guidelines with multiple icon sizes for optimal display.
        /// </summary>
        /// <param name="control">The ribbon control requesting the image.</param>
        /// <returns>A Bitmap object containing the login icon at the optimal size.</returns>
        public Bitmap GetLoginImage(Office.IRibbonControl control)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();

                // For large ribbon buttons, use the largest available size (96x96) for maximum impact
                // This follows Microsoft's M365 guidelines for high-DPI displays and large buttons
                string resourceName = "QuantBoost_Excel.Resources.login_96x96.PNG";

                using (var stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream != null)
                    {
                        // Return the 96x96 image directly - no scaling needed
                        // Office will handle any necessary scaling for different display contexts
                        return new Bitmap(stream);
                    }
                    else
                    {
                        ErrorHandlingService.LogException(null, $"GetLoginImage: Could not load {resourceName} from embedded resources.");

                        // Fallback to 80x80 if 96x96 fails
                        using (var fallbackStream = assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.login_80x80.PNG"))
                        {
                            if (fallbackStream != null)
                            {
                                ErrorHandlingService.LogException(null, "GetLoginImage: Using 80x80 fallback image.");
                                return new Bitmap(fallbackStream);
                            }
                        }

                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error loading login icon image.");
                return null;
            }
        }

        /// <summary>
        /// Returns the custom logout image for the ribbon button.
        /// Uses Microsoft M365 guidelines with multiple icon sizes for optimal display.
        /// </summary>
        /// <param name="control">The ribbon control requesting the image.</param>
        /// <returns>A Bitmap object containing the logout icon at the optimal size.</returns>
        public Bitmap GetLogoutImage(Office.IRibbonControl control)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();

                // For large ribbon buttons, use the largest available size (96x96) for maximum impact
                // This follows Microsoft's M365 guidelines for high-DPI displays and large buttons
                string resourceName = "QuantBoost_Excel.Resources.logout_96x96.PNG";

                using (var stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream != null)
                    {
                        // Return the 96x96 image directly - no scaling needed
                        // Office will handle any necessary scaling for different display contexts
                        return new Bitmap(stream);
                    }
                    else
                    {
                        ErrorHandlingService.LogException(null, $"GetLogoutImage: Could not load {resourceName} from embedded resources.");

                        // Fallback to 80x80 if 96x96 fails
                        using (var fallbackStream = assembly.GetManifestResourceStream("QuantBoost_Excel.Resources.logout_80x80.PNG"))
                        {
                            if (fallbackStream != null)
                            {
                                ErrorHandlingService.LogException(null, "GetLogoutImage: Using 80x80 fallback image.");
                                return new Bitmap(fallbackStream);
                            }
                        }

                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error loading logout icon image.");
                return null;
            }
        }

        public void OnTracePrecedents_Click(Office.IRibbonControl control)
        {
            try
            {
                // 1. Perform licensing validation first.
                if (!EnsureThisAddInReady("Excel Trace") || !IsPremiumFeatureEnabled(control))
                {
                    MessageBox.Show("A valid license is required to use this feature.", "QuantBoost", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 2. Call the centralized method that contains all the core logic.
                // This ensures both the ribbon button and hotkey behave identically.
                Globals.ThisAddIn.LaunchTraceForActiveCell();
            }
            catch (Exception ex)
            {
                QuantBoost_Shared.Utilities.ErrorHandlingService.HandleException(ex, "An unexpected error occurred in Excel Trace ribbon handler.");
                MessageBox.Show("An unexpected error occurred. Please see the logs for details.", "QuantBoost Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Helper class for ShowDialog to ensure the form is properly owned by Excel
        public class Win32Window : System.Windows.Forms.IWin32Window
        {
            public IntPtr Handle { get; private set; }
            public Win32Window(int hwnd)
            {
                Handle = new IntPtr(hwnd);
            }
        }

        /// <summary>
        /// Recursively counts the total number of nodes in a trace tree.
        /// </summary>
        /// <param name="node">The root node to count from.</param>
        /// <returns>Total number of nodes including the root.</returns>
        private int CountTotalNodes(QuantBoost_Excel.Features.ExcelTrace.Model.TraceNode node)
        {
            if (node == null) return 0;

            int count = 1; // Count this node
            foreach (var child in node.Children)
            {
                count += CountTotalNodes(child);
            }
            return count;
        }

        /// <summary>
        /// Handles the Size Analyzer button click.
        /// Opens the Size Analyzer task pane for analyzing Excel workbook file sizes.
        /// </summary>
        /// <param name="control">The ribbon control that was clicked.</param>
        public void OnSizeAnalyzer_Click(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnSizeAnalyzer_Click triggered.");

                // 1. Perform licensing validation first.
                if (!EnsureThisAddInReady("Size Analyzer") || !IsPremiumFeatureEnabled(control))
                {
                    MessageBox.Show("A valid license is required to use this feature.", "QuantBoost", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 2. Create or show the Size Analyzer task pane
                if (_analyzeTaskPane == null)
                {
                    ErrorHandlingService.LogException(null, "Creating Size Analyzer Task Pane...");
                    _analyzePaneControl = new QuantBoost_Excel.Features.SizeAnalyzer.UI.WpfHostControl();
                    _analyzeTaskPane = Globals.ThisAddIn.CustomTaskPanes.Add(_analyzePaneControl, "QuantBoost Workbook Size Analyzer");
                    _analyzeTaskPane.DockPosition = Office.MsoCTPDockPosition.msoCTPDockPositionRight;
                    _analyzeTaskPane.Width = 800; // Wide enough to show all columns comfortably
                    _analyzeTaskPane.VisibleChanged += AnalyzeTaskPane_VisibleChanged;
                }

                // 3. Show the task pane and start analysis
                _analyzeTaskPane.Visible = true;
                ErrorHandlingService.LogException(null, "Size Analyzer Task Pane shown successfully.");
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, "An unexpected error occurred in Size Analyzer ribbon handler.");
                MessageBox.Show("An unexpected error occurred. Please see the logs for details.", "QuantBoost Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the visibility changed event for the Size Analyzer task pane.
        /// </summary>
        private void AnalyzeTaskPane_VisibleChanged(object sender, EventArgs e)
        {
            try
            {
                if (_analyzeTaskPane != null && !_analyzeTaskPane.Visible)
                {
                    ErrorHandlingService.LogException(null, "Size Analyzer Task Pane hidden by user.");
                    // Clear any ongoing analysis when the pane is hidden
                    _analyzePaneControl?.ClearResults();
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error in AnalyzeTaskPane_VisibleChanged event handler.");
            }
        }
        #endregion



        #region Ribbon Callbacks - Account Group
        public void OnManageAccountClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnManageAccountClick triggered.");
                if (!EnsureThisAddInReady("Manage Account Dialog")) return;
                var licenseManager = Globals.ThisAddIn._licensingManager;
                if (licenseManager == null)
                {
                    ToastNotifier.ShowToast("Licensing service is unavailable.", 3000, Color.OrangeRed);
                    ErrorHandlingService.LogException(null, "Manage Account: Licensing Manager is null."); return;
                }
                LicenseDetails currentLicenseSDK = licenseManager.CurrentLicense;
                LicenseDetailsClient clientDetails = Globals.ThisAddIn.MapToClientDetails(currentLicenseSDK);
                string productId = ThisAddIn.PRODUCT_ID;
                string deviceId = Globals.ThisAddIn.GetOrCreateDeviceId();
                using (var dlg = new LicenseDialog(clientDetails, licenseManager as IQuantBoostLicensingManager, productId, deviceId))
                {
                    dlg.TriggerLicenseStatusUpdate = () => Globals.ThisAddIn.TriggerLicenseStatusUpdateForRibbon();
                    dlg.GetApiBaseUrl = () => Globals.ThisAddIn.GetApiBaseUrl();
                    dlg.ShowDialog();
                }
                InvalidateRibbonControl("btnManageAccount");
                InvalidateRibbonControl("btnLogout");
            }
            catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error showing Manage Account dialog"); }
        }

        public string GetManageAccountLabel(Office.IRibbonControl control)
        {
            if (!EnsureThisAddInReady("GetManageAccountLabel", false)) return "Manage Account (Error)";
            string token = TokenStorage.RetrieveRefreshToken();
            var currentLicense = Globals.ThisAddIn._licensingManager?.CurrentLicense;
            if (!string.IsNullOrEmpty(token) && currentLicense != null && !string.IsNullOrEmpty(currentLicense.Email))
                return $"Account: {currentLicense.Email.Split('@')[0]}";
            return "Login / Manage License";
        }

        public bool GetManageAccountVisible(Office.IRibbonControl control) => true;

        public bool GetLogoutButtonVisible(Office.IRibbonControl control)
        {
            if (!EnsureThisAddInReady("GetLogoutButtonVisible", false)) return false;
            return !string.IsNullOrEmpty(TokenStorage.RetrieveRefreshToken());
        }

        public async void OnLogoutClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnLogoutClick triggered.");
                if (!EnsureThisAddInReady("Logout")) return;

                // 1. Clear the locally stored refresh token. This is the most critical step.
                TokenStorage.ClearRefreshToken();
                ErrorHandlingService.LogException(null, "Local refresh token cleared.");

                // The server-side logout API call is removed. The client does not persist the short-lived
                // access token required by the server, so the call would always fail.
                // The user is effectively logged out on this client by clearing the local token.
                ToastNotifier.ShowToast("Logged out successfully.", 2000, Color.Green);

                var thisAddIn = Globals.ThisAddIn;
                if (thisAddIn._licensingManager != null)
                {
                    // 2. Clear the in-memory license details and refresh the status.
                    thisAddIn._licensingManager.ClearCurrentLicense();
                    await thisAddIn._licensingManager.RefreshLicenseStatusAsync();
                }

                // 3. Update the Ribbon UI to reflect the logged-out state.
                UpdateLicenseUI(null);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, "Error during logout");
                ToastNotifier.ShowToast("Error during logout.", 3000, Color.Red);
            }
        }
        #endregion

        #region Ribbon XML Callbacks - Getters
        public bool IsPremiumFeatureEnabled(Office.IRibbonControl control)
        {
            if (!EnsureThisAddInReady("IsPremiumFeatureEnabled", false)) return false;
            var licenseManager = Globals.ThisAddIn._licensingManager;
            if (licenseManager == null) { ErrorHandlingService.LogException(null, "IsPremiumFeatureEnabled: Licensing Manager null."); return false; }
            LicenseDetails currentLicenseSDK = licenseManager.CurrentLicense;
            LicenseDetailsClient clientDetails = Globals.ThisAddIn.MapToClientDetails(currentLicenseSDK);
            bool isActive = clientDetails != null && clientDetails.IsSubscriptionEffectivelyActive;
            if (!isActive) ErrorHandlingService.LogException(null, $"IsPremiumFeatureEnabled: Feature '{control.Id}' disabled. Status: {clientDetails?.LastKnownStatus}");
            return isActive;
        }
        #endregion

        #region UI Update & Invalidation
        public void UpdateLicenseUI(LicenseDetailsClient license)
        {
            ErrorHandlingService.LogException(null, $"UpdateLicenseUI called. License active: {license?.IsSubscriptionEffectivelyActive ?? false}");
            try
            {
                InvalidateRibbonControl("btnTracePrecedents");
                InvalidateRibbonControl("btnSizeAnalyzer");
                InvalidateRibbonControl("btnManageAccount");
                InvalidateRibbonControl("btnLogout");
            }
            catch (Exception ex) { ErrorHandlingService.LogException(ex, "Error during Ribbon UI invalidation in UpdateLicenseUI."); }
        }

        public void InvalidateRibbonControl(string controlId)
        {
            if (_ribbonUI != null) { try { _ribbonUI.InvalidateControl(controlId); } catch (Exception ex) { ErrorHandlingService.LogException(ex, $"Error invalidating Ribbon control: {controlId}"); } }
        }
        #endregion

        #region Helper Methods
        private bool EnsureThisAddInReady(string operationName, bool showUserMessage = true)
        {
            if (Globals.ThisAddIn == null)
            {
                string errorMsg = $"Cannot perform '{operationName}': Add-in core not available.";
                ErrorHandlingService.LogException(new InvalidOperationException(errorMsg), "Add-in State Error");
                if (showUserMessage) QuantBoost_Shared.Utilities.AsyncHelper.RunOnUIThread(() => ToastNotifier.ShowToast(errorMsg, 3000, Color.Red));
                return false;
            }
            return true;
        }
        #endregion
    }
}
// --- END OF MainRibbon.cs ---
