// supabaseClient.js

const { createClient } = require('@supabase/supabase-js');
// Environment variables are only loaded for non-production environments
if (process.env.NODE_ENV !== 'production') {
  require('dotenv').config();
}

const SUPABASE_URL = process.env.SUPABASE_URL;
// IMPORTANT: Make sure your .env file has both of these keys defined
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('CRITICAL: SUPABASE_URL, SUPABASE_ANON_KEY, and SUPABASE_SERVICE_ROLE_KEY must all be defined in your .env file.');
}

// Standard client for user-facing operations (uses 'anon' key)
// This is used for all auth functions (login, refresh, getUser)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Admin client for trusted backend operations (uses 'service_role' key)
// This client bypasses all RLS policies. Use it for system tasks.
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

console.log('Supabase clients (standard and admin) initialized successfully.');

// Export both clients in an object
module.exports = { supabase, supabaseAdmin };
