---
title: Structure of QuantBoost Excel Add-in (Planned)
purpose: Outlines the planned high-level directory and key file structure for the QuantBoost Excel Add-in project.
projects: ["excel-add-in"]
source_analysis: "Derived from development guides and common VSTO project structure."
status: bootstrapping
last_updated: 2025-05-13T17:40:00Z
tags: ["excel-add-in", "structure", "planned"]
---

## Planned Project Structure: QuantBoost Excel Add-in

This document outlines the anticipated high-level directory structure for the VSTO Excel Add-in project. This is a planned structure and will be created as development begins.

```
QuantBoost.ExcelAddin/ (Root Project Folder)
|-- QuantBoost.ExcelAddin.csproj
|-- QuantBoost.ExcelAddin.sln (if standalone, or part of a larger solution)
|
|-- ThisAddIn.cs                 # Main add-in class, handles startup, shutdown, core eventing
|-- QuantBoostExcelRibbon.cs       # Code-behind for the custom Excel Ribbon
|-- QuantBoostExcelRibbon.xml      # XML definition for the custom Excel Ribbon
|
|-- App.config
|-- packages.config (if not using PackageReference in csproj)
|
|-- Features/
|   |-- NameScrubber/
|   |   |-- NameScrubberController.cs
|   |   |-- NameScrubberService.cs
|   |   |-- UI/
|   |   |   |-- NameScrubberPane.cs         # UserControl for the Task Pane
|   |   |   |-- NameScrubberPane.Designer.cs
|   |   |   |-- NameScrubberPane.resx
|   |   |-- Models/
|   |   |   |-- NameInfo.cs
|   |
|   |-- ExcelTrace/
|   |   |-- ExcelTraceController.cs
|   |   |-- DependencyTracerService.cs
|   |   |-- HotkeyManager.cs
|   |   |-- UI/
|   |   |   |-- ExcelTracePane.cs           # UserControl for the Task Pane
|   |   |   |-- ExcelTracePane.Designer.cs
|   |   |   |-- ExcelTracePane.resx
|   |   |-- Models/
|   |   |   |-- DependentInfo.cs
|   |
|   |-- SheetAnalyzer/
|   |   |-- SheetAnalyzerController.cs
|   |   |-- WorkbookAnalyzerService.cs
|   |   |-- UI/
|   |   |   |-- SheetAnalyzerPane.cs        # UserControl for the Task Pane
|   |   |   |-- SheetAnalyzerPane.Designer.cs
|   |   |   |-- SheetAnalyzerPane.resx
|   |   |-- Models/
|   |   |   |-- SheetSizeInfo.cs
|   |   |   |-- WorkbookSizeResult.cs
|
|-- Services/                     # Potentially for shared services if not feature-specific
|   |-- LicensingService.cs         # Wrapper or helper for QuantBoost.Licensing.SDK interactions
|
|-- UI/                           # Common UI elements, dialogs, base classes
|   |-- Dialogs/
|   |   |-- ActivationDialog.cs       # (If SDK doesn't provide all needed UI)
|   |-- BaseTaskPane.cs             # Optional base class for common Task Pane logic
|
|-- Utilities/
|   |-- AsyncHelper.cs
|   |-- ErrorHandlingService.cs
|   |-- ToastNotifier.cs
|   |-- ComHelper.cs                # Utilities for COM object management
|   |-- OpenXmlHelper.cs            # Utilities for OpenXML operations
|
|-- Properties/
|   |-- AssemblyInfo.cs
|   |-- Resources.resx
|   |-- Settings.settings
|
|-- Resources/                    # Images, icons for Ribbon and UI
|
|-- QuantBoost.Licensing.dll      # (Reference to the shared Licensing SDK project/assembly)
```

### Key Components Explanation:

*   **`ThisAddIn.cs`**: Entry point for the VSTO add-in. Manages lifecycle, global event handlers (like license status changes), and initialization of core components like the Ribbon and `AsyncHelper`.
*   **`QuantBoostExcelRibbon.cs/.xml`**: Defines the custom tab and controls for Excel, providing user access to the add-in's features.
*   **`Features/`**: This directory will be organized by major feature (Name Scrubber, Excel Trace, Sheet Size Analyzer).
    *   Each feature subfolder will contain its specific `Controller` (to manage UI logic and orchestrate calls to services), `Service` (to encapsulate the core business logic of the feature), UI elements (like `TaskPane` UserControls), and `Models` (data structures specific to that feature).
*   **`Services/` (Shared)**: May contain services that are shared across features, such as a more dedicated wrapper around the `QuantBoost.Licensing.SDK` if needed, though much of this interaction might live in `ThisAddIn.cs` or individual feature controllers.
*   **`UI/` (Common)**: For shared UI components, such as custom base classes for Task Panes, or common dialogs (though the Licensing SDK is expected to provide its own activation dialogs).
*   **`Utilities/`**: Contains helper classes for common tasks like asynchronous operations (`AsyncHelper`), error handling and logging (`ErrorHandlingService`), toast notifications (`ToastNotifier`), and potentially wrappers for COM or OpenXML operations if they become complex enough to warrant dedicated helpers.
*   **`Properties/` & `Resources/`**: Standard VSTO project folders for assembly information, project settings, and embedded resources like images.

This structure aims for modularity and separation of concerns, making the codebase easier to understand, maintain, and test. It mirrors many of the good practices observed in the PowerPoint add-in's structure.
