---
title: Technical Constraints for QuantBoost_Frontend
purpose: To document any implicit or explicit technical constraints identified in the QuantBoost_Frontend project.
projects: ["QuantBoost_Frontend"]
source_analysis: Codebase analysis of QuantBoost_Frontend
status: bootstrapped-incomplete
last_updated: 2025-05-13T00:00:00.000Z
tags: ["frontend", "constraints", "dependencies"]
---

## QuantBoost_Frontend Technical Constraints

### Explicit Constraints (from configuration & code)

1.  **Environment:**
    *   **Node.js:** Required for Next.js. Specific version might be implied by `package.json` (not reviewed) or Next.js version compatibility.
    *   **Next.js Version:** The project uses Next.js with the App Router. The specific version (from `package.json`) would dictate compatible features and APIs.
    *   **TypeScript Version:** As specified in `package.json` or inferred by `tsconfig.json` settings. `target: "ES2017"` in `tsconfig.json`.
2.  **Supabase Dependency:**
    *   The application is tightly coupled with Supabase for authentication and as a backend.
    *   Requires specific environment variables: `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`.
    *   Assumes a Supabase project is configured with appropriate auth settings (and potentially database schema).
3.  **Stripe Dependency:**
    *   Essential for payment processing and subscription management.
    *   Requires Stripe Price IDs to be hardcoded or configured (currently hardcoded in `pricing/page.tsx`).
    *   Assumes a Stripe account is set up with corresponding products and prices.
    *   The backend API route (`/api/checkout/create-session`) needs Stripe secret keys, typically via environment variables.
4.  **Tailwind CSS:**
    *   The UI is built with Tailwind CSS. Removing or replacing it would require a significant rewrite of component styling.
5.  **Shadcn/ui:**
    *   The core UI components (`Button`, `Card`, `Input`, etc.) are from Shadcn/ui. This implies a dependency on its structure and conventions (including Radix UI primitives).
6.  **Font Dependency (Geist):**
    *   `layout.tsx` specifies `Geist` and `Geist_Mono` fonts. While replaceable, it's a current project choice.

### Implicit Constraints & Assumptions

1.  **Build Process:**
    *   Relies on the Next.js build system (`next build`).
    *   Deployment environment must support Next.js applications (e.g., Vercel, Node.js server).
2.  **API Contract with Backend (`/api/checkout/create-session`):**
    *   The frontend expects this API endpoint to exist and function as specified (accept `priceId`, `userId`; return Stripe session URL).
    *   Any changes to this API contract would require frontend updates.
3.  **External Service Availability:**
    *   Functionality depends on the uptime and availability of Supabase, Stripe, YouTube (for demos), and potentially Macabacus.com (for embedded demos).
4.  **Security Model:**
    *   Relies on Supabase for secure authentication.
    *   Sensitive operations (like Stripe session creation) are delegated to backend API routes to protect secret keys. This security model must be maintained.
5.  **Performance Considerations:**
    *   While Next.js offers performance optimizations (static generation, server components), large client-side bundles or numerous API calls could still impact performance.
    *   The embedded iframes (YouTube, Macabacus) can also impact page load times and performance.
6.  **Browser Compatibility:**
    *   As a modern web application using Next.js and Tailwind CSS, it likely targets evergreen browsers. Specific browser support matrix is not defined but typically excludes very old browsers like IE11 unless explicitly polyfilled.
7.  **Data Structure for Supabase:**
    *   Although not detailed in the frontend code, the backend (Supabase and API routes) will assume certain data structures for users, subscriptions, etc. The frontend might implicitly rely on these structures if it were to fetch or display such data.
